using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;

namespace Esky.FlightsCache.Database.Model;

[BsonIgnoreExtraElements]
public class Airport
{
    public required string Id { get; set; }
    [BsonElement("multiport")] public string Multiport { get; set; }
    [BsonElement("codes")] public List<string>? Codes { get; set; }
    [BsonElement("countryCode")] public string CountryCode { get; set; }
}