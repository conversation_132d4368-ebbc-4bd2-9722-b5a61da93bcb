using MongoDB.Bson.Serialization.Attributes;
using System.Linq;

namespace Esky.FlightsCache.Database.Model;

[BsonIgnoreExtraElements]
public record FlightOfferOneWay : FlightOfferBase
{
    [BsonIgnoreIfDefault]
    [BsonElement("IsOneWay")]
    public bool IsOneWay { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsOutbound")]
    public bool IsOutbound { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsInbound")]
    public bool IsInbound { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("MinStay")]
    public int MinStay { get; init; }

    [BsonElement("Airlines")] public string[] Airlines => OutboundLeg.Segments.Select(s => s.Airline).Distinct().OrderBy(x => x).ToArray();

    [BsonIgnore]
    public TripType TripType
    {
        get
        {
            var tripType = TripType.None;
            if (IsOneWay) tripType |= TripType.OneWay;
            if (IsOutbound) tripType |= TripType.Outbound;
            if (IsInbound) tripType |= TripType.Inbound;
            return tripType;
        }
        init
        {
            IsOneWay = value.HasFlag(TripType.OneWay);
            IsOutbound = value.HasFlag(TripType.Outbound);
            IsInbound = value.HasFlag(TripType.Inbound);
        }
    }
    
    [BsonIgnore]
    public static readonly FlightOfferOneWay Empty = new()
    {
        Provider = 0,
        ReadProvider = 0,
        DepartureDate = default,
        ArrivalDate = default,
        Route = null,
        OutboundLeg = null,
        AirlineGroup = null,
        PriceElements = null,
        Currency = null,
        RefPrice = 0,
        PriceHash = 0,
        Source = null,
        ModificationDate = default,
        RefreshDate = default,
        SendDate = default,
        ExpirationDate = default
    };
}