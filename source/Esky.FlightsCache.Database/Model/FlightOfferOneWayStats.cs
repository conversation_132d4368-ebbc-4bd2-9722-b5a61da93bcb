using MongoDB.Bson.Serialization.Attributes;

namespace Esky.FlightsCache.Database.Model;

[BsonIgnoreExtraElements]
public record FlightOfferOneWayStats : FlightOfferStats
{
    [BsonIgnoreIfDefault]
    [BsonElement("IsOneWay")]
    public bool IsOneWay { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsOutbound")]
    public bool IsOutbound { get; init; }

    [BsonIgnoreIfDefault]
    [BsonElement("IsInbound")]
    public bool IsInbound { get; init; }
    
    [BsonIgnoreIfDefault]
    [BsonElement("MinStay")]
    public int MinStay { get; init; }
}