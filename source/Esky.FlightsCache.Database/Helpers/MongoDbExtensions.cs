using MongoDB.Driver;
using System;

namespace Esky.FlightsCache.Database.Helpers;

public static class MongoDbExtensions
{
    public static IMongoCollection<T> EnsureIndex<T>(this IMongoCollection<T> collection, string indexName,
        Func<IndexKeysDefinitionBuilder<T>, IndexKeysDefinition<T>> indexDefinition)
    {
        collection.Indexes
            .CreateOneAsync(
                new CreateIndexModel<T>(
                    indexDefinition(new IndexKeysDefinitionBuilder<T>()),
                    new CreateIndexOptions { Name = indexName, Background = true })
            );
        return collection;
    }
}