using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.Database.Model;
using MongoDB.Driver;

namespace Esky.FlightsCache.Database;

public class DatabaseContext
{
    public IMongoCollection<Airport> AirportsCollection { get; }
    public IMongoCollection<FlightOfferOneWay> FlightOffersOneWayCollection { get; }
    public IMongoCollection<FlightOfferOneWayStats> FlightOffersOneWayStatsCollection { get; }
    public IMongoCollection<FlightOfferRoundTrip> FlightOffersRoundTripCollection { get; }
    public IMongoCollection<FlightOfferRoundTripStats> FlightOffersRoundTripStatsCollection { get; }

    public DatabaseContext(string connectionString)
    {
        var dbName = MongoUrl.Create(connectionString).DatabaseName;
        var database = new MongoClient(connectionString).GetDatabase(dbName);

        FlightOffersOneWayCollection = database
            .GetCollection<FlightOfferOneWay>("flightOffersOW")
            .WithReadPreference(new ReadPreference(ReadPreferenceMode.SecondaryPreferred));

        FlightOffersRoundTripCollection = database
            .GetCollection<FlightOfferRoundTrip>("flightOffersRT")
            .WithReadPreference(new ReadPreference(ReadPreferenceMode.SecondaryPreferred));
        
        AirportsCollection = database.GetCollection<Airport>("airports");

        FlightOffersOneWayStatsCollection = database
            .GetCollection<FlightOfferOneWayStats>("flightOffersOW.stats")
            .EnsureIndex("Route_Source", itx => itx.Ascending(x => x.Route.Id).Ascending(x => x.Source.Name));

        FlightOffersRoundTripStatsCollection = database
            .GetCollection<FlightOfferRoundTripStats>("flightOffersRT.stats")
            .EnsureIndex("Route_Source", itx => itx.Ascending(x => x.Route.Id).Ascending(x => x.Source.Name));
    }
}