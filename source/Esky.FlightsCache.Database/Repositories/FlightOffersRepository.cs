using Esky.FlightsCache.Database.Model;
using Esky.Framework.PartnerSettings.Enums;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Repositories;

public interface IFlightOffersRepository
{
    Task<long> RemoveByCacheProviderCode(int cacheProviderCode, bool removeOw, bool removeRt);
    Task<List<(string, DateTime)>> GetDirectRyanairLowAvailabilityItems(int availableSeatsCount, int daysForward);
    Task<(int CollectedDates, int CollectedFlights, int DatesWithMoreThan1Stop)> GetSourceStatsOw(string sourceName, string[] routes);
    Task<(int CollectedDates, int CollectedFlights, int DatesWithMoreThan1Stop)> GetSourceStatsRt(string sourceName, string[] routes);
    Task<ISet<DateTime>> GetDepartureDatesOW(int provider, string supplier, string route);
}

public class FlightOffersRepository(DatabaseContext databaseContext) : IFlightOffersRepository
{
    public Task<long> RemoveByCacheProviderCode(int cacheProviderCode, bool removeOw, bool removeRt)
    {
        var tasks = new List<Task<DeleteResult>>();

        if (removeOw)
        {
            tasks.Add(databaseContext.FlightOffersOneWayCollection.DeleteManyAsync(s => s.Provider == cacheProviderCode));
            tasks.Add(databaseContext.FlightOffersOneWayStatsCollection.DeleteManyAsync(s => s.Provider == cacheProviderCode));
        }

        if (removeRt)
        {
            tasks.Add(databaseContext.FlightOffersRoundTripCollection.DeleteManyAsync(s => s.Provider == cacheProviderCode));
            tasks.Add(databaseContext.FlightOffersRoundTripStatsCollection.DeleteManyAsync(s => s.Provider == cacheProviderCode));
        }

        return Task.WhenAll(tasks).ContinueWith(t => t.Result.Sum(r => r.DeletedCount));
    }
    
    public async Task<List<(string, DateTime)>> GetDirectRyanairLowAvailabilityItems(int availableSeatsCount, int daysForward)
    {
        var pipeline = new EmptyPipelineDefinition<FlightOfferOneWay>()
            .Match(x => 
                x.Provider == (int)ProviderCodeEnum.DirectRyanair
                && x.IsOneWay == true
                && x.DepartureDay <= DateTime.Today.AddDays(daysForward)
                && x.AvailableSeatsCount <= availableSeatsCount)
            .Group(x => new { RouteId = x.Route.Id, x.DepartureDay }, g => new { g.Key.RouteId, g.Key.DepartureDay });

        var result = await databaseContext.FlightOffersOneWayCollection.Aggregate(pipeline).ToListAsync();

        return result.Select(x => (x.RouteId, x.DepartureDay)).ToList();
    }
    
    public async Task<(int CollectedDates, int CollectedFlights, int DatesWithMoreThan1Stop)> GetSourceStatsOw(string sourceName, string[] routes)
    {
        var pipeline = new[]
        {
            new BsonDocument("$match",
                new BsonDocument
                {
                    { "Route._id", new BsonDocument("$in", new BsonArray(routes)) }, { "Source.Name", sourceName }, {"ExpirationDate", new BsonDocument("$gt", DateTime.UtcNow) }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", new BsonDocument { { "day", "$DepartureDay" }, { "stops", "$MinStops" } } },
                    { "count", new BsonDocument("$sum", 1) }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", new BsonDocument { { "day", "$_id.day" } } },
                    { "minStops", new BsonDocument("$min", "$_id.stops") },
                    { "count", new BsonDocument("$sum", "$count") }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", BsonNull.Value },
                    { "totalFlights", new BsonDocument("$sum", "$count") },
                    { "uniqueDDCount", new BsonDocument("$sum", 1) },
                    { "countMinStopsGreaterThanOne", new BsonDocument("$sum", new BsonDocument("$cond", new BsonArray { new BsonDocument("$gt", new BsonArray { "$minStops", 1 }), 1, 0 })) }
                }),
            new BsonDocument("$project", new BsonDocument { { "_id", 0 }, { "uniqueDDCount", 1 }, { "totalFlights", 1 }, { "countMinStopsGreaterThanOne", 1 } })
        };

        var result = await databaseContext.FlightOffersOneWayStatsCollection.Aggregate<BsonDocument>(pipeline).FirstOrDefaultAsync();

        return result is null
            ? (0, 0, 0)
            : (result.GetValue("uniqueDDCount").AsInt32, result.GetValue("totalFlights").AsInt32, result.GetValue("countMinStopsGreaterThanOne").AsInt32);
    }

    public async Task<(int CollectedDates, int CollectedFlights, int DatesWithMoreThan1Stop)> GetSourceStatsRt(string sourceName, string[] routes)
    {
        var pipeline = new[]
        {
            new BsonDocument("$match",
                new BsonDocument
                {
                    { "Route._id", new BsonDocument("$in", new BsonArray(routes)) }, { "Source.Name", sourceName }, {"ExpirationDate", new BsonDocument("$gt", DateTime.UtcNow) }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", new BsonDocument { { "day", "$DepartureDay" }, { "stay", "$StayLength.DepartureToDeparture" }, { "stops", "$MinStops" } } },
                    { "count", new BsonDocument("$sum", 1) }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", new BsonDocument { { "day", "$_id.day" }, { "stay", "$_id.stay" } } },
                    { "minStops", new BsonDocument("$min", "$_id.stops") },
                    { "count", new BsonDocument("$sum", "$count") }
                }),
            new BsonDocument("$group",
                new BsonDocument
                {
                    { "_id", BsonNull.Value },
                    { "totalFlightsSum", new BsonDocument("$sum", "$count") },
                    { "uniquePairsCount", new BsonDocument("$sum", 1) },
                    { "countMinStopsGreaterThanOne", new BsonDocument("$sum", new BsonDocument("$cond", new BsonArray { new BsonDocument("$gt", new BsonArray { "$minStops", 1 }), 1, 0 })) }
                }),
            new BsonDocument("$project", new BsonDocument { { "_id", 0 }, { "uniquePairsCount", 1 }, { "totalFlightsSum", 1 }, { "countMinStopsGreaterThanOne", 1 } })
        };

        var result = await databaseContext.FlightOffersRoundTripStatsCollection.Aggregate<BsonDocument>(pipeline).FirstOrDefaultAsync();

        return result is null
            ? (0, 0, 0)
            : (result.GetValue("uniquePairsCount").AsInt32, result.GetValue("totalFlightsSum").AsInt32, result.GetValue("countMinStopsGreaterThanOne").AsInt32);
    }
    
    public async Task<ISet<DateTime>> GetDepartureDatesOW(int provider, string supplier, string route)
    {
        return (await databaseContext.FlightOffersOneWayStatsCollection
                .Find(x =>
                    x.Provider == provider &&
                    (string.IsNullOrEmpty(supplier) || x.Supplier == supplier) &&
                    x.Route.Id == route &&
                    x.ExpirationDate > DateTime.UtcNow)
                .Project(x => x.DepartureDay)
                .ToListAsync())
            .ToHashSet();
    }
}