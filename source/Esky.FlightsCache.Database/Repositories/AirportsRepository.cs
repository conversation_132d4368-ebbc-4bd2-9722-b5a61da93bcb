using Esky.FlightsCache.Database.Model;
using Microsoft.Extensions.Caching.Memory;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Database.Repositories;

public interface IAirportsRepository
{
    Task<IEnumerable<string>> GetAirportCodesAsync(string airport);
    Task<string> GetCountryAsync(string airport);
    Task<bool> IsMultiportAsync(string airport);
}

public class AirportsRepository(DatabaseContext dbContext, IMemoryCache memoryCache) : IAirportsRepository
{
    private readonly IMongoCollection<Airport> _airportsCollection = dbContext.AirportsCollection;
    private const int _cacheExpirationHours = 24;

    public async Task<IEnumerable<string>> GetAirportCodesAsync(string airport)
    {
        var airports = await GetAirportsAsync();
        return airports.Where(a => a.Codes is not null && a.Codes.Contains(airport)).Select(a => a.Id);
    }

    public async Task<string> GetCountryAsync(string airport)
    {
        var airports = await GetAirportsAsync();
        return airports.FirstOrDefault(a => a.Id == airport)?.CountryCode;
    }

    public async Task<bool> IsMultiportAsync(string airport)
    {
        var airports = await GetAirportsAsync();
        return airports.Any(x => x.Id != airport && x.Multiport == airport);
    }
    
    private async Task<ISet<Airport>> GetAirportsAsync()
    {
        return await memoryCache.GetOrCreate($"{nameof(AirportsRepository)}", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(_cacheExpirationHours);
            return (await _airportsCollection.AsQueryable().ToListAsync()).ToHashSet();
        });
    }
}