using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping;

public interface IProviderConfigurationValidator
{
    void Validate(CacheProviderConfiguration providerConfiguration);
    void Validate(ProviderMarginConfiguration providerConfiguration);
}

public class ProviderConfigurationValidator : IProviderConfigurationValidator
{
    public void Validate(CacheProviderConfiguration providerConfiguration)
    {
        ValidateProviderCode(providerConfiguration.CacheProviderCode, nameof(providerConfiguration.CacheProviderCode));
        ValidateProviderCode(providerConfiguration.ReadProviderCode, nameof(providerConfiguration.ReadProviderCode));

        ValidateWriteConfiguration(providerConfiguration.WriteConfigurations);
    }

    public void Validate(ProviderMarginConfiguration providerConfiguration)
    {
        ValidateProviderCode(providerConfiguration.ProviderCode, nameof(providerConfiguration.ProviderCode));
        ValidateMarginConfiguration(providerConfiguration);

        foreach (var marginConfiguration in providerConfiguration.Margins.Select(c => c.Margin))
        {
            ValidateMargin(marginConfiguration);
        }
    }

    private static void ValidateProviderCode(int providerCode, string name)
    {
        // CachProviderCode == -1: Skip save to cache
        if (providerCode < 1 && providerCode != -1)
            throw new ValidationException($"Invalid {name}");

        if (providerCode > 255)
            throw new ValidationException($"Invalid {name}, must be <256 (probably there is no available code to assign)");
    }

    internal static void ValidateWriteConfiguration(CacheProviderConfiguration.WriteConfiguration[] writeConfiguration)
    {
        if (writeConfiguration.Length == 0)
            throw new ValidationException("Missing WriteProviders");

        if (writeConfiguration.Any(w => w.AirlineCodes.Length == 0))
            throw new ValidationException("At least one airline code must be specified");

        if (writeConfiguration.Any(w =>
                w.AirlineCodes.Any(ac => string.IsNullOrWhiteSpace(ac) || ac.Length != 2)))
            throw new ValidationException("Airline codes must contain 2 characters");

        foreach (var w in writeConfiguration.Select(w => w.ProviderCode))
            ValidateProviderCode(w, nameof(CacheProviderConfiguration.WriteConfiguration.ProviderCode));

        if (writeConfiguration.Select(p => p.ProviderCode).Distinct().Count() !=
            writeConfiguration.Length)
            throw new ValidationException("Duplicate write provider code");
    }

    internal static void ValidateTechnicalMarginConfiguration(TechnicalMarginConfiguration config)
    {
        if (string.IsNullOrWhiteSpace(config.Supplier))
            throw new ValidationException("Supplier must be specified (all suppliers represented by '*')");
        
        if (config.AirlineCodes.Length == 0)
            throw new ValidationException("At least one airline code must be specified (all airline codes represented by '*')");
        
        ValidateMargin(config.Margin);
    }
    
    internal static void ValidateMarginConfiguration(ProviderMarginConfiguration config)
    {
        if (!config.Margins.Any())
            throw new ValidationException("Missing Margins");
        
        if (config.Margins.Any(c => string.IsNullOrWhiteSpace(c.Supplier)))
            throw new ValidationException("Supplier must be specified (all suppliers represented by '*')");
        
        if (config.Margins.Any(c => c.AirlineCodes.Length == 0))
            throw new ValidationException("At least one airline code must be specified (all airline codes represented by '*')");
        
        if (config.Margins.Any(c =>
                c.AirlineCodes.Any(ac => string.IsNullOrWhiteSpace(ac) || (ac.Length != 2 && ac != "*"))))
            throw new ValidationException("Airline codes must contain 2 characters or '*' for all");

        var duplicatedSupplierWithAirline = config.Margins
            .SelectMany(c => 
                c.AirlineCodes.Select(a => $"{c.Supplier}.{a}"))
            .GroupBy(c => c, (code, group) => (Code: code, Count: group.Count()))
            .Where(g => g.Count > 1)
            .Select(c => c.Code)
            .ToArray();
        if (duplicatedSupplierWithAirline.Length != 0)
            throw new ValidationException($"Supplier with airline defined more than once: {string.Join(", ", duplicatedSupplierWithAirline)}");
    }
    
    internal static void ValidateMargin(MarginConfiguration margin)
    {
        if (margin is { MarginType: MarginType.Absolute, Amount: not 0 } &&
            margin.Currency?.Length != 3)
            throw new ValidationException("Margin currency is missing or longer then 3 characters");

        if (margin.MarginType == MarginType.Relative &&
            !string.IsNullOrEmpty(margin.Currency))
            throw new ValidationException("Currency not allowed for relative margin!");

        if (margin is { MarginType: MarginType.Relative, Amount: <= -100 or > 100 })
            throw new ValidationException("Amount value out of range!");
    }
}