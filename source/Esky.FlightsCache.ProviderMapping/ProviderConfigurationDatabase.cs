using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.ProviderMapping;

public interface IProviderConfigurationDatabase
{
    Task<List<CacheProviderConfiguration>> GetMappingList();
    Task<List<ProviderMarginConfiguration>> GetMarginList();
    Task<List<TechnicalMarginConfiguration>> GetTechnicalMarginList();
    Task<TechnicalMarginConfiguration> GetTechnicalMargin(string id);
    Task AddOrUpdate(int cacheProviderCode, CacheProviderConfiguration providerConfiguration);
    Task AddOrUpdate(int cacheProviderCode, ProviderMarginConfiguration providerConfiguration);
    Task AddOrUpdate(string? id, TechnicalMarginConfiguration configuration);
    Task DeleteMapping(int cacheProviderCode);
    Task DeleteMargin(int cacheProviderCode);
    Task DeleteTechnicalMargin(string id);
}

public class ProviderConfigurationDatabase : IProviderConfigurationDatabase
{
    private readonly IMongoCollection<CacheProviderConfiguration> _providerConfiguration;
    private readonly IMongoCollection<ProviderMarginConfiguration> _providerMarginConfiguration;
    private readonly IMongoCollection<TechnicalMarginConfiguration> _technicalMarginConfiguration;

    public ProviderConfigurationDatabase(Settings settings)
    {
        var url = MongoUrl.Create(settings.ConnectionUrl);
        var db = new MongoClient(url).GetDatabase(url.DatabaseName);
        _providerConfiguration = db.GetCollection<CacheProviderConfiguration>("providerConfiguration");
        _providerMarginConfiguration = db.GetCollection<ProviderMarginConfiguration>("providerMargins");
        _technicalMarginConfiguration = db.GetCollection<TechnicalMarginConfiguration>("technicalMargin");
    }

    public async Task AddOrUpdate(int cacheProviderCode, CacheProviderConfiguration providerConfiguration)
    {
        await _providerConfiguration.ReplaceOneAsync(
            x => x.CacheProviderCode == cacheProviderCode,
            providerConfiguration,
            new ReplaceOptions { IsUpsert = true }
        );
    }

    public async Task AddOrUpdate(int cacheProviderCode, ProviderMarginConfiguration providerConfiguration)
    {
        await _providerMarginConfiguration.ReplaceOneAsync(
            x => x.ProviderCode == cacheProviderCode,
            providerConfiguration,
            new ReplaceOptions { IsUpsert = true }
        );
    }

    public async Task AddOrUpdate(string? id, TechnicalMarginConfiguration configuration)
    {
        await CheckForDuplicateTechnicalMargin(id, configuration);
        ProviderConfigurationValidator.ValidateTechnicalMarginConfiguration(configuration);

        var objectId = id == null ? ObjectId.GenerateNewId() : ObjectId.Parse(id);
        configuration.Id = objectId;
        await _technicalMarginConfiguration.ReplaceOneAsync(
            x => x.Id == objectId,
            configuration,
            new ReplaceOptions { IsUpsert = true }
        );
    }

    private async Task CheckForDuplicateTechnicalMargin(string id, TechnicalMarginConfiguration configuration)
    {
        if (id != null)
        {
            return;
        }

        var existing = await _technicalMarginConfiguration
            .AsQueryable()
            .FirstOrDefaultAsync(x => x.Supplier == configuration.Supplier &&
                                      x.ProviderCode == configuration.ProviderCode &&
                                      x.AirlineCodes == configuration.AirlineCodes &&
                                      x.Passenger == configuration.Passenger &&
                                      x.Context == configuration.Context &&
                                      x.FromNumberOfPassengers == configuration.FromNumberOfPassengers);

        if (existing != null)
        {
            throw new ValidationException(
                "Duplicate technical margin configuration - there already is a configuration for this supplier, provider, airlines, passenger, context and number of passengers");
        }
    }

    public async Task DeleteMapping(int cacheProviderCode)
    {
        var result = await _providerConfiguration.DeleteOneAsync(x => x.CacheProviderCode == cacheProviderCode
        );

        if (result.DeletedCount == 0)
            throw new KeyNotFoundException("CacheProviderCode not found");
    }

    public async Task DeleteMargin(int cacheProviderCode)
    {
        var result = await _providerMarginConfiguration.DeleteOneAsync(x => x.ProviderCode == cacheProviderCode
        );

        if (result.DeletedCount == 0)
            throw new KeyNotFoundException("CacheProviderCode not found");
    }

    public async Task DeleteTechnicalMargin(string id)
    {
        var objectId = ObjectId.Parse(id);
        var result = await _technicalMarginConfiguration.DeleteOneAsync(x => x.Id == objectId
        );

        if (result.DeletedCount == 0)
            throw new KeyNotFoundException("Id not found");
    }

    public Task<List<CacheProviderConfiguration>> GetMappingList()
    {
        return _providerConfiguration.AsQueryable().ToListAsync();
    }

    public Task<List<ProviderMarginConfiguration>> GetMarginList()
    {
        return _providerMarginConfiguration.AsQueryable().ToListAsync();
    }

    public Task<List<TechnicalMarginConfiguration>> GetTechnicalMarginList()
    {
        return _technicalMarginConfiguration.AsQueryable().ToListAsync();
    }

    public Task<TechnicalMarginConfiguration> GetTechnicalMargin(string id)
    {
        var objectId = ObjectId.Parse(id);
        return _technicalMarginConfiguration.AsQueryable()
            .FirstOrDefaultAsync(x => x.Id == objectId, CancellationToken.None);
    }

    public class Settings
    {
        public string ConnectionUrl { get; set; }
    }
}