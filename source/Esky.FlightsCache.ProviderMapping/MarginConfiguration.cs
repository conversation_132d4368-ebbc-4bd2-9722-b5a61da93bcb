using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Esky.FlightsCache.ProviderMapping;

public class MarginConfiguration
{
    public decimal Amount { get; set; }
    public string Currency { get; set; }

    public static MarginConfiguration ZeroMargin => new MarginConfiguration();

    [BsonRepresentation(BsonType.String)]
    public MarginType MarginType { get; set; } = MarginType.Absolute;
}

[JsonConverter(typeof(StringEnumConverter))]
public enum MarginType
{
    Absolute = 0,
    Relative = 1
}