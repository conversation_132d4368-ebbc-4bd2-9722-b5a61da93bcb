#nullable enable
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Linq;

namespace Esky.FlightsCache.ProviderMapping;

public class TechnicalMarginConfiguration
{
    private string[] _airlineCodes = [];
    public ObjectId Id { get; set; }
    public required int? ProviderCode { get; set; } = null; // null = all providers
    public required string Supplier { get; set; }

    public required string[] AirlineCodes
    {
        get => _airlineCodes;
        set => _airlineCodes = value.Select(c => c.ToUpper()).OrderBy(c => c).ToArray();
    }

    public int FromNumberOfPassengers { get; set; } = 1;
    [BsonRepresentation(BsonType.String), JsonConverter(typeof(StringEnumConverter))]
    public PassengerType Passenger { get; set; } = PassengerType.All;

    [BsonRepresentation(BsonType.String), JsonConverter(typeof(StringEnumConverter))]
    public required MarginContext Context { get; set; } // Base or Packages
    public required MarginConfiguration Margin { get; set; }
}

public enum PassengerType
{
    All,
    Adult,
    Youth,
    Child,
    Infant,
}

public enum MarginContext
{
    Base = 1,
    Packages = 2
}