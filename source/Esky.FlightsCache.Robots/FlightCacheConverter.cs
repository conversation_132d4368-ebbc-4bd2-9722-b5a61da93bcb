using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using SeparationOptionEnum = Esky.FlightsCache.MessageContract.SeparationOptionEnum;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class FlightCacheConverter : IFlightCacheConverter
    {
        private readonly ICurrencyRatioProvider _currencyRatioProvider;
        private readonly IPriceCacheBuilder _priceCacheBuilder;
        private readonly ISeparationOptionsResolver _separationOptionsResolver;

        public FlightCacheConverter(ICurrencyRatioProvider ratioProvider, IPriceCacheBuilder priceCacheBuilder, ISeparationOptionsResolver separationOptionsResolver)
        {
            _currencyRatioProvider = ratioProvider;
            _priceCacheBuilder = priceCacheBuilder;
            _separationOptionsResolver = separationOptionsResolver;
        }

        public List<FlightCache> Convert(IEnumerable<FlightDto> flights,
            QueueElement queueElement,
            CacheTimeOutConfiguration timeOutConfiguration,
            SearchFlightsProviderQuery query)
        {
            var resList = flights.Select(flight =>
            {
                var providerCode = queueElement.ProviderCode;
                var expirationHours = timeOutConfiguration.GetTimeOutInHoursForProvider((int)(providerCode ?? 0), flight.Legs.First().Segments.First().DepartureLocalTime);
                var startExpirationDate = flight.Legs.Min(x => x.DataTimestamp) ?? DateTime.UtcNow;
                var expirationDate = startExpirationDate.AddHours(expirationHours);

                var legs = flight.Legs.Select((leg, i) =>
                    new FlightCacheLeg
                    {
                        ArrivalCode = leg.Segments.Last().DestinationAirport,
                        ArrivalDate = leg.Segments.Last().ArrivalLocalTime,
                        DepartureCode = leg.Segments.First().OriginAirport,
                        DepartureDate = leg.Segments.First().DepartureLocalTime,
                        AirlineCode = leg.Segments.First().AirlineCode,
                        CurrencyCode = flight.Currency,
                        ConversionRatioToReferenceCurrency = _currencyRatioProvider.GetRatio(flight.Currency, "EUR"),
                        DepartureSearchCodes = [leg.Segments.First().OriginAirport],
                        ArrivalSearchCodes = [leg.Segments.Last().DestinationAirport],
                        DepartureAirportDetails = new AirportDetails(),
                        ArrivalAirportDetails = new AirportDetails(),
                        SeparationOptions = _separationOptionsResolver.Resolve(leg, flight.Legs.Count == 2),
                        AvailableSeatsCount = leg.Segments.Min(x=>x.SeatsRemaining),
                        TotalFaresLeft = leg.TotalFaresLeft,
                        Segments = leg.Segments.Select(s => new FlightCacheSegment
                        {
                            ArrivalCode = s.DestinationAirport,
                            ArrivalDate = s.ArrivalLocalTime,
                            DepartureCode = s.OriginAirport,
                            DepartureDate = s.DepartureLocalTime,
                            AirlineCode = s.AirlineCode,
                            FlightNumber = s.FlightNumber,
                            OperatingAirlineCode = s.OperatedBy,
                            OperatingFlightNumber = s.OperatedByFlightNumber,
                            BookingClass = s.BookingClass,
                            FareDetails = new FareDetails
                            {
                                FareCode = s.FareDetails?.FareCode,
                                OfficeId = s.FareDetails?.OfficeId,
                                OfferId = s.FareDetails?.OfferId,
                                OfferType = (MessageContract.OfferType?)s.FareDetails?.OfferType ?? MessageContract.OfferType.Regular,
                                LastTicketDate = s.FareDetails?.LastTicketDate
                            },
                            IsBaggageIncludedInPrice = s.Baggage is AvailabilityEnum.Included,
                            AircraftCode = s.AircraftCode,
                            Stopovers = s.Stopovers
                                ?.Select(stop => new FlightCacheSegmentStopover
                                {
                                    AirportCode = stop.AirportCode,
                                    DepartureDate = stop.DepartureDate,
                                    ArrivalDate = stop.ArrivalDate,
                                })
                                .ToList()
                        }).ToList(),
                        AdultPrices = LegPrices(query, flight, PassengerTypeEnum.ADT, legIndex: i),
                        ChildPrices = LegPrices(query, flight, PassengerTypeEnum.CHD, legIndex: i),
                        InfantPrices = LegPrices(query, flight, PassengerTypeEnum.INF, legIndex: i),
                        DataTimestamp = leg.DataTimestamp
                    }).ToList();

                return new FlightCache
                {
                    ExpirationDate = expirationDate,
                    LegsCanBeUseSeparately = LegsCanBeUsedSeparately(legs),
                    ProviderCode = (int)(providerCode ?? 0),
                    ValidatingCarrier = flight.Legs.First().Segments.First().AirlineCode,
                    Supplier = flight.Supplier,
                    Legs = legs
                };
            }).ToList();

            return resList;

            static bool LegsCanBeUsedSeparately(List<FlightCacheLeg> flightLegs) =>
                flightLegs.Count != 1 
                && flightLegs.All(l => l.SeparationOptions.Options != SeparationOptionEnum.None);
        }

        private List<PriceCacheEntry>? LegPrices(SearchFlightsProviderQuery query, FlightDto flightDto, PassengerTypeEnum passengerType, int legIndex)
        {
            if (!query.Passengers.Any(p => p.Code == passengerType && p.Count > 0))
            {
                return null;
            }

            var result = _priceCacheBuilder.Build(query, flightDto, passengerType, legIndex);

            if (result.BasePrice == 0 && result.TaxPrice == 0)
            {
                return null;
            }

            return [result];
        }
    }
}