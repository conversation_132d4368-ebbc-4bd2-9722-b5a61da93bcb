using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class CacheRequestBuilder : ICacheRequestBuilder
    {
        public CacheRequest Build(List<FlightCache> flights, SearchFlightsProviderQuery query, QueueElement queueElement, RequestBuildSettings requestBuildSettings)
        {
            var deleteReturnDepartureDayFrom = queueElement.DeleteReturnDepartureDayFrom;
            var deleteReturnDepartureDayTo = queueElement.DeleteReturnDepartureDayTo;

            if (deleteReturnDepartureDayFrom is null
                && deleteReturnDepartureDayTo is null
                && queueElement.IsRoundTrip
                && query.Legs.Count() == 2
                && flights.TrueForAll(f => f.LegsCanBeUseSeparately))
            {
                deleteReturnDepartureDayFrom = deleteReturnDepartureDayTo = query.Legs.Last().DepartureDate;
            }

            var sourceName = queueElement.SourceName ?? $"{queueElement.ProviderCode}Consumer";
            if (flights.Count == 0) sourceName += "_Empty";

            return new CacheRequest
            {
                SourceDescription = new SourceDescription
                {
                    Name = sourceName,
                    SearchDepartureCode = queueElement.DepartureCode,
                    SearchArrivalCode = queueElement.ArrivalCode,
                    SearchDepartureDate = queueElement.DepartureDay,
                    SearchReturnDepartureDate = queueElement.ReturnDepartureDay,
                    SendDate = DateTime.UtcNow,
                    MachineName = Environment.MachineName,
                    Provider = ((int)(queueElement.ProviderCode ?? 0)).ToString(),
                    ProviderCode = (int?)queueElement.ProviderCode,
                    Supplier = queueElement.Supplier,
                    PartnerCode = queueElement.PartnerCode,
                    PaxConfiguration = (query.Passengers.FirstOrDefault(x => x.Code == PassengerTypeEnum.ADT)?.Count ?? 0) + "." +
                                        (query.Passengers.FirstOrDefault(x => x.Code == PassengerTypeEnum.YTH)?.Count ?? 0) + "." +
                                        (query.Passengers.FirstOrDefault(x => x.Code == PassengerTypeEnum.CHD)?.Count ?? 0) + "." +
                                        (query.Passengers.FirstOrDefault(x => x.Code == PassengerTypeEnum.INF)?.Count ?? 0),
                    SessionId = $"{flights.Count}",
                    Flex = queueElement.Flex
                },
                CommandOptions = new CommandDataOptions
                {
                    GroupName = Guid.NewGuid().ToString(),
                    SkipDataFiltering = false,
                    DeleteOptions = new DeleteOptions
                    {
                        AirlineCodes = flights.Select(f => f.Legs.First().AirlineCode).ToList(),
                        ProviderCodes = flights.Count == 0 && queueElement.ProviderCode is not null ? [(int)queueElement.ProviderCode] : null,
                        DeleteDepartureDayFrom = queueElement.DeleteDepartureDayFrom,
                        DeleteDepartureDayTo = queueElement.DeleteDepartureDayTo,
                        DeleteReturnDepartureDayFrom = deleteReturnDepartureDayFrom,
                        DeleteReturnDepartureDayTo = deleteReturnDepartureDayTo,
                        IsEnabled = true
                    },
                    SkipSQLSave = requestBuildSettings.SkipSQLSave
                },
                Flights = flights,

            };
        }
    }
}