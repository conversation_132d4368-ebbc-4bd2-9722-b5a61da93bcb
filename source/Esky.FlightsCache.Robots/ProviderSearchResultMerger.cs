using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;
using SeparationOptionEnum = Esky.Flights.Integration.Providers.Contract.SeparationOptionEnum;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class ProviderSearchResultMerger : IProviderSearchResultMerger
    {
        private readonly int _minStayHours = 4;

        private readonly IPriceCacheBuilder _priceCacheBuilder;

        public ProviderSearchResultMerger(IPriceCacheBuilder priceCacheBuilder)
        {
            _priceCacheBuilder = priceCacheBuilder;
        }

        public IEnumerable<FlightDto> Map(SearchFlightsProviderQuery query, IEnumerable<FlightDto> flights)
        {
            var result = flights.ToLookup(o => o.TripType, o => Map(o, query)).ToDictionary(o => o.Key, o => o.ToList());

            if (query.TripType == TripType.RT)
            {
                foreach (var group in flights
                    .Where(x => !x.IsAdditionalFlight && x.TripType.IsOneWay(out _))
                    .GroupBy(x => new { x.Supplier, x.Currency }))
                {
                    var owFlights = group.ToLookup(o => o.TripType, o => Map(o, query));

                    if (owFlights.Count == 0)
                    {
                        continue;
                    }

                    if (!result.ContainsKey(TripType.RT))
                    {
                        result[TripType.RT] = [];
                    }

                    var mixedFlights = Merge(result[TripType.OWO], result[TripType.OWI]);

                    result[TripType.RT].AddRange(mixedFlights);

                    if (result.TryGetValue(TripType.OWO, out var value)) value.RemoveAll(o => o.Flight?.Legs.First().SeparationOptions?.Options == SeparationOptionEnum.RoundTripOutbound);
                    if (result.TryGetValue(TripType.OWI, out value)) value.RemoveAll(o => o.Flight?.Legs.First().SeparationOptions?.Options == SeparationOptionEnum.RoundTripInbound);
                }
            }

            return result.Select(o => o.Value.Select(p => p.Flight)).Aggregate(new List<FlightDto>(), (o, p) => o.Concat(p).ToList());
        }

        private PreFlightCache Map(FlightDto model, SearchFlightsProviderQuery query)
        {
            return new PreFlightCache(model, query, _priceCacheBuilder);
        }

        private IEnumerable<PreFlightCache> Merge(IReadOnlyList<PreFlightCache> outboundFlights, IReadOnlyList<PreFlightCache> inboundFlights)
        {
            if (!outboundFlights.Any() || !inboundFlights.Any())
                return [];

            return
                from outbound in Filter(outboundFlights)
                from inbound in Filter(inboundFlights)
                where CanBeMerge(outbound, inbound)
                select CreateRoundTripFlight(outbound, inbound);
        }

        private static IEnumerable<PreFlightCache> Filter(IReadOnlyList<PreFlightCache> flights)
        {
            var directFlights = flights.ToLookup(f => f.Flight.Legs[0].Segments.Count == 1);

            if (directFlights[false].Count() <= 64)
                return flights;

            var cheapestFlights = directFlights[false]
                .OrderBy(f => f.TotalPrice)
                .ThenBy(f => f.FlightTime)
                .Take(64);

            var fastestFlights = directFlights[false]
                .OrderBy(f => f.FlightTime)
                .ThenBy(f => f.TotalPrice)
                .Take(64);

            return directFlights[true].Concat(cheapestFlights.Union(fastestFlights));
        }

        private bool CanBeMerge(PreFlightCache outbound, PreFlightCache inbound)
        {
            return outbound.Flight.Legs[0].Segments.Last().ArrivalLocalTime.AddHours(_minStayHours) < inbound.Flight.Legs[0].Segments.First().DepartureLocalTime;
        }

        private static PreFlightCache CreateRoundTripFlight(PreFlightCache outbound, PreFlightCache inbound)
        {
            return new PreFlightCache(
                new FlightDto
                {
                    Currency = outbound.Flight.Currency,
                    Legs = [outbound.Flight.Legs.First(), inbound.Flight.Legs.First()],
                    Prices = MergePrices(outbound.Flight.Prices, inbound.Flight.Prices)
                },
                new PriceCacheEntry(
                    outbound.Price.BasePrice + inbound.Price.BasePrice,
                    outbound.Price.TaxPrice + inbound.Price.TaxPrice,
                    outbound.Price.MinimumNumberOfPaxes < inbound.Price.MinimumNumberOfPaxes
                        ? inbound.Price.MinimumNumberOfPaxes
                        : outbound.Price.MinimumNumberOfPaxes
                )
            );
        }

        private static List<PriceDto> MergePrices(params List<PriceDto>[] prices)
        {
            var result = new List<PriceDto>();

            foreach (var priceList in prices)
            {
                foreach (var p in priceList)
                {
                    var m = result.FirstOrDefault(o => o.PriceType == p.PriceType && o.PassengerType == p.PassengerType);

                    if (m == null)
                    {
                        m = new PriceDto { PriceType = p.PriceType, PassengerType = p.PassengerType };
                        result.Add(m);
                    }

                    m.Amount += p.Amount;
                }
            }

            return result;
        }
    }

    internal class PreFlightCache
    {
        public FlightDto Flight { get; set; }

        public TimeSpan FlightTime => TimeSpan.FromMinutes(Flight.Legs[0].Duration ?? 0);
        public PriceCacheEntry Price { get; set; }
        public decimal TotalPrice => Price.BasePrice + Price.TaxPrice;
        public string DepartureCode => Flight.Legs[0].Segments.First().OriginAirport;
        public string ArrivalCode => Flight.Legs[0].Segments.Last().DestinationAirport;

        public PreFlightCache(FlightDto flight, PriceCacheEntry price)
        {
            Flight = flight;
            Price = price;
        }

        public PreFlightCache(FlightDto flight, SearchFlightsProviderQuery query, IPriceCacheBuilder priceCacheBuilder)
        {
            Flight = flight;
            Price = priceCacheBuilder.Build(query, flight, PassengerTypeEnum.ADT, legIndex: 0);
        }
    }
}
