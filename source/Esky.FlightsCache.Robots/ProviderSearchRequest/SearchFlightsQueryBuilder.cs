using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.ProviderSearchRequest
{
    public class SearchFlightsQueryBuilder : ISearchFlightsQueryBuilder
    {
        public SearchFlightsProviderQuery BuildSingle(QueueElement queueElement) => BuildAll(queueElement).First().Query;
        
        public IEnumerable<(SearchFlightsProviderQuery Query, bool IsRequired)> BuildAll(QueueElement queueElement)
        {
            SearchFlightsProviderQuery query;

            if (queueElement.IsRoundTrip && queueElement.ReturnDepartureDays?.Length > 0)
            {
                foreach (var returnDepartureDate in GetRequiredFirst(queueElement.ReturnDepartureDays))
                {
                    query = GetQuery(GetOutboundLeg(), GetInboundLeg(returnDepartureDate.Return));
                    yield return (query, returnDepartureDate.IsRequired);
                }
                yield break;
            }

            if (queueElement.IsRoundTrip && queueElement.ReturnDepartureDay.HasValue)
            {
                query = GetQuery(GetOutboundLeg(), GetInboundLeg(queueElement.ReturnDepartureDay.Value));
                yield return (query, true);
                yield break;
            }

            query = GetQuery(GetOutboundLeg());
            yield return (query, true);

            SearchFlightsProviderQuery GetQuery(params SearchFlightsProviderQuery.Leg[] legs) => new()
            {
                PartnerCode = queueElement.PartnerCode ?? throw new Exception($"Partner code not provided in {queueElement.GetType().Name}"),
                Flex = queueElement.Flex > 0 ? queueElement.Flex : null,
                AirlineFilters = string.IsNullOrEmpty(queueElement.AirlineCode)
                    ? null
                    : new SearchFlightsProviderQuery.Filters { IncludedAirlines = [queueElement.AirlineCode] },
                Passengers = CreatePassengers(queueElement.PaxConfiguration.ToDefaultPaxConfigurationIfNullOrEmpty()),
                Legs = legs,
                Currency = queueElement.Currency
            };

            SearchFlightsProviderQuery.Leg GetOutboundLeg() => new()
            {
                DepartureCode = queueElement.DepartureCode,
                ArrivalCode = queueElement.ArrivalCode,
                DepartureDate = queueElement.DepartureDay
            };

            SearchFlightsProviderQuery.Leg GetInboundLeg(DateTime returnDate) => new()
            {
                DepartureCode = queueElement.ArrivalCode,
                ArrivalCode = queueElement.DepartureCode,
                DepartureDate = returnDate
            };
        }

        private static List<SearchFlightsProviderQuery.Passenger> CreatePassengers(PaxConfiguration paxConfiguration)
        {
            return new List<SearchFlightsProviderQuery.Passenger>
            {
                new() { Code = PassengerTypeEnum.ADT, Count = paxConfiguration.Adult },
                new() { Code = PassengerTypeEnum.YTH, Count = paxConfiguration.Youth },
                new() { Code = PassengerTypeEnum.CHD, Count = paxConfiguration.Child },
                new() { Code = PassengerTypeEnum.INF, Count = paxConfiguration.Infant }
            };
        }

        private static IEnumerable<ReturnDepartureDate> GetRequiredFirst(IEnumerable<ReturnDepartureDate> dates) =>
            dates
                .GroupBy(d => d.IsRequired)
                .OrderByDescending(d => d.Key)
                .SelectMany(g => g);
    }
}