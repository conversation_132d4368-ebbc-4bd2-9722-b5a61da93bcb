<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AsyncKeyedLock" Version="7.1.4" />
    <PackageReference Include="Esky.Flights.Integration.Providers.Contract" Version="2.1.11" />
    <PackageReference Include="Esky.FlightsCache.MessageContract" Version="2.10.0" />
    <PackageReference Include="Google.Cloud.BigQuery.V2" Version="3.10.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.23.0" />
    <PackageReference Include="system.reflection.metadata" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Esky.FlightsCache.CurrencyProvider\Esky.FlightsCache.CurrencyProvider.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots.Messages\Esky.FlightsCache.Robots.Messages.csproj" />
  </ItemGroup>
  <ItemGroup>
	<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
		<_Parameter1>Esky.FlightsCache.Tests</_Parameter1>
	</AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <InternalsVisibleTo Include="Esky.FlightsCache.Robots.Integration.Tests" />
  </ItemGroup>

</Project>
