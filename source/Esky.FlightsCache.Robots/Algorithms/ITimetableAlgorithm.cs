using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Esky.FlightsCache.RobotsProducers.Algorithms
{
    public interface ITimetableAlgorithm
    {
        Task GenerateQueueElements(ActionBlock<IEnumerable<QueueElement>> queueElementProcessingStream, string partnerCode, string airlineCode, int numberOfDepartureDaysForward, ProviderCodeEnum provider);
    }
}