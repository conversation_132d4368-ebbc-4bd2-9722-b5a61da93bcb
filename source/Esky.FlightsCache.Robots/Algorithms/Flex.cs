using Esky.FlightsCache.RobotsProducers.Algorithms;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Robots.Algorithms;

public static class Flex
{
    public static IEnumerable<DateTime> ApplyFlex(this IEnumerable<DateTime> self, int flex)
    {
        return flex > 0 ? Flex() : self;

        IEnumerable<DateTime> Flex()
        {
            var ordered = self.Order().ToArray();

            DateTime lastReturnedDate = default;
            foreach (var departureDate in ordered)
            {
                if (departureDate <= lastReturnedDate.AddDays(flex))
                {
                    continue;
                }
                lastReturnedDate = departureDate.AddDays(flex);
                yield return lastReturnedDate;
            }
        }
    }

    public static IEnumerable<Route> ApplyFlex(
        this IEnumerable<Route> self,
        int flex,
        DateTime startDay,
        DateTime endDay)
    {
        return self.Select(route => route.ApplyFlex(flex, startDay, endDay));
    }

    public static Route ApplyFlex(
        this Route self,
        int flex,
        DateTime startDay,
        DateTime endDay)
    {
        return self with
        {
            FlyingDates = self
                .FlyingDates
                .Where(e => startDay <= e && e <= endDay)
                .ApplyFlex(flex)
                .ToArray()
        };
    }
}