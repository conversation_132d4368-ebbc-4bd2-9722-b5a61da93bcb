using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.Algorithms
{
    public interface IDayByDayAlgorithm
    {
        Task<(IEnumerable<TravelFusionQueueElement>, int count)> GenerateTravelFusionElements(
            IEnumerable<string> suppliers,
            IEnumerable<string> exceptSuppliers,
            string partnerCode,
            DateTime startDate,
            DateTime endDate,
            TimeSpan minAge,
            string sourceName,
            string paxConfiguration = PaxConfigurations.Default);

        Task<IEnumerable<QueueElement>> Generate(
            string partnerCode,
            ProviderCodeEnum provider,
            DateTime startDate,
            DateTime endDate,
            IEnumerable<string> suppliers);
    }
}