using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Esky.FlightsCache.RobotsProducers.Algorithms
{
    public interface IFlexAlgorithm
    {
        Task GenerateQueueElements(ActionBlock<IEnumerable<QueueElement>> queueElementProcessingStream,
            DateTime startingDay,
            int numberOfDepartureDaysForward);
    }
}