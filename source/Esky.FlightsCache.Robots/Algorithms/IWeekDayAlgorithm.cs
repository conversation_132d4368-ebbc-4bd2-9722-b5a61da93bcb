using Esky.FlightsCache.Robots.ExternalServices.FlightsCache;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Esky.FlightsCache.RobotsProducers;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.Algorithms;

public interface IWeekDayAlgorithm
{
    Task<IEnumerable<QueueElement>> Generate(
        string partnerCode,
        string[] suppliers,
        DateTime startDay,
        int daysForward,
        ProviderCodeEnum provider
    );
}

public class WeekDayAlgorithm : IWeekDayAlgorithm
{
    private readonly ITimetableServiceClient _timetableService;
    private readonly IFlightsCacheService _flightsCacheService;
    private readonly IQueueElementCreator _queueElementCreator;
    private readonly ILogger<WeekDayAlgorithm> _logger;

    public WeekDayAlgorithm(
        ITimetableServiceClient timetableService,
        IFlightsCacheService flightsCacheService,
        IQueueElementCreator queueElementCreator,
        ILogger<WeekDayAlgorithm> logger)
    {
        _timetableService = timetableService;
        _flightsCacheService = flightsCacheService;
        _queueElementCreator = queueElementCreator;
        _logger = logger;
    }

    public async Task<IEnumerable<QueueElement>> Generate(
        string partnerCode,
        string[] suppliers,
        DateTime startDay,
        int daysForward,
        ProviderCodeEnum provider)
    {
        var endDate = startDay.Date.AddDays(daysForward);
        var dates = startDay.Date.Range(endDate)
            .Where(day => day.DayOfWeek == startDay.DayOfWeek)
            .ToList();

        var connectionNetworkTask = _timetableService.GetConnectionNetworkByProviderCode((int)provider, suppliers, newRoutes: false);
        var timetableTask = _flightsCacheService.GetTimetable(new SearchTimetablesRequest { Suppliers = suppliers, DepartureDates = dates }, CancellationToken.None);
        await Task.WhenAll(connectionNetworkTask, timetableTask);
        var connectionNetworks = connectionNetworkTask.Result;
        var flyingTimetableByDepartureAirport = timetableTask.Result.GroupBy(x => x.DepartureAirportCode);

        var suppliersCommaSeparated = string.Join(',', suppliers);
        var sourceName = $"{provider}Consumer_WeekDay_ExistingRoutes_{suppliersCommaSeparated}";

        _logger.LogInformation("{ProviderCode}:{Supplier} found {RoutesCount} departure routes", provider, suppliersCommaSeparated, connectionNetworks.Sum(x => x.ArrivalAirportCodes.Count));

        return EnumerateElements();

        IEnumerable<QueueElement> EnumerateElements()
        {
            var departureGroups = connectionNetworks
                .GroupJoin(
                    flyingTimetableByDepartureAirport,
                    connectionNetwork => connectionNetwork.DepartureAirportCode,
                    flyingTimetableItem => flyingTimetableItem.Key,
                    (connectionNetwork, flyingTimetableGroup) => (ConnectionNetwork: connectionNetwork, FlyingTimetableGroup: flyingTimetableGroup.FirstOrDefault()?.ToArray() ?? Array.Empty<SearchTimetablesResponseItem>())
                );

            foreach (var (connectionNetwork, flyingTimetableGroup) in departureGroups)
            {
                var departureCode = connectionNetwork.DepartureAirportCode;
                foreach (var connectionNetworkArrivalAirport in connectionNetwork.ArrivalAirportCodes)
                {
                    var arrivalCode = connectionNetworkArrivalAirport.ArrivalCode;
                    var daysToExclude = flyingTimetableGroup
                        .Where(ft => ft.ArrivalAirportCode == arrivalCode)
                        .SelectMany(x => x.Dates)
                        .ToHashSet();

                    foreach (var departureDay in dates)
                    {
                        if (daysToExclude.Contains(departureDay))
                        {
                            continue;
                        }

                        yield return _queueElementCreator
                            .CreateOneWayElement(provider, partnerCode, departureCode, arrivalCode, departureDay)
                            .ConfigureSourceName(sourceName)
                            .Resolve();
                    }
                }
            }
        }
    }
}