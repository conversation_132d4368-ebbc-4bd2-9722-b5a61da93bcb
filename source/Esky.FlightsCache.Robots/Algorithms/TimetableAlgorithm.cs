using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Esky.FlightsCache.RobotsProducers.Algorithms
{
    public class TimetableAlgorithm : ITimetableAlgorithm
    {
        private readonly ITimetableServiceClient _timetableService;
        private readonly IQueueElementCreator _queueElementCreator;

        private readonly ILogger<TimetableAlgorithm> _logger;

        public TimetableAlgorithm(ITimetableServiceClient timetableService, IQueueElementCreator queueElementCreator, ILogger<TimetableAlgorithm> logger)
        {
            _timetableService = timetableService;
            _queueElementCreator = queueElementCreator;
            _logger = logger;
        }

        public async Task GenerateQueueElements(ActionBlock<IEnumerable<QueueElement>> queueElementProcessingStream,
            string partnerCode,
            string airlineCode,
            int numberOfDepartureDaysForward,
            ProviderCodeEnum providerCode)
        {
            var routes = await _timetableService.GetConnectionNetworkByAirline(airlineCode);
            _logger.LogInformation("{ProviderCode}:{AirlineCode} found {RoutesCount} routes", providerCode, airlineCode, routes.Count);

            foreach (var route in routes)
            {
                var departureCode = route.DepartureAirportCode;
                foreach (var airport in route.ArrivalAirportCodes)
                {
                    var arrivalCode = airport.ArrivalCode;

                    var days = await _timetableService.GetFlyingDays(airlineCode, departureCode, arrivalCode);

                    if (_logger.IsEnabled(LogLevel.Trace))
                    {
                        _logger.LogTrace("for route {DepartureCode}-{ArrivalCode} {AirlineCode} flies on: {Days}", departureCode, arrivalCode, airlineCode, string.Join(",", days));
                    }
                    var queueElements = new List<QueueElement>();

                    foreach (var departureDay in days)
                    {
                        if (departureDay <= DateTime.Now ||
                            DateTime.Now.AddDays(numberOfDepartureDaysForward) < departureDay)
                        {
                            continue;
                        }

                        var element = _queueElementCreator.CreateOneWayElement(providerCode, partnerCode,
                                departureCode, arrivalCode, departureDay)
                            .ConfigureAirlineCode(airlineCode)
                            .ConfigureSourceName($"{providerCode}Consumer_{airlineCode}")
                            .Resolve();

                        queueElements.Add(element);
                    }

                    if (queueElements.Any())
                    {
                        queueElementProcessingStream.Post(queueElements);
                    }
                }
            }
        }
    }
}