using Esky.FlightsCache.Robots.ExternalServices.FlightsCache;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Esky.FlightsCache.RobotsProducers;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.Algorithms
{
    public class DayByDayAlgorithm : IDayByDayAlgorithm
    {
        private readonly ITimetableServiceClient _timetableService;
        private readonly IFlightsCacheService _flightsCacheService;
        private readonly IQueueElementCreator _queueElementCreator;
        private readonly ILogger<DayByDayAlgorithm> _logger;

        public DayByDayAlgorithm(ITimetableServiceClient timetableService,
            IFlightsCacheService flightsCacheService,
            IQueueElementCreator queueElementCreator,
            ILogger<DayByDayAlgorithm> logger)
        {
            _timetableService = timetableService;
            _flightsCacheService = flightsCacheService;
            _queueElementCreator = queueElementCreator;
            _logger = logger;
        }

        public async Task<(IEnumerable<TravelFusionQueueElement>, int count)> GenerateTravelFusionElements(
            IEnumerable<string> suppliers,
            IEnumerable<string> exceptSuppliers,
            string partnerCode,
            DateTime startDate,
            DateTime endDate,
            TimeSpan minAge,
            string sourceName,
            string paxConfiguration = PaxConfigurations.Default)
        {
            var timetableRequest = new SearchTimetablesRequest
            {
                Suppliers = suppliers,
                ExceptSuppliers = exceptSuppliers,
                MinDepartureDate = startDate,
                MaxDepartureDate = endDate,
                MinAge = minAge
            };

            var timetablesResponseItems =
                await _flightsCacheService.GetTimetable(timetableRequest, CancellationToken.None);

            var count = timetablesResponseItems.Sum(x => x.Dates.Count());

            _logger.LogInformation(
                "{sourceName} found routes: {RoutesCount}, routes*days {DatesCount}",
                sourceName,
                timetablesResponseItems.Length,
                count
            );

            return (timetablesResponseItems
                .SelectMany(
                    item => item
                        .Dates
                        .Select(date => _queueElementCreator
                            .CreateOneWayElement(ProviderCodeEnum.TravelFusion, partnerCode, item.DepartureAirportCode,
                                item.ArrivalAirportCode, date.Date)
                            .ConfigureSourceName(sourceName)
                            .ConfigurePaxConfiguration(paxConfiguration)
                            .Resolve()
                            .To<TravelFusionQueueElement>()
                        )
                ), count);
        }

        public async Task<IEnumerable<QueueElement>> Generate(
            string partnerCode,
            ProviderCodeEnum provider,
            DateTime startDate,
            DateTime endDate,
            IEnumerable<string> suppliers)
        {
            var connectionNetworks = await _timetableService.GetConnectionNetworkByProviderCode((int)provider, suppliers, newRoutes: true);
            _logger.LogInformation("{ProviderCode}: found new {RoutesCount} departure routes", provider, connectionNetworks.Sum(x => x.ArrivalAirportCodes.Count));

            return EnumerateElements();

            IEnumerable<QueueElement> EnumerateElements()
            {
                foreach (var connectionNetwork in connectionNetworks)
                {
                    var departureCode = connectionNetwork.DepartureAirportCode;
                    foreach (var connectionNetworkArrivalAirport in connectionNetwork.ArrivalAirportCodes)
                    {
                        var arrivalCode = connectionNetworkArrivalAirport.ArrivalCode;

                        foreach (var departureDay in startDate.Range(endDate))
                        {
                            var element = _queueElementCreator
                                .CreateOneWayElement(provider, partnerCode, departureCode, arrivalCode, departureDay)
                                .ConfigureSourceName($"{provider}Consumer_DayByDay_DiscoverRoutes")
                                .Resolve();

                            yield return element;
                        }
                    }
                }
            }
        }
    }
}