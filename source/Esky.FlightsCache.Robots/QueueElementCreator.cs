using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers
{
    public class QueueElementCreator : IQueueElementCreator
    {
        private QueueElement? _queueElement;

        public IQueueElementCreator CreateOneWayElement(ProviderCodeEnum? providerCode, string partnerCode,
            string departureCode, string arrivalCode, DateTime departureDay, int flex = 0, string? officeId = null,
            string[]? alcExcludedAirlines = null)
        {
            _queueElement = new QueueElement()
            {
                DepartureDay = DateTime.SpecifyKind(departureDay, DateTimeKind.Unspecified),
                DeleteDepartureDayFrom = departureDay,
                DeleteDepartureDayTo = flex != 0 ? departureDay.AddDays(flex) : departureDay,
                PartnerCode = partnerCode,
                DepartureCode = departureCode,
                ArrivalCode = arrivalCode,
                IsRoundTrip = false,
                Flex = flex,
                ProviderCode = providerCode,
                OfficeId = officeId,
                AlcExcludedAirlines = alcExcludedAirlines ?? []
            };

            return this;
        }

        public IQueueElementCreator CreateRoundTripElement(ProviderCodeEnum? providerCode, string partnerCode,
            string departureCode, string arrivalCode, DateTime departureDay, DateTime returnDepartureDay, int flex = 0,
            string? officeId = null, string[]? alcExcludedAirlines = null)
        {
            _queueElement = new QueueElement
            {
                DepartureDay = DateTime.SpecifyKind(departureDay, DateTimeKind.Unspecified),
                PartnerCode = partnerCode,
                ReturnDepartureDay = DateTime.SpecifyKind(returnDepartureDay, DateTimeKind.Unspecified),
                DepartureCode = departureCode,
                DeleteDepartureDayFrom = departureDay,
                DeleteDepartureDayTo = flex != 0 ? departureDay.AddDays(flex) : departureDay,
                DeleteReturnDepartureDayFrom = returnDepartureDay,
                DeleteReturnDepartureDayTo = flex != 0 ? returnDepartureDay.AddDays(flex) : returnDepartureDay,
                ArrivalCode = arrivalCode,
                IsRoundTrip = true,
                Flex = flex,
                ProviderCode = providerCode,
                OfficeId = officeId,
                AlcExcludedAirlines = alcExcludedAirlines ?? Array.Empty<string>()
            };

            return this;
        }

        public IQueueElementCreator CreateSeparableRoundTripElement(ProviderCodeEnum? providerCode, string partnerCode,
            string departureCode, string arrivalCode, DateTime departureDay, ReturnDepartureDate[] returnDepartureDates,
            string? officeId = null, string[]? alcExcludedAirlines = null)
        {
            _queueElement = new QueueElement
            {
                DepartureDay = DateTime.SpecifyKind(departureDay, DateTimeKind.Unspecified),
                PartnerCode = partnerCode,
                DepartureCode = departureCode,
                DeleteDepartureDayFrom = departureDay,
                DeleteDepartureDayTo = departureDay,
                ReturnDepartureDay = null,
                ReturnDepartureDays = returnDepartureDates
                    .Select(d => d with { Return = DateTime.SpecifyKind(d.Return, DateTimeKind.Unspecified) })
                    .ToArray(),
                DeleteReturnDepartureDayFrom = null,
                DeleteReturnDepartureDayTo = null,
                Flex = 0,
                ArrivalCode = arrivalCode,
                IsRoundTrip = true,
                ProviderCode = providerCode,
                OfficeId = officeId,
                AlcExcludedAirlines = alcExcludedAirlines ?? Array.Empty<string>()
            };

            return this;
        }

        public QueueElement Resolve()
        {
            if (_queueElement == null)
            {
                throw new Exception("Queue element not initialized. Use CreateX method before.");
            }

            return _queueElement;
        }

        public IQueueElementCreator ConfigureDeleteForFlex()
        {
            if (_queueElement == null)
            {
                throw new Exception("Queue element not initialized. Use CreateX method before.");
            }

            _queueElement.SetDeleteDateForFlex();

            return this;
        }

        public IQueueElementCreator ConfigureAirlineCode(string airlineCode)
        {
            if (_queueElement == null)
            {
                throw new Exception("Queue element not initialized. Use CreateX method before.");
            }

            _queueElement.AirlineCode = airlineCode;
            return this;
        }

        public IQueueElementCreator ConfigureSourceName(string sourceName)
        {
            if (_queueElement == null)
            {
                throw new Exception("Queue element not initialized. Use CreateX method before.");
            }

            _queueElement.SourceName = sourceName;
            return this;
        }

        public IQueueElementCreator ConfigurePaxConfiguration(string paxConfiguration)
        {
            if (_queueElement == null)
            {
                throw new Exception("Queue element not initialized. Use CreateX method before.");
            }

            _queueElement.PaxConfiguration = paxConfiguration;
            return this;
        }
    }
}