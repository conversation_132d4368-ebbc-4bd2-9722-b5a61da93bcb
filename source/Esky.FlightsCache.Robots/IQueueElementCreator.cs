using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.RobotsProducers
{
    public interface IQueueElementCreator
    {
        IQueueElementCreator CreateOneWayElement(ProviderCodeEnum? provider, string partnerCode, string departureCode, string arrivalCode, DateTime departureDay, int flex = 0, string? officeId = null, string[]? alcExcludedAirlines = null);
        IQueueElementCreator CreateRoundTripElement(ProviderCodeEnum? provider, string partnerCode, string departureCode, string arrivalCode, DateTime departureDay, DateTime returnDepartureDay, int flex = 0, string? officeId = null, string[]? alcExcludedAirlines = null);
        IQueueElementCreator CreateSeparableRoundTripElement(ProviderCodeEnum? providerCode, string partnerCode, string departureCode, string arrivalCode, DateTime departureDay, ReturnDepartureDate[] returnDepartureDates, string? officeId = null, string[]? alcExcludedAirlines = null);
        QueueElement Resolve();
        IQueueElementCreator ConfigureDeleteForFlex();
        IQueueElementCreator ConfigureAirlineCode(string airlineCode);
        IQueueElementCreator ConfigureSourceName(string sourceName);
        IQueueElementCreator ConfigurePaxConfiguration(string paxConfiguration);
    }
}