using System;

namespace Esky.FlightsCache.Robots.TimetableServiceClient
{
    public class GetFlyingDaysResponse
    {
        public required FlyingDaysId id { get; set; }
        public DateTime createTime
        {
            get; set;
        }

        public class FlyingDaysId
        {
            private DateTime _flyingDay;

            public required string airlineCode { get; set; }
            public required string departureAirportCode { get; set; }
            public required string arrivalAirportCode { get; set; }
            public DateTime day
            {
                get => _flyingDay;
                set => _flyingDay = value.Date;
            }
        }
    }
}