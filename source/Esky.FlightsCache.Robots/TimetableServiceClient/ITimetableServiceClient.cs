using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.TimetableServiceClient
{
    public interface ITimetableServiceClient
    {
        Task<IList<DateTime>> GetFlyingDays(string airlineCode, string departureCode, string arrivalCode);
        Task<IList<DateTime>> GetFlyingDays(string departureCode, string arrivalCode, IReadOnlyCollection<string> airlineCodes);
        Task<IList<ConnectionNetwork>> GetConnectionNetworkByAirline(string airlineCode);
        Task<IList<ConnectionNetwork>> GetConnectionNetworkByProviderCode(int providerCode, IEnumerable<string> suppliers, bool? newRoutes = null);
    }
}