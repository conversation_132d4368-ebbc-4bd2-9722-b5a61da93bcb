using AsyncKeyedLock;
using Esky.FlightsCache.Robots.TimetableServiceClient;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.TimetableServiceClient
{
    public class TimetableServiceClient : ITimetableServiceClient
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly Uri? _baseAddress;
        private readonly IMemoryCache _memoryCache;
        private static readonly AsyncKeyedLocker<string> _asyncKeyedLocker = new();

        public TimetableServiceClient(IHttpClientFactory clientFactory,
            IOptions<TimetableServiceConfiguration> config,
            IMemoryCache memoryCache)
        {
            _clientFactory = clientFactory;
            _baseAddress = string.IsNullOrWhiteSpace(config.Value.Url) ? null : new Uri(config.Value.Url);
            _memoryCache = memoryCache;
        }

        public async Task<IList<DateTime>> GetFlyingDays(string airlineCode, string departureCode, string arrivalCode)
        {
            var client = _clientFactory.CreateClient();
            client.BaseAddress = _baseAddress;

            var response = await client.GetAsync($"api/timetables/destination/{departureCode}-{arrivalCode}/airline/{airlineCode}/flyingdays");

            if (response.IsSuccessStatusCode)
            {
                var res = JsonConvert.DeserializeObject<List<GetFlyingDaysResponse>>(await response.Content.ReadAsStringAsync()) ?? [];
                return res.Select(day => day.id.day).Where(day => day >= DateTime.Today).ToList();
            }
            throw new Exception($"Invalid response GetFlyingDays from timetable api for {departureCode}-{arrivalCode}: {response.StatusCode}");
        }

        public async Task<IList<DateTime>> GetFlyingDays(string departureCode, string arrivalCode, IReadOnlyCollection<string> airlineCodes)
        {
            var client = _clientFactory.CreateClient();
            client.BaseAddress = _baseAddress;

            var response = await client.GetAsync($"api/timetables/destination/{departureCode}-{arrivalCode}/flyingdays?airlineCodes={string.Join(',', airlineCodes)}");

            if (response.IsSuccessStatusCode)
            {
                var schedule = JsonConvert.DeserializeObject<List<AirlineRouteSchedule>>(await response.Content.ReadAsStringAsync()) ?? [];
                return schedule
                    .SelectMany(e => e.Days.Select(day => new DateTime(e.Year, e.Month, day)))
                    .Where(day => DateTime.Today <= day)
                    .Distinct()
                    .ToList();
            }
            throw new Exception($"Invalid response GetFlyingDays from timetable api for {departureCode}-{arrivalCode}: {response.StatusCode}");
        }

        public async Task<IList<ConnectionNetwork>> GetConnectionNetworkByAirline(string airlineCode)
        {
            var client = _clientFactory.CreateClient();
            client.BaseAddress = _baseAddress;

            var uri = $"api/connectionnetworks/airline/{airlineCode}";

            if (_memoryCache.TryGetValue(uri, out IList<ConnectionNetwork>? connectionNetwork))
            {
                return connectionNetwork!;
            }

            using (await _asyncKeyedLocker.LockAsync(uri))
            {
                connectionNetwork = await _memoryCache.GetOrCreateAsync(uri, async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10);
                    var response = await client.GetAsync(uri);
                    if (response.IsSuccessStatusCode)
                    {
                        return JsonConvert.DeserializeObject<IList<ConnectionNetwork>>(await response.Content.ReadAsStringAsync()) ?? [];
                    }

                    throw new InvalidOperationException(
                        $"Invalid response GetConnectionNetworkByAirline from timetable api for: {airlineCode}, statusCode: {response.StatusCode}");
                });
            }
            return connectionNetwork!;
        }

        public async Task<IList<ConnectionNetwork>> GetConnectionNetworkByProviderCode(int providerCode, IEnumerable<string> suppliers, bool? newRoutes = null)
        {
            var client = _clientFactory.CreateClient();
            client.BaseAddress = _baseAddress;

            var uri = $"/api/connectionnetworks/providers/{providerCode}";
            var parameters = string.Join('&', GetParameters());
            if (!string.IsNullOrEmpty(parameters)) uri += $"?{parameters}";

            var response = await client.GetAsync(uri);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<IList<ConnectionNetwork>>() ?? [];
            }
            throw new Exception($"Invalid response GetConnectionNetworkByProviderCode from timetable api for provider: {providerCode}");

            IEnumerable<string> GetParameters()
            {
                if (suppliers.Any()) yield return $"suppliers={string.Join(',', suppliers)}";
                if (newRoutes.HasValue) yield return $"newRoutes={newRoutes}";
            }
        }
        
        internal class AirlineRouteSchedule
        {
            public required string AirlineCode { get; init; }
            public required string DepartureAirportCode { get; init; }
            public required string ArrivalAirportCode { get; init; }
            public required int Year { get; init; }
            public required int Month { get; init; }
            public required int[] Days { get; init; }
        }
    }
}