using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.TimetableServiceClient
{
    [DebuggerDisplay("{DebuggerDisplay,nq}")]
    public class ConnectionNetwork
    {
        public required string DepartureAirportCode { get; set; }

        public required List<ConnectionNetworkArrivalAirport> ArrivalAirportCodes { get; set; }

        private string DebuggerDisplay => $"{DepartureAirportCode}=>{string.Join(',', ArrivalAirportCodes.Select(x => x.ArrivalCode))}";
    }
}