using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract
{
    public class SearchTimetablesRequest
    {
        [Required]
        public required IEnumerable<string> Suppliers { get; set; }
        public IEnumerable<string>? ExceptSuppliers { get; set; }
        public IEnumerable<DateTime>? DepartureDates { get; set; }
        public DateTime? MinDepartureDate { get; set; }
        public DateTime? MaxDepartureDate { get; set; }
        public TimeSpan? MinAge { get; set; }
    }
}