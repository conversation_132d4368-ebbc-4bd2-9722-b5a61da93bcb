using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.ExternalServices.FlightsCache
{
    public class FlightsCacheService : IFlightsCacheService
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly Uri _baseAddress;
        public FlightsCacheService(IHttpClientFactory clientFactory, FlightsCacheServiceConfiguration config)
        {
            _clientFactory = clientFactory;
            _baseAddress = string.IsNullOrWhiteSpace(config?.Url)
                ? throw new ArgumentNullException(nameof(FlightsCacheServiceConfiguration), "Configuration is null or empty")
                : new Uri(config.Url);
        }

        public async Task<SearchTimetablesResponseItem[]> GetTimetable(SearchTimetablesRequest request, CancellationToken cancellationToken)
        {
            var client = _clientFactory.CreateClient();
            var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            var response = await client.PostAsync(
                new Uri(_baseAddress, "api/Timetables/travelfusion/days"),
                content,
                cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<SearchTimetablesResponseItem[]>(cancellationToken: cancellationToken) ?? [];
            }

            throw new Exception($"Invalid response from FlightsCache API - timetable for TravelFusion, reason: {response.ReasonPhrase}");
        }
    }
}
