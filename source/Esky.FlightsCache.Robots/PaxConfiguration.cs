using System;

namespace Esky.FlightsCache.Robots;

public readonly record struct PaxConfiguration
{
    private string Pax { get; }

    public int Adult { get; }
    public int Youth { get; }
    public int Child { get; }
    public int Infant { get; }

    public PaxConfiguration(string pax)
    {
        ArgumentNullException.ThrowIfNull(pax);

        try
        {
            var parts = pax.Split('.');
            Adult = int.Parse(parts[0]);
            Youth = int.Parse(parts[1]);
            Child = int.Parse(parts[2]);
            Infant = int.Parse(parts[3]);
            Pax = pax;
        }
        catch (Exception e) when (e is IndexOutOfRangeException or FormatException or ArgumentNullException or OverflowException)
        {
            throw new ArgumentException("Invalid pax configuration", nameof(pax));
        }
    }

    public PaxConfiguration(
        int? adult = null,
        int? youth = null,
        int? child = null,
        int? infant = null)
    {
        Adult = adult ?? 0;
        Youth = youth ?? 0;
        Child = child ?? 0;
        Infant = infant ?? 0;

        Pax = $"{Adult}.{Youth}.{Child}.{Infant}";
    }
    public override string ToString() => Pax;

    public int SeatsCount => Adult + Youth + Child;

    public static implicit operator string(PaxConfiguration pax) => pax.ToString();
    public static implicit operator PaxConfiguration(string pax) => new(pax);
}