using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Robots
{
    public static class DateTimeExtensions
    {
        public static DateTime Min(DateTime date1, DateTime? date2)
        {
            return !date2.HasValue || date1 < date2 ? date1 : date2.Value;
        }

        public static DateTime Max(DateTime date1, DateTime? date2)
        {
            return !date2.HasValue || date1 > date2 ? date1 : date2.Value;
        }

        public static IEnumerable<DateTime> Range(this DateTime startDate, DateTime endDate)
        {
            return Enumerable.Range(0, endDate.Subtract(startDate).Days + 1).Select(o => startDate.AddDays(o));
        }

        public static DateTime ToTomorrowIfPastOrToday(this DateTime dateTime)
        {
            var today = DateTime.UtcNow.Date;

            return dateTime.Date <= today
                ? today.AddDays(1)
                : dateTime;
        }
    }
}
