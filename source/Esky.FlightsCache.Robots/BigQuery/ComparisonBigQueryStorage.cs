using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Google.Apis.Bigquery.v2.Data;
using Google.Cloud.BigQuery.V2;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.BigQuery;

public interface ICacheRequestDiffStorage
{
    Task SaveDiff(IList<CacheRequest> ryanairRequests, IList<CacheRequest> sscRequests);
}

public class ComparisonBigQueryStorage : ICacheRequestDiffStorage
{
    private readonly Lazy<BigQueryTable> _table;
    private readonly ILogger<ComparisonBigQueryStorage> _logger;

    public ComparisonBigQueryStorage(ILogger<ComparisonBigQueryStorage> logger, Lazy<BigQueryClient> client)
    {
        _logger = logger;
        _table = new Lazy<BigQueryTable>(() =>
        {
            var dataset = client.Value.GetOrCreateDataset("SSC");
            return dataset.GetOrCreateTable("DirectRyanairComparison", new Table());
        });
    }

    public async Task SaveDiff(IList<CacheRequest> ryanairRequests, IList<CacheRequest> sscRequests)
    {
        try
        {
            var ryanairLegs = ryanairRequests.FirstOrDefault()?.Flights
                .SelectMany(f => f.Legs)
                .Select(l => (Hash: HashLeg(l), Leg: l)).ToList() ?? [];

            var sscLegs = sscRequests.FirstOrDefault()?.Flights
                .SelectMany(f => f.Legs)
                .Select(l => (Hash: HashLeg(l), Leg: l)).ToList() ?? [];

            var leftOuterJoin =
                from rLeg in ryanairLegs
                join sLeg in sscLegs on rLeg.Hash equals sLeg.Hash into temp
                from sLeg in temp.DefaultIfEmpty()
                select (rLeg, sLeg);

            var rightOuterJoin =
                from sLeg in sscLegs
                join rLeg in ryanairLegs on sLeg.Hash equals rLeg.Hash into temp
                from rLeg in temp.DefaultIfEmpty()
                select (rLeg, sLeg);

            var diffs = leftOuterJoin.Union(rightOuterJoin).ToList();

            var rows = new List<BigQueryInsertRow>();

            foreach (var diff in diffs)
            {
                var leg = diff.rLeg.Leg ?? diff.sLeg.Leg;

                var row = new BigQueryInsertRow
                {
                    { "Timestamp", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") },
                    { "DepartureCode", leg.DepartureCode },
                    { "ArrivalCode", leg.ArrivalCode },
                    { "DepartureDate", leg.DepartureDate.ToString("s") },
                    { "Currency", leg.CurrencyCode }
                };

                var directRyanairPrice = diff.rLeg.Leg?.AdultPrices.FirstOrDefault();
                var sscPrice = diff.sLeg.Leg?.AdultPrices.FirstOrDefault();

                if (directRyanairPrice != null)
                {
                    row.Add("DirectRyanairPrice", (float)(directRyanairPrice.BasePrice + directRyanairPrice.TaxPrice));
                }

                if (sscPrice != null)
                {
                    row.Add("SSCPrice", (float)(sscPrice.BasePrice + sscPrice.TaxPrice));
                }

                rows.Add(row);
            }

            await _table.Value.InsertRowsAsync(rows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bigquery save");
        }
    }

    private static string HashLeg(FlightCacheLeg leg)
    {
        var sr = new StringBuilder();
        var firstDepartureDate = leg.Segments[0].DepartureDate;

        sr.Append(firstDepartureDate.Date.ToString("d"));
        foreach (var segment in leg.Segments)
        {
            sr.Append(new SegmentHashCoder
            {
                DepartureCode = segment.DepartureCode,
                DepartureTime = segment.DepartureDate.ToString("HH:mm"),
                ArrivalCode = segment.ArrivalCode,
                ArrivalTime = segment.ArrivalDate.ToString("HH:mm"),
                AirlineCode = segment.AirlineCode,
                FlightNumber = segment.FlightNumber
            });
            sr.Append('|');
        }

        return sr.ToString();
    }
}