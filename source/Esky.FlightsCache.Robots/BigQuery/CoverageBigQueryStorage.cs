using Google.Cloud.BigQuery.V2;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots.BigQuery;

public interface ICoverageStorage
{
    Task SaveCoverageAsync(IEnumerable<CoverageResult> results);
}

public class CoverageBigQueryStorage : ICoverageStorage
{
    private readonly Lazy<BigQueryTable> _table;
    private readonly ILogger<CoverageBigQueryStorage> _logger;

    public CoverageBigQueryStorage(ILogger<CoverageBigQueryStorage> logger, Lazy<BigQueryClient> client)
    {
        _logger = logger;
        _table = new Lazy<BigQueryTable>(() =>
        {
            var dataset = client.Value.GetDataset("FlightsCache");
            return dataset.GetTable("CacheCoverage");
        });
    }

    public async Task SaveCoverageAsync(IEnumerable<CoverageResult> results)
    {
        try
        {
            var rows = new List<BigQueryInsertRow>();
            foreach (var result in results)
            {
                var row = new BigQueryInsertRow
                {
                    { "Timestamp", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") },
                    { "Airline", result.Airline },
                    { "Provider", result.Provider },
                    { "Departure", result.Departure },
                    { "Arrival", result.Arrival },
                    { "CacheDays", result.CacheDays.Select(x=>x.ToString("yyyy-MM-dd")).ToList() },
                    { "TimetableDays", result.TimetableDays.Select(x=>x.ToString("yyyy-MM-dd")).ToList() }
                };
                
                if (result.DepartureCountry != null)
                {
                    row.Add("DepartureCountry", result.DepartureCountry);
                }
                
                if (result.ArrivalCountry != null)
                {
                    row.Add("ArrivalCountry", result.ArrivalCountry);
                }
            
                if (result.Supplier != null)
                {
                    row.Add("Supplier", result.Supplier);
                }

                rows.Add(row);
            }

            await _table.Value.InsertRowsAsync(rows);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to save coverage result");
        }
    }
}

public record CoverageResult
{
    public required string Airline { get; init; }
    public required int Provider { get; init; }
    public string? Supplier { get; init; }
    public required string Departure { get; init; }
    public required string Arrival { get; init; }
    public required string? DepartureCountry { get; init; }
    public required string? ArrivalCountry { get; init; }
    public required ISet<DateTime> CacheDays { get; init; }
    public required ISet<DateTime> TimetableDays { get; init; }
}