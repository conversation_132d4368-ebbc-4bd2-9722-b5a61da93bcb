using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{

    public interface ICacheRequestBuilder
    {
        CacheRequest Build(List<FlightCache> flights, SearchFlightsProviderQuery query, QueueElement queueElement, RequestBuildSettings requestBuildSettings);
    }
}