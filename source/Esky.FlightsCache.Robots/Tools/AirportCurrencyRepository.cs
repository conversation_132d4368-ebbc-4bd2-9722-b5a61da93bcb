using Microsoft.Extensions.Options;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Tools
{
    public class AirportCurrencyRepository : IAirportCurrencyRepository
    {
        private readonly IMongoCollection<AirportCurrencyDocument> _collection;

        public AirportCurrencyRepository(IOptions<AirportCurrencySettings> settings)
        {
            var database = new MongoClient(settings.Value.ConnectionString).GetDatabase(MongoUrl.Create(settings.Value.ConnectionString).DatabaseName);
            _collection = database.GetCollection<AirportCurrencyDocument>("AirportCurrencies");
        }

        public Task<List<AirportCurrencyDocument>> GetAll(CancellationToken cancellationToken)
        {
            return _collection.AsQueryable().ToListAsync(cancellationToken);
        }

        public Task Update(string airportCode, string providerName, string currencyCode, DateTime lastUpdate, CancellationToken cancellationToken)
        {
            return _collection.UpdateOneAsync(
                s => s.Id == AirportCurrencyDocument.AirportCurrencyId.Create(airportCode, providerName),
                Builders<AirportCurrencyDocument>.Update
                    .Set(s => s.Currency, currencyCode)
                    .Set(s => s.LastUpdateDate, lastUpdate), new UpdateOptions()
                    {
                        IsUpsert = true
                    }, cancellationToken);
        }
    }

    public interface IAirportCurrencyRepository
    {
        Task<List<AirportCurrencyDocument>> GetAll(CancellationToken cancellationToken);
        Task Update(string airportCode, string providerName, string currencyCode, DateTime lastUpdate, CancellationToken cancellationToken);
    }
}