using MongoDB.Bson.Serialization.Attributes;
using System;

namespace Esky.FlightsCache.RobotsProducers.Tools
{
    public class AirportCurrencyDocument
    {
        [BsonId]
        public required AirportCurrencyId Id { get; set; }

        public required string Currency { get; set; }

        public DateTime LastUpdateDate { get; set; }

        public class AirportCurrencyId
        {
            public required string AirportCode { get; set; }

            public required string ProviderName { get; set; }

            public static AirportCurrencyId Create(string airportCode, string providerName)
            {
                return new AirportCurrencyId()
                {
                    AirportCode = airportCode,
                    ProviderName = providerName
                };
            }
        }
    }
}