using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Tools
{
    public interface IAirportCurrencyService
    {
        Task Update(string airportCode, string providerName, string currency, CancellationToken cancellationToken);
        Task Update(string airportCode, string providerName, string currency);
        Task<string?> Get(string airportCode, string providerName, CancellationToken cancellationToken);
        Task<string?> Get(string airportCode, string providerName);
        Task<bool> SameCurrency(string airportCode1, string airportCode2, string providerName);
    }
}