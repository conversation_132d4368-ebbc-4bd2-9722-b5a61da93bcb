using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Tools
{
    public class AirportCurrencyService : IAirportCurrencyService
    {
        private readonly IAirportCurrencyRepository _repository;
        private readonly AirportCurrencySettings _airportCurrencySettings;
        private DateTime _lastUpdate;
        private readonly ConcurrentDictionary<string, AirportCurrencyDocument> _allDocuments = new();

        public AirportCurrencyService(IAirportCurrencyRepository repository, IOptions<AirportCurrencySettings> airportCurrencySettings)
        {
            _repository = repository;
            _airportCurrencySettings = airportCurrencySettings.Value;
        }

        public Task Update(string airportCode, string providerName, string currency)
        {
            return Update(airportCode, providerName, currency, CancellationToken.None);
        }

        public async Task<string?> Get(string airportCode, string providerName, CancellationToken cancellationToken)
        {
            await TryLoad(cancellationToken);

            return _allDocuments!.GetValueOrDefault(CreateKey(airportCode, providerName), default)?.Currency;
        }

        public Task<string?> Get(string airportCode, string providerName)
        {
            return Get(airportCode, providerName, CancellationToken.None);
        }

        public async Task<bool> SameCurrency(string airportCode1, string airportCode2, string providerName)
        {
            var currency1 = await Get(airportCode1, providerName);

            if (currency1 == null) return false;

            var currency2 = await Get(airportCode2, providerName);

            if (currency2 == null) return false;

            return currency1.Equals(currency2, StringComparison.OrdinalIgnoreCase);
        }

        public async Task Update(string airportCode, string providerName, string currency, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(currency)) return;

            await TryLoad(cancellationToken);

            var key = CreateKey(airportCode, providerName);
            var document = _allDocuments.GetOrAdd(key, new AirportCurrencyDocument()
            {
                Currency = currency,
                Id = new AirportCurrencyDocument.AirportCurrencyId() { AirportCode = airportCode, ProviderName = providerName },
                LastUpdateDate = DateTime.MinValue
            });

            if (document.LastUpdateDate.AddMinutes(_airportCurrencySettings.UpdateIntervalInMinutes) < DateTime.UtcNow)
            {
                document.Currency = currency;
                document.LastUpdateDate = DateTime.UtcNow;

                await _repository.Update(airportCode, providerName, currency, document.LastUpdateDate, cancellationToken);
            }
        }

        private async Task TryLoad(CancellationToken cancellationToken)
        {
            if (_allDocuments.IsEmpty || _lastUpdate.AddMinutes(_airportCurrencySettings.UpdateIntervalInMinutes) < DateTime.Now)
            {
                var allDocuments = (await _repository.GetAll(cancellationToken))
                    .ToDictionary(CreateKey, s => s);

                foreach (var d in allDocuments)
                {
                    _allDocuments.AddOrUpdate(d.Key, d.Value, (_, document) => document);
                }

                _lastUpdate = DateTime.UtcNow;
            }
        }

        private static string CreateKey(AirportCurrencyDocument document)
        {
            return CreateKey(document.Id.AirportCode, document.Id.ProviderName);
        }

        private static string CreateKey(string airportCode, string providerName)
        {
            return $"{airportCode}_{providerName}";
        }
    }
}