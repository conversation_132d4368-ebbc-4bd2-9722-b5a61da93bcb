using System;
using System.Globalization;

namespace Esky.FlightsCache.RobotsProducers.Tools
{
    public static class ConnectionNetworkExtensions
    {
        /// <summary>
        /// Get first day of month
        /// </summary>
        /// <param name="month">Month in format: yyyyMM</param>
        /// <returns></returns>
        public static DateTime GetFirstDayOfMonth(this int month)
        {
            return DateTime.ParseExact(month.ToString() + "01", "yyyyMMdd", CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Get last day of month
        /// </summary>
        /// <param name="month">Month in format: yyyyMM</param>
        /// <returns></returns>
        public static DateTime GetLastDayOfMonth(this int month)
        {
            var firstDay = GetFirstDayOfMonth(month);

            return new DateTime(firstDay.Year, firstDay.Month, DateTime.DaysInMonth(firstDay.Year, firstDay.Month));
        }
    }
}