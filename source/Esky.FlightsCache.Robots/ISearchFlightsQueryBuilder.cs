using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public interface ISearchFlightsQueryBuilder
    {
        SearchFlightsProviderQuery BuildSingle(QueueElement queueElement);
        IEnumerable<(SearchFlightsProviderQuery Query, bool IsRequired)> BuildAll(QueueElement queueElement);
    }
}