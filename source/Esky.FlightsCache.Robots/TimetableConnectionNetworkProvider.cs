using Esky.FlightsCache.RobotsProducers.Algorithms;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Robots;

public interface IConnectionNetworkProvider
{
    Task<IReadOnlyCollection<Route>> GetConnectionNetwork(IReadOnlyCollection<string> airlineCodes);
}

public class TimetableConnectionNetworkProvider : IConnectionNetworkProvider
{
    private readonly ILogger<TimetableConnectionNetworkProvider> _logger;
    private readonly ITimetableServiceClient _timetableService;

    public TimetableConnectionNetworkProvider(
        ILogger<TimetableConnectionNetworkProvider> logger,
        ITimetableServiceClient timetableService)
    {
        _logger = logger;
        _timetableService = timetableService;
    }

    public async Task<IReadOnlyCollection<Route>> GetConnectionNetwork(IReadOnlyCollection<string> airlineCodes)
    {
        var connectionNetworks = await Task.WhenAll(airlineCodes.Select(x => _timetableService.GetConnectionNetworkByAirline(x)));

        var routes =
            (
                from airlineConnectionNetworks in connectionNetworks
                from connectionNetwork in airlineConnectionNetworks
                from arrivalAirport in connectionNetwork.ArrivalAirportCodes
                select new { DepartureCode = connectionNetwork.DepartureAirportCode, arrivalAirport.ArrivalCode }
            )
            .Distinct();

        List<Route> result = [];

        foreach (var route in routes)
        {
            var flyingDates = await _timetableService.GetFlyingDays(route.DepartureCode, route.ArrivalCode, airlineCodes);
            result.Add(new Route(route.DepartureCode, route.ArrivalCode, flyingDates.AsReadOnly()));
        }

        _logger.LogInformation("For [{@AirlineCodes}] found {NRoutes} routes", airlineCodes, result.Count);
        return result;
    }
}