using Esky.Flights.Integration.Providers.Contract;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class SeparationOptionsResolver : ISeparationOptionsResolver
    {
        public MessageContract.SeparationOptions Resolve(LegDto leg, bool isRoundTrip)
        {
            if (leg.SeparationOptions is null || leg.SeparationOptions.Options == SeparationOptionEnum.None)
            {
                return new MessageContract.SeparationOptions
                {
                    Options = isRoundTrip ? MessageContract.SeparationOptionEnum.None : MessageContract.SeparationOptionEnum.OneWay
                };
            }

            return new MessageContract.SeparationOptions
            {
                Options = (MessageContract.SeparationOptionEnum)leg.SeparationOptions.Options,
                MinStay = leg.SeparationOptions.MinStay
            };
        }
    }
}
