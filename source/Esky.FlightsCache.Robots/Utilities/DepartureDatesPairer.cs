using System.Collections.Generic;
using System;

namespace Esky.FlightsCache.Robots.Utilities;
public static class DepartureDatesPairer
{
    public static IEnumerable<PairingResult> PairDates(List<DateTime> outbound, List<DateTime> inbound)
    {
        outbound.Sort();
        inbound.Sort();

        int o = outbound.Count - 1, i = inbound.Count - 1;
        while (o >= 0 && i >= 0)
        {
            if (outbound[o] <= inbound[i])
            {
                yield return new(outbound[o], inbound[i]);
                o--;
                i--;
            }
            else
            {
                yield return new(outbound[o], outbound[o]);
                o--;
            }
        }

        while (o >= 0)
        {
            yield return new(outbound[o], outbound[o]);
            o--;
        }

        while (i >= 0)
        {
            yield return new(inbound[i], inbound[i]);
            i--;
        }
    }

    public record struct PairingResult(DateTime outboundDate, DateTime inboundDate);
}
