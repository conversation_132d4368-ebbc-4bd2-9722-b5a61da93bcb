using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class PriceCacheBuilder : IPriceCacheBuilder
    {
        public PriceCacheEntry Build(SearchFlightsProviderQuery query, FlightDto flightDto, PassengerTypeEnum passengerType, int legIndex)
        {
            int MapPriceToLegIndex(PriceDto prices)
            {
                if (prices.Segments is null || !prices.Segments.Any()) return 0;
                var segmentToLegIndex = flightDto.Legs.SelectMany((leg, l) => leg.Segments.Select(_ => l)).ToArray();
                var si = prices.Segments[0];
                return segmentToLegIndex[si];
            }

            var segmentPrices = flightDto.Prices.Where(x => MapPriceToLegIndex(x) == legIndex);

            if (!segmentPrices.Any()) return new(0, 0);

            if (segmentPrices.All(o => !o.PassengerType.HasValue))
                return CalculateSingleAdultPrice(query, segmentPrices);

            var pricePaxGroup = segmentPrices
                .Where(o => o.PassengerType.HasValue && o.PassengerType == passengerType)
                .ToList();

            if (!pricePaxGroup.Any())
            {
                return new(0, 0);
            }

            return new PriceCacheEntry
            {
                BasePrice = pricePaxGroup.FirstOrDefault(x => x.PriceType == PriceType.Base)?.Amount ?? 0,
                TaxPrice = pricePaxGroup.FirstOrDefault(x => x.PriceType == PriceType.Tax)?.Amount ?? 0,
                MinimumNumberOfPaxes = 1
            };
        }

        private static PriceCacheEntry CalculateSingleAdultPrice(SearchFlightsProviderQuery query, IEnumerable<PriceDto> prices)
        {
            var paxCount = query.Passengers.Sum(p => p.Count);

            var basePrice = prices.FirstOrDefault(p => !p.PassengerType.HasValue && p.PriceType == PriceType.Base)?.Amount ?? 0;
            var tax = prices.FirstOrDefault(p => !p.PassengerType.HasValue && p.PriceType == PriceType.Tax)?.Amount ?? 0;

            return new PriceCacheEntry(basePrice / paxCount, tax / paxCount);
        }
    }
}
