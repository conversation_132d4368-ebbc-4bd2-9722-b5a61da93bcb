using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public interface IFlightCacheConverter
    {
        List<FlightCache> Convert(IEnumerable<FlightDto> flights,
            QueueElement queueElement,
            CacheTimeOutConfiguration timeOutConfiguration,
            SearchFlightsProviderQuery query);
    }
}