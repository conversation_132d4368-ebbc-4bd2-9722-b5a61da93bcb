using Microsoft.Extensions.Configuration;
using System;
using System.Data.SqlClient;

namespace Esky.FlightsCache.CurrencyProvider
{
    internal class CurrencyRatioDatabaseClient : ICurrencyRatioDatabaseClient
    {
        private readonly IConfiguration _config;

        public CurrencyRatioDatabaseClient(
            IConfiguration config)
        {
            _config = config;
        }

        public decimal GetRatio(string sourceCurrency, string destinationCurrency)
        {
            try
            {
                var connString = _config.GetConnectionString("FlightsPromoCache_Staging");

                if (string.IsNullOrEmpty(connString)) throw new Exception("Fallback currency ratio database not configured");

                using (SqlConnection connection = new SqlConnection(connString))
                using (var cmd = connection.CreateCommand())
                {
                    connection.Open();

                    cmd.CommandText = "SELECT TOP 1 [Ratio] " +
                        "FROM [dbo].[CurrencyRatios] " +
                        "WHERE SourceCurrency = @SourceCurrency AND DestinationCurrency = @DestinationCurrency";

                    cmd.Parameters.AddWithValue("@SourceCurrency", sourceCurrency);
                    cmd.Parameters.AddWithValue("@DestinationCurrency", destinationCurrency);

                    return (decimal)cmd.ExecuteScalar();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Unable to get currency ratio from db: {sourceCurrency}->{destinationCurrency}", ex);
            }
        }
    }

}