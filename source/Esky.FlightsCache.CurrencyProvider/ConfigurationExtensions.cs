using Esky.CurrencyService.ApiClient;
using Esky.CurrencyService.ApiClient.DependencyInjection.Microsoft;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.CurrencyProvider
{
    public static class ConfigurationExtensions
    {
        public static void ConfigureCurrencyProvider(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddCurrencyServiceClient();
            services.AddSingleton<IDateProvider, DateProvider>();
            services.AddScoped<ICurrencyRatioProvider, CurrencyRatioProvider>();
            services.AddSingleton<ICurrencyRatioDatabaseClient, CurrencyRatioDatabaseClient>();

            services.Configure<CurrencyServiceApiClientOptions>(configuration.GetSection("ExternalServices:CurrencyService"));
        }
    }
}
