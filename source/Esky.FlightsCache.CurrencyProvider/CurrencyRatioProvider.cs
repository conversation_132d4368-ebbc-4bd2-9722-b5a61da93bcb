using Esky.CurrencyService.ApiClient;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;

namespace Esky.FlightsCache.CurrencyProvider
{
    internal class CurrencyRatioProvider : ICurrencyRatioProvider
    {
        private readonly IMemoryCache _cache;
        private readonly ICurrencyServiceApiClient _currencyServiceClient;
        private readonly ICurrencyRatioDatabaseClient _currencyRatioDatabaseClient;
        private readonly ILogger<CurrencyRatioProvider> _logger;

        public CurrencyRatioProvider(
            IMemoryCache cache,
            ICurrencyServiceApiClient currencyServiceClient,
            ICurrencyRatioDatabaseClient currencyRatioDatabaseClient,
            ILogger<CurrencyRatioProvider> logger
            )
        {
            _cache = cache;
            _currencyServiceClient = currencyServiceClient;
            _currencyRatioDatabaseClient = currencyRatioDatabaseClient;
            _logger = logger;
        }

        public decimal GetRatio(string sourceCurrency, string targetCurrency)
        {
            if (sourceCurrency.Equals(targetCurrency, StringComparison.OrdinalIgnoreCase))
                return 1;

            string key = string.Intern($"Esky.FlightsCache.AmadeusFeed.Import.Parsing.CurrencyRatioProvider_{sourceCurrency}_{targetCurrency}");

            if (!_cache.TryGetValue(key, out decimal result))
            {
                lock (key)
                {
                    result = _cache.GetOrCreate(key, entry =>
                    {
                        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10);
                        return FetchRatio(sourceCurrency, targetCurrency);
                    });
                }
            }

            return result;
        }

        private decimal FetchRatio(string sourceCurrency, string destinationCurrency)
        {
            try
            {
                return FetchRatioFromApi(sourceCurrency, destinationCurrency);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Unable to fetch ratio from API ({SourceCurrency}=>{DestinationCurrency}). Using database CurrencyRatios fallback.", sourceCurrency, destinationCurrency);

                return _currencyRatioDatabaseClient.GetRatio(sourceCurrency, destinationCurrency);
            }
        }

        private decimal FetchRatioFromApi(string sourceCurrency, string destinationCurrency)
        {
            var rate = _currencyServiceClient.GetDefaultExchangeRateAsync(sourceCurrency, destinationCurrency).GetAwaiter().GetResult();

            if (rate == null || rate.Data == null)
                throw new Exception("Currency service returned null rate");

            return rate.Data.Rate;
        }

    }

}