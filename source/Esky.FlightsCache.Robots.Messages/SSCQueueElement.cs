using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.RobotsProducers.Messages;

public class SSCQueueElement : QueueElement
{
    public static SSCQueueElement Create(
        string departure,
        string arrival,
        DateTime departureDate,
        DateTime? returnDepartureDate,
        string supplier,
        int? flex = null,
        string pax = "2.0.0.0")
    {
        var flexDays = flex ?? 0;
        return new SSCQueueElement
        {
            ProviderCode = ProviderCodeEnum.SSCProvider,
            PartnerCode = "ADMIN",
            DepartureCode = departure,
            ArrivalCode = arrival,
            DepartureDay = departureDate,
            PaxConfiguration = pax,
            Supplier = supplier,
            SourceName = $"SSCRobot_{supplier}",
            DeleteDepartureDayFrom = departureDate.AddDays(-flexDays),
            DeleteDepartureDayTo = departureDate.AddDays(flexDays),
            Flex = flexDays,
            // rt
            ReturnDepartureDay = returnDepartureDate,
            IsRoundTrip = returnDepartureDate is not null,
            DeleteReturnDepartureDayFrom = returnDepartureDate?.AddDays(-flexDays),
            DeleteReturnDepartureDayTo = returnDepartureDate?.AddDays(flexDays)
        };
    }

    internal static readonly string RobotPrefix = "Robots.SSC";
    internal static string QueueNameForUnmatched() => $"{RobotPrefix}.unmatched";
}