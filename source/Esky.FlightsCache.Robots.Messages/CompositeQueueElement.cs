using Esky.Framework.PartnerSettings.Enums;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Messages;

public class CompositeQueueElement : QueueElement
{
    public bool ProceedNextOnSuccess { get; set; }
    public IReadOnlyCollection<CompositeQueueElement> NextQueueElements { get; set; } = [];

    public override string SourceName 
    {
        get => $"CompositeRobot{Depth}_{ProviderCode}_{Supplier}_{PartnerCode}";
        set
        {
            // Method intentionally left empty.
        }
    }
        
    private new string PaxConfiguration { get; set; }
        
    public string[] PaxConfigurations { get; set; }
    public int Depth { get; init; } = 0;

    public Override? OverrideSettings { get; set; }

    public record Override(ProviderOverride? Provider, SupplierOverride? Supplier);
    public record ProviderOverride(ProviderCodeEnum ProviderCode);
    public record SupplierOverride(string? SupplierName);
}