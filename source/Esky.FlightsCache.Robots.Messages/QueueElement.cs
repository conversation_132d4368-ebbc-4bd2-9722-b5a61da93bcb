using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Messages
{
    public record ReturnDepartureDate(DateTime Return, bool IsRequired);
    
    public class QueueElement
    {
        public virtual QueueElement InnerElement
        {
            set
            {
                DepartureCode = value.DepartureCode;
                ArrivalCode = value.ArrivalCode;
                IsRoundTrip = value.IsRoundTrip;
                DepartureDay = value.DepartureDay;
                ReturnDepartureDay = value.ReturnDepartureDay;
                ReturnDepartureDays = value.ReturnDepartureDays;
                PartnerCode = value.PartnerCode;
                Flex = value.Flex;
                DeleteDepartureDayFrom = value.DeleteDepartureDayFrom;
                DeleteDepartureDayTo = value.DeleteDepartureDayTo;
                DeleteReturnDepartureDayFrom = value.DeleteReturnDepartureDayFrom;
                DeleteReturnDepartureDayTo = value.DeleteReturnDepartureDayTo;
                ProviderCode = value.ProviderCode;
                Supplier = value.Supplier;
                AirlineCode = value.AirlineCode;
                SourceName = value.SourceName;
                PaxConfiguration = value.PaxConfiguration;
                OfficeId = value.OfficeId;
                AlcExcludedAirlines = value.AlcExcludedAirlines;
                Currency = value.Currency;
                FeatureName = value.FeatureName;
            }
        }

        public virtual bool IsValid() => true;

        public string DepartureCode { get; set; }

        public string ArrivalCode { get; set; }

        public bool IsRoundTrip { get; set; }

        public DateTime DepartureDay { get; set; }

        public DateTime? ReturnDepartureDay
        {
            get
            {
                return ReturnDepartureDays?.FirstOrDefault()?.Return;
            }
            set
            {
                if (value.HasValue)
                {
                    ReturnDepartureDays = [new ReturnDepartureDate(value.Value, true)];
                }
            }
        }

        public ReturnDepartureDate[] ReturnDepartureDays { get; set; }

        public string PartnerCode { get; set; }

        public virtual string SourceName { get; set; }

        public int Flex { get; set; }

        public DateTime DeleteDepartureDayFrom { get; set; }

        public DateTime DeleteDepartureDayTo { get; set; }

        public DateTime? DeleteReturnDepartureDayFrom { get; set; }

        public DateTime? DeleteReturnDepartureDayTo { get; set; }

        public string AirlineCode { get; set; }

        public ProviderCodeEnum? ProviderCode { get; set; }
        public string Supplier { get; set; }
#pragma warning disable CS8632
        public string? Currency { get; set; }
        public string? FeatureName { get; set; }
#pragma warning restore CS8632

        public string OfficeId { get; set; }
        public string[] AlcExcludedAirlines { get; set; }

        public string PaxConfiguration { get; set; }

        public override string ToString()
        {
            var tripDates = IsRoundTrip ? $"{DepartureDay:d}-{ReturnDepartureDay:d}" : DepartureDay.ToString("d");
            return $"{DepartureCode}-{ArrivalCode}:{tripDates}|Flex:{Flex}";
        }

        public void SetDeleteDateForFlex()
        {
            DeleteDepartureDayFrom = DepartureDay.AddDays(-Flex);
            DeleteDepartureDayTo = DepartureDay.AddDays(Flex);

            if (ReturnDepartureDay.HasValue)
            {
                DeleteReturnDepartureDayFrom = ReturnDepartureDay.Value.AddDays(-Flex);
                DeleteReturnDepartureDayTo = ReturnDepartureDay.Value.AddDays(Flex);
            }
        }
    }
}