using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.Robots.Messages;
public class HotelsApiQueueElement
{
    public string PartnerCode { get; set; }
    public string? ProviderConfigurationId { get; set; }
    public ProviderCodeEnum? ProviderCode { get; set; }
    public string SourceName { get; set; }
    public int[] HotelMetaCodes { get; set; }
    public DateOnly CheckInDate { get; set; }
    public DateOnly CheckOutDate { get; set; }
    public int Adults { get; set; }
    public int[] ChildrenAges { get; set; } = [];
}
