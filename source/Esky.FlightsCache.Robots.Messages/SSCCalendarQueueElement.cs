using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.RobotsProducers.Messages;

public class SSCCalendarQueueElement : QueueElement
{
    public static SSCCalendarQueueElement Create(
        string departure,
        string arrival,
        DateTime departureDate,
        DateTime? returnDepartureDate,
        string supplier)
    {
        var firstDayOfDepartureMonth = new DateTime(departureDate.Year, departureDate.Month, 1);
        var lastDayOfDepartureMonth = firstDayOfDepartureMonth.AddMonths(1).AddDays(-1);
        var firstDayOfReturnMonth = returnDepartureDate.HasValue 
            ? new DateTime(returnDepartureDate.Value.Year, returnDepartureDate.Value.Month, 1) 
            : default;
        var lastDayOfReturnMonth = returnDepartureDate.HasValue 
            ? firstDayOfReturnMonth.AddMonths(1).AddDays(-1) 
            : default;
        return new SSCCalendarQueueElement
        {
            ProviderCode = ProviderCodeEnum.SSCProvider,
            PartnerCode = "ADMIN",
            DepartureCode = departure,
            ArrivalCode = arrival,
            DepartureDay = departureDate,
            PaxConfiguration = "*******",
            Supplier = supplier,
            SourceName = $"SSCCalendarRobot_{supplier}",
            DeleteDepartureDayFrom = firstDayOfDepartureMonth,
            DeleteDepartureDayTo = lastDayOfDepartureMonth,
            Flex = 0,
            // rt
            ReturnDepartureDay = returnDepartureDate,
            IsRoundTrip = returnDepartureDate is not null,
            DeleteReturnDepartureDayFrom = firstDayOfReturnMonth,
            DeleteReturnDepartureDayTo = lastDayOfReturnMonth
        };
    }

    internal static readonly string RobotPrefix = "Robots.SSC.Calendar";
    internal static string QueueNameForUnmatched() => $"{RobotPrefix}.unmatched";
}