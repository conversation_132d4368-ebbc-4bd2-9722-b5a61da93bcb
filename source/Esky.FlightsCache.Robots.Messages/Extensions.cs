using System;

namespace Esky.FlightsCache.RobotsProducers.Messages;

public static class Extensions
{
    public static T To<T>(this QueueElement element) where T : QueueElement, new()
    {
        return new T { InnerElement = element };
    }
    
    public static T ForOutbound<T>(this T element) where T : QueueElement, new()
    {
        var e = new T { InnerElement = element };
        e.DepartureCode = element.DepartureCode;
        e.ArrivalCode = element.ArrivalCode;
        e.DepartureDay = element.DepartureDay;
        e.ReturnDepartureDay = null;
        e.ReturnDepartureDays = null;
        e.DeleteDepartureDayFrom = element.DepartureDay.AddDays(-element.Flex);
        e.DeleteDepartureDayTo = element.DepartureDay.AddDays(element.Flex);
        e.DeleteReturnDepartureDayFrom = null;
        e.DeleteReturnDepartureDayTo = null;
        e.IsRoundTrip = false;
        return e;
    }

    public static T ForInbound<T>(this T element) where T : QueueElement, new()
    {
        if (element.ReturnDepartureDay is null)
        {
            return null;
        }

        var e = new T { InnerElement = element };
        e.DepartureCode = element.ArrivalCode;
        e.ArrivalCode = element.DepartureCode;
        e.DepartureDay = element.ReturnDepartureDay.Value;
        e.ReturnDepartureDay = null;
        e.ReturnDepartureDays = null;
        e.DeleteDepartureDayFrom = element.ReturnDepartureDay.Value.AddDays(-element.Flex);
        e.DeleteDepartureDayTo = element.ReturnDepartureDay.Value.AddDays(element.Flex);
        e.DeleteReturnDepartureDayFrom = null;
        e.DeleteReturnDepartureDayTo = null;
        e.IsRoundTrip = false;
        return e;
    }

    public static T ReverseRoute<T>(this T element) where T : QueueElement, new()
    {
        var e = new T { InnerElement = element };
        e.DepartureCode = element.ArrivalCode;
        e.ArrivalCode = element.DepartureCode;
        return e;
    }

    public static T ForDate<T>(this T element, DateTime date) where T : QueueElement, new()
    {
        var e = new T { InnerElement = element };
        e.DepartureDay = date;
        e.DeleteDepartureDayFrom = date.AddDays(-element.Flex);
        e.DeleteDepartureDayTo = date.AddDays(element.Flex);
        e.ReturnDepartureDay = null;
        e.ReturnDepartureDays = null;
        e.DeleteReturnDepartureDayFrom = null;
        e.DeleteReturnDepartureDayTo = null;
        e.IsRoundTrip = false;
        return e;
    }

    public static T ForDates<T>(this T element, DateTime departureDate, DateTime returnDate) where T : QueueElement, new()
    {
        var e = new T { InnerElement = element };
        e.DepartureDay = departureDate;
        e.ReturnDepartureDay = returnDate;
        e.DeleteDepartureDayFrom = departureDate.AddDays(-element.Flex);
        e.DeleteDepartureDayTo = departureDate.AddDays(element.Flex);
        e.DeleteReturnDepartureDayFrom = returnDate.AddDays(-element.Flex);
        e.DeleteReturnDepartureDayTo = returnDate.AddDays(element.Flex);
        e.IsRoundTrip = true;
        return e;
    }
}