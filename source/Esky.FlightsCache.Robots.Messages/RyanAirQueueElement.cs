namespace Esky.FlightsCache.RobotsProducers.Messages
{
    public class RyanAirQueueElement : QueueElement
    {
        public RyanAirQueueElement ForInbound()
        {
            var element = new RyanAirQueueElement()
            {
                InnerElement = this
            };
            element.DepartureCode = this.ArrivalCode;
            element.ArrivalCode = this.DepartureCode;
            element.DepartureDay = this.ReturnDepartureDay.Value;
            element.ReturnDepartureDay = null;
            element.DeleteDepartureDayFrom = this.ReturnDepartureDay.Value.AddDays(-this.Flex);
            element.DeleteDepartureDayTo = this.ReturnDepartureDay.Value.AddDays(this.Flex);
            element.DeleteReturnDepartureDayFrom = null;
            element.DeleteReturnDepartureDayTo = null;
            element.IsRoundTrip = false;
            return element;
        }

        public RyanAirQueueElement ForOutbound()
        {
            var element = new RyanAirQueueElement()
            {
                InnerElement = this
            };
            element.DepartureCode = this.DepartureCode;
            element.ArrivalCode = this.ArrivalCode;
            element.DepartureDay = this.DepartureDay;
            element.ReturnDepartureDay = null;
            element.DeleteDepartureDayFrom = this.DepartureDay.AddDays(-this.Flex);
            element.DeleteDepartureDayTo = this.DepartureDay.AddDays(this.Flex);
            element.DeleteReturnDepartureDayFrom = null;
            element.DeleteReturnDepartureDayTo = null;
            element.IsRoundTrip = false;
            return element;
        }
    }
}