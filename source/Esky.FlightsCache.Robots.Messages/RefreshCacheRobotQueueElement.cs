using System.Collections.Generic;

namespace Esky.IBE.Robots.Queue.Messages
{
    public class RefreshCacheRobotQueueElement : Esky.FlightsCache.RobotsProducers.Messages.QueueElement
    {
        /// <summary>
        /// Pricing session id. Will be used to track search after failed pricing
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// Leg source Ids if pricing comes from cache source
        /// </summary>
        public List<long> CacheSourceIds { get; set; }

        /// <summary>
        /// Pricing request id
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// Source partner code if pricing comes from cache source
        /// </summary>
        public string CacheSourcePartnerCode { get; set; }

        public RefreshCacheSourceType SourceType { get; set; }

        public enum RefreshCacheSourceType
        {
            Unknown = 0,
            FlightPriceChange = 1,
            FlightNotAvailable = 2
        }
    }
}