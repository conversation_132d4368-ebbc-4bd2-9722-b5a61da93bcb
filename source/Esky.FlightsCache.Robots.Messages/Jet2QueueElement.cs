using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.Robots.Messages;

public class Jet2QueueElement : QueueElement
{
    public static Jet2QueueElement ForFlightsOnRoute(string departure, string arrival, int daysForward, bool isRoundTrip)
    {
        var flex = (int)Math.Ceiling(daysForward / 2.0);
        var searchDate = DateTime.SpecifyKind(DateTime.Today, DateTimeKind.Unspecified).AddDays(flex);
        var deleteFrom = searchDate.AddDays(-flex);
        var deleteTo = searchDate.AddDays(flex);

        return new Jet2QueueElement
        {
            ProviderCode = ProviderCodeEnum.Jet2,
            PaxConfiguration = "*******",
            DepartureCode = departure,
            ArrivalCode = arrival,
            IsRoundTrip = isRoundTrip,
            PartnerCode = "ADMIN",
            SourceName = "Robots.Jet2",
            // jet2 supports search for all flights on specific route
            Flex = flex,
            DepartureDay = searchDate,
            // only outbound/ow flights returned, prices differ
            ReturnDepartureDay = isRoundTrip ? searchDate : null,
            DeleteDepartureDayFrom = deleteFrom,
            DeleteDepartureDayTo = deleteTo,
            // never delete return flights - provider only returns outbound flights for rt search
            DeleteReturnDepartureDayFrom = null,
            DeleteReturnDepartureDayTo = null
        };
    }
    
    public static Jet2QueueElement ForAvailabilityCheck(string departure, string arrival, DateTime searchDate, bool isRoundTrip, int adults)
    {
        return new Jet2QueueElement
        {
            ProviderCode = ProviderCodeEnum.Jet2,
            PaxConfiguration = $"{adults}.0.0.0",
            DepartureCode = departure,
            ArrivalCode = arrival,
            IsRoundTrip = isRoundTrip,
            PartnerCode = "ADMIN",
            SourceName = "Robots.Jet2",
            DepartureDay = searchDate,
            // only outbound/ow flights returned, prices differ
            ReturnDepartureDay = isRoundTrip ? searchDate : null,
        };
    }
}