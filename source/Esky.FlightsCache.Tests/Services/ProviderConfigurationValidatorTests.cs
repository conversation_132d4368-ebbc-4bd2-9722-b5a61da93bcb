using Esky.FlightsCache.ProviderMapping;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Esky.FlightsCache.Tests.Services;
#nullable enable
public class ProviderConfigurationValidatorTests
{
    private readonly ProviderConfigurationValidator _sut = new();

    [Fact]
    public void ValidateCacheProviderConfiguration_WhenValid_ShouldNotThrow()
    {
        _sut.Validate(CreateValidCacheProviderConfiguration());
    }

    [Theory]
    [InlineData(null, true)]
    [InlineData("ABCD", true)]
    [InlineData("A", true)]
    [InlineData("AB", false)]
    [InlineData("*", true)]
    public void ValidateWriteConfiguration_WhenInvalidAirlineCodes_ShouldThrowValidationException(
        string airlineCode, bool throwException)
    {
        CacheProviderConfiguration.WriteConfiguration[] config =
        [
            new() { AirlineCodes = [airlineCode], ProviderCode = 1 }
        ];
        var act = () => ProviderConfigurationValidator.ValidateWriteConfiguration(config);

        if (throwException)
            act.Should().Throw<ValidationException>();
        else
            act.Should().NotThrow();
    }

    [Theory]
    [InlineData(0, 0, 0)]
    [InlineData(-2, -5, -200)]
    [InlineData(300, 400, 500)]
    public void ValidateCacheProviderConfiguration_WhenInvalidProviderCode_ShouldThrowValidationException(
        int cacheProvider, int readProvider, int writeProvider)
    {
        var config = CreateValidCacheProviderConfiguration();
        config.CacheProviderCode = cacheProvider;
        Assert.Throws<ValidationException>(() => _sut.Validate(config));

        config = CreateValidCacheProviderConfiguration();
        config.ReadProviderCode = readProvider;
        Assert.Throws<ValidationException>(() => _sut.Validate(config));

        config = CreateValidCacheProviderConfiguration();
        config.WriteConfigurations[0].ProviderCode = writeProvider;
        Assert.Throws<ValidationException>(() => _sut.Validate(config));
    }

    
    [Fact]
    public void ValidateWriteConfiguration_WhenEmpty_ShouldThrowValidationException()
    {
        CacheProviderConfiguration.WriteConfiguration[] config = [];
        var act = () => ProviderConfigurationValidator.ValidateWriteConfiguration(config);

        act.Should().Throw<ValidationException>();
    }
    
    [Fact]
    public void ValidateWriteConfiguration_WhenDuplicateWriteProviderCodes_ShouldThrowValidationException()
    {
        CacheProviderConfiguration.WriteConfiguration[] config =
        [
            new() { ProviderCode = 10, AirlineCodes = ["AA"] },
            new() { ProviderCode = 10, AirlineCodes = ["AB"] }
        ];
        var act = () => ProviderConfigurationValidator.ValidateWriteConfiguration(config);

        act.Should().Throw<ValidationException>();
    }

    [Fact]
    public void ValidateWriteConfiguration_WhenAirlineNotSpecified_ShouldThrowValidationException()
    {
        CacheProviderConfiguration.WriteConfiguration[] config =
        [
            new() { AirlineCodes = [], ProviderCode = 2 }
        ];
        var act = () => ProviderConfigurationValidator.ValidateWriteConfiguration(config);

        act.Should().Throw<ValidationException>();
    }

    
    [Fact]
    public void ValidateProviderMarginConfiguration_WhenMarginsNotDefined_ThenThrow()
    {
        var config = new ProviderMarginConfiguration { ProviderCode = 1, Margins = [] };
        var act = () => ProviderConfigurationValidator.ValidateMarginConfiguration(config);

        act.Should().Throw<ValidationException>();
    }

    public static TheoryData<string, string[], string, string[], string> DuplicatedSupplierWithAirlineData = new()
    {
        { "*", ["*"], "*", ["*"], "*.*" },
        { "supplier1", ["*"], "supplier1", ["*"], "supplier1.*" },
        { "*", ["AB", "CD"], "*", ["CD", "EF"], "*.CD" },
        { "supplier1", ["AB", "CD"], "supplier1", ["CD", "EF"], "supplier1.CD" }
    };
    
    [Theory]
    [MemberData(nameof(DuplicatedSupplierWithAirlineData))]
    public void ValidateProviderMarginConfiguration_WhenDuplicatedSupplierAirline_ThenThrow(
        string supplier1, string[] airlines1, 
        string supplier2, string[] airlines2, 
        string duplicatedSupplierWithAirline)
    {
        var config = new ProviderMarginConfiguration { ProviderCode = 1, Margins = [
            new ProviderMarginConfiguration.ContextMargin
            {
                Supplier = supplier1,
                AirlineCodes = airlines1,
                Margin = MarginConfiguration.ZeroMargin,
            },
            new ProviderMarginConfiguration.ContextMargin
            {
                Supplier = supplier2,
                AirlineCodes = airlines2,
                Margin = MarginConfiguration.ZeroMargin,
            }
        ] };
        var act = () => 
            ProviderConfigurationValidator.ValidateMarginConfiguration(config);

        act.Should().Throw<ValidationException>().Where(e => e.Message.Contains(duplicatedSupplierWithAirline));
    }
    
    [Theory]
    [InlineData(null, null)]
    [InlineData(null, "FR")]
    [InlineData("FR", "A")]
    [InlineData("FR", "AAA")]
    public void ValidateProviderMarginConfiguration_WhenIncorrectAirlines_ThenThrow(params string[] airlines)
    {
        var config = new ProviderMarginConfiguration
        {
            ProviderCode = 1,
            Margins = [new ProviderMarginConfiguration.ContextMargin
            {
                AirlineCodes = airlines, Supplier = "supplier1", Margin = MarginConfiguration.ZeroMargin
            }]
        };
        var act = () => ProviderConfigurationValidator.ValidateMarginConfiguration(config);

        act.Should().Throw<ValidationException>();
    }
        
    [Theory]
    [InlineData(MarginType.Absolute, 100, "", true)]
    [InlineData(MarginType.Absolute, 100, "currency", true)]
    [InlineData(MarginType.Absolute, 100, "PLN", false)]
    [InlineData(MarginType.Absolute, -100, "", true)]
    [InlineData(MarginType.Absolute, -100, "currency", true)]
    [InlineData(MarginType.Absolute, -100, "PLN", false)]
    [InlineData(MarginType.Absolute, 0, "", false)]
    [InlineData(MarginType.Absolute, 0, "PLN", false)]
    
    [InlineData(MarginType.Relative, 0, "PLN", true)]
    [InlineData(MarginType.Relative, 0, "", false)]
    [InlineData(MarginType.Relative, 101, "", true)]
    [InlineData(MarginType.Relative, -100, "", true)]
    [InlineData(MarginType.Relative, 100, "", false)]
    [InlineData(MarginType.Relative, -99, "", false)]
    public void ValidateMargins_WhenMarginIncorrect_ThenThrow(MarginType marginType, decimal amount, string currency, bool throwException)
    {
        var margin = new MarginConfiguration { MarginType = marginType, Amount = amount, Currency = currency, };
        
        var act = () => ProviderConfigurationValidator.ValidateMargin(margin);
        
        if (throwException)
            act.Should().Throw<ValidationException>();
        else
            act.Should().NotThrow();
    }
    
    [Fact]
    public void ValidateMargins_WhenZeroMargin_ThenNotThrow()
    {
        var margin = MarginConfiguration.ZeroMargin;
        
        var act = () => ProviderConfigurationValidator.ValidateMargin(margin);
        
        act.Should().NotThrow();
    }
    
    [Fact]
    public void ValidateProviderMarginConfiguration_WhenValid_ThenNotThrow()
    {
        var config = new ProviderMarginConfiguration
        {
            ProviderCode = 2,
            Margins =
            [
                new ProviderMarginConfiguration.ContextMargin
                {
                    Supplier = "supplier",
                    AirlineCodes = [ "FR", "RK" ],
                    Margin = new MarginConfiguration { MarginType = MarginType.Absolute, Amount = 5, Currency = "EUR" }
                },
                new ProviderMarginConfiguration.ContextMargin
                {
                    Supplier = "*",
                    AirlineCodes = [ "*" ],
                    Margin = new MarginConfiguration { MarginType = MarginType.Relative, Amount = 10, }
                }
            ]
        };
        
        var act = () => _sut.Validate(config);
        
        act.Should().NotThrow();
    }

    private CacheProviderConfiguration CreateValidCacheProviderConfiguration() => new()
    {
        CacheProviderCode = 58,
        ReadProviderCode = 58,
        WriteConfigurations =
        [
            new CacheProviderConfiguration.WriteConfiguration
            {
                AirlineCodes = ["FR", "OE"],
                ProviderCode = 58,
            },
            new CacheProviderConfiguration.WriteConfiguration
            {
                AirlineCodes = ["W6"],
                ProviderCode = 6,
            }
        ]
    };
}