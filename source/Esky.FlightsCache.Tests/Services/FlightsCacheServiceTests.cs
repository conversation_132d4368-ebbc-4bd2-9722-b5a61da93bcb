using Esky.FlightsCache.Robots.ExternalServices.FlightsCache;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Esky.FlightsCache.Tests.Utils;
using FluentAssertions;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.Extensions;
using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Esky.FlightsCache.Tests.Services
{
    public class FlightsCacheServiceTests
    {
        private readonly MockHttpMessageHandler _httpHandler = new();
        private readonly FlightsCacheService _sut;

        public FlightsCacheServiceTests()
        {
            var httpClient = new HttpClient(_httpHandler);
            var httpClientFactory = Substitute.For<IHttpClientFactory>();
            httpClientFactory.ReturnsForAll(httpClient);
            var config = new FlightsCacheServiceConfiguration { Url = "http://a.b" };
            _sut = new FlightsCacheService(httpClientFactory, config);
        }

        [Fact]
        public async Task GivenHttpHandlerReturningError_WhenSendAnyRequest_ThenThrowsException()
        {
            // arrange
            var request = new SearchTimetablesRequest { Suppliers = [] };

            _httpHandler.Configure(HttpStatusCode.InternalServerError);

            // act
            var resultFunc = () => _sut.GetTimetable(request, CancellationToken.None);

            // assert
            await Assert.ThrowsAsync<Exception>(resultFunc);
        }


        [Fact]
        public async Task Given_When_Then()
        {
            // arrange
            var request = new SearchTimetablesRequest
            {
                Suppliers = new[] { "wizzair" },
                ExceptSuppliers = new[] { "something" },
                MaxDepartureDate = DateTime.UtcNow.AddDays(10),
                MinDepartureDate = DateTime.UtcNow,
                MinAge = TimeSpan.FromHours(2),
            };
            var response = new SearchTimetablesResponseItem[]
            {
                new ()
                {
                    DepartureAirportCode = "KTW",
                    ArrivalAirportCode = "LTN",
                    Dates = new [] {DateTime.UtcNow, DateTime.UtcNow.AddDays(2)}
                }
            };

            _httpHandler.Configure(JsonConvert.SerializeObject(response), HttpStatusCode.OK);

            // act
            var result = await _sut.GetTimetable(request, CancellationToken.None);

            // assert
            result.Should().NotBeNullOrEmpty();
            result.Should().BeEquivalentTo(response);
            request.Should().BeEquivalentTo(JsonConvert.DeserializeObject<SearchTimetablesRequest>(_httpHandler.Input));
        }
    }
}
