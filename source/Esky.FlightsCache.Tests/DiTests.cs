using Esky.FlightsCache.RobotsProducers.IoC;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Esky.FlightsCache.Tests;

public class DiTests
{
    [Fact]
    public void WhenConfigureServiceBus_ThenGetServiceBus()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .AddJsonFile("appsettings.Development.json")
            .Build();

        var serviceProvider = new ServiceCollection()
            .ConfigureServiceBus(configuration)
            .BuildServiceProvider(new ServiceProviderOptions { ValidateOnBuild = true, ValidateScopes = true });

        var service = serviceProvider.GetService<IBus>();

        Assert.IsAssignableFrom<IBus>(service);
    }
}