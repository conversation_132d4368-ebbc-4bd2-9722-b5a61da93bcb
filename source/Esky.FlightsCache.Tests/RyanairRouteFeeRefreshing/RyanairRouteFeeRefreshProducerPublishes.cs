using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.RyanairRouteFeeRefreshing;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using FluentAssertions;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Time.Testing;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.RyanairRouteFeeRefreshing;

public class RyanairRouteFeeRefreshProducerPublishes : IAsyncLifetime
{
    private readonly ITestHarness _testHarness;
    private readonly RyanAirRouteFeeRefreshProducer _producer;
    private readonly ITimetableServiceClient _timetables = Substitute.For<ITimetableServiceClient>();
    private readonly FakeTimeProvider _timeProvider;

    public RyanairRouteFeeRefreshProducerPublishes()
    {
        var serviceProvider = new ServiceCollection()
            .AddSingleton(_timetables)
            .AddScoped<RyanAirRouteFeeRefreshProducer>()
            .AddMassTransitTestHarness()
            .AddSingleton<TimeProvider>(new FakeTimeProvider(new DateTimeOffset(new DateTime(2024, 1, 1))))
            .BuildServiceProvider();

        _testHarness = serviceProvider.GetTestHarness();
        _producer = serviceProvider.GetRequiredService<RyanAirRouteFeeRefreshProducer>();
        _timeProvider = serviceProvider.GetRequiredService<TimeProvider>() as FakeTimeProvider;
    }

    public Task InitializeAsync() => _testHarness.Start();
    public Task DisposeAsync() => _testHarness.Stop();

    [Fact]
    public async Task AllRoutesFromConnectionNetworksWithDepartureDateInMonthOrLater()
    {
        // Arrange
        var utcNow = _timeProvider.GetUtcNow().DateTime;
        _timetables
            .GetConnectionNetworkByAirline("FR")
            .Returns(
                [
                    CreateConnectionNetwork(departure: "WAW", arrivals: ["LTN", "FRA"]),
                    CreateConnectionNetwork(departure: "LTN", arrivals: ["WAW"]),
                    CreateConnectionNetwork(departure: "WRO", arrivals: ["FRA", "LTN", "STN"])
                ]
            );
        _timetables
            .GetConnectionNetworkByAirline("RK")
            .Returns([CreateConnectionNetwork(departure: "STN", arrivals: ["KTW", "POZ"])]);
        _timetables
            .GetFlyingDays(Arg.Any<string>(), Arg.Any<string>(), Arg.Is<string[]>(a => a.SequenceEqual(new[] { "FR", "RK" })))
            .Returns([utcNow.AddDays(14), utcNow.AddMonths(1)]);
        _timetables
            .GetFlyingDays("WRO", "FRA", Arg.Is<string[]>(a => a.SequenceEqual(new[] { "FR", "RK" })))
            .Returns([utcNow.AddDays(14)]); // not published
        _timetables
            .GetFlyingDays("LTN", "WAW", Arg.Is<string[]>(a => a.SequenceEqual(new[] { "FR", "RK" })))
            .Returns([utcNow.AddDays(14), utcNow.AddMonths(1).AddDays(3)]);

        // Act
        await _producer.Produce(CancellationToken.None);

        // Assert
        var messages = _testHarness
            .Published
            .Select<RyanairRefreshRouteFee>()
            .Select(e => e.Context.Message);

        var departureDate = DateOnly.FromDateTime(utcNow.AddMonths(1));
        messages
            .Should()
            .BeEquivalentTo(
                new RyanairRefreshRouteFee[]
                {
                    new() { Departure = "WAW", Arrival = "LTN", DepartureDate = departureDate },
                    new() { Departure = "WAW", Arrival = "FRA", DepartureDate = departureDate },
                    new() { Departure = "LTN", Arrival = "WAW", DepartureDate = departureDate.AddDays(3) },
                    new() { Departure = "WRO", Arrival = "LTN", DepartureDate = departureDate },
                    new() { Departure = "WRO", Arrival = "STN", DepartureDate = departureDate },
                    new() { Departure = "STN", Arrival = "KTW", DepartureDate = departureDate },
                    new() { Departure = "STN", Arrival = "POZ", DepartureDate = departureDate }
                },
                opt => opt.WithStrictOrdering()
            );
    }

    private static ConnectionNetwork CreateConnectionNetwork(string departure, IEnumerable<string> arrivals)
    {
        return new ConnectionNetwork
        {
            DepartureAirportCode = departure,
            ArrivalAirportCodes = arrivals
                .Select(code => new ConnectionNetworkArrivalAirport { ArrivalCode = code, Months = [] })
                .ToList()
        };
    }
}