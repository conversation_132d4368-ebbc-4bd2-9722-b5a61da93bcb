using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.Tests.RobotsProducers;

public class RelativeDateTests
{
    private static readonly DateTime _today = DateTime.UtcNow.Date;
    public static readonly IReadOnlyList<object[]> RelativeDateTestData = new List<object[]>
    {
        new object[] { "3", _today.AddDays(3), null },
        new object[] { "-5", _today.AddDays(-5), null },
        new object[] { "-5", _today.AddDays(10).AddDays(-5), _today.AddDays(10) },
        new object[] { _today.AddDays(2), _today.AddDays(2), null },
        new object[] { _today.AddDays(2), _today.AddDays(2), _today.AddDays(2) },
        new object[] { _today.AddDays(-5), _today.AddDays(-5), null },
        new object[] { _today.AddDays(-5), _today.AddDays(-5), _today.AddDays(-2) },
    };

    [Theory, MemberData(nameof(RelativeDateTestData))]
    public void TestRelativeDates(string str, DateTime expected, DateTime? relativeTo)
    {
        var relativeDate = RelativeDate.Parse(str);
        Assert.Equal(expected, relativeDate.GetDate(relativeTo));
    }

    [Theory]
    [InlineData("x")]
    [InlineData("1x")]
    [InlineData("2023-06-06T")]
    [InlineData("2023-s06-06")]
    public void ParseThrowsArgumentExceptionOnInvalidString(string inputString)
    {
        Assert.Throws<ArgumentException>(() => RelativeDate.Parse(inputString));
    }

    [Fact]
    public void Default_ShouldBe_RelativeDaysZero()
    {
        RelativeDate relativeDate = default;

        relativeDate.RelativeDays.Should().Be(0);
        relativeDate.IsRelative.Should().Be(true);
        relativeDate.GetDate().Should().Be(DateTime.UtcNow.Date);
        relativeDate.GetDate(DateTime.UtcNow.Date.AddDays(3)).Should().Be(DateTime.UtcNow.Date.AddDays(3));
    }

    [Fact]
    public void NewtonsoftJsonSerializationTest()
    {
        var settings = new JsonSerializerSettings
        {
            Converters = new List<JsonConverter> { new RelativeDateJsonSerializer() }
        };

        var jsonByDay = JsonConvert.SerializeObject(new RelativeDate(20), settings);
        var parsedByDay = JsonConvert.DeserializeObject<RelativeDate>(jsonByDay, settings);

        var jsonByDate = JsonConvert.SerializeObject(new RelativeDate(DateTime.UtcNow.Date.AddDays(5)), settings);
        var parsedByDate = JsonConvert.DeserializeObject<RelativeDate>(jsonByDate, settings);

        Assert.Equal(new RelativeDate(20), parsedByDay);
        Assert.Equal(new RelativeDate(DateTime.UtcNow.Date.AddDays(5)), parsedByDate);
    }
}
