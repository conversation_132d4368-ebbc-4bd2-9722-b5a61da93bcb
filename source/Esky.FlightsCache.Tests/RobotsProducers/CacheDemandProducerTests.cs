using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using FluentAssertions;
using System.Linq;
using Xunit;

namespace Esky.FlightsCache.Tests.RobotsProducers
{
    public class CacheDemandProducerTests
    {
        [Fact]
        public void JobConfigurationParameter_MustBeOnTheSamePostionInAllGenerateMethods()
        {
            var producerType = typeof(CacheDemandProducer);
            var configParamIndexes = producerType
                .GetMethods()
                .Where(m => m.Name == nameof(CacheDemandProducer.Generate))
                .Select(m => m
                    .GetParameters()
                    .Select((e, i) => (Parameter: e, Index: i))
                    .First(e => e.Parameter.ParameterType == typeof(CacheDemandJobConfiguration))
                    .Index)
                .Distinct()
                .ToArray();

            configParamIndexes.Length.Should().Be(1);
        }
    }
}
