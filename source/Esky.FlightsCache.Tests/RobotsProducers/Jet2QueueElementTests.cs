using Esky.FlightsCache.Robots.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.Tests.RobotsProducers;

public class Jet2QueueElementTests
{
    [Theory]
    [InlineData(10, 5, true)] // even days forward
    [InlineData(11, 6, false)] // odd days forward
    public void ForFlightsOnRoute_ShouldReturnCorrectJet2QueueElement(int daysForward, int expectedFlex, bool isRoundTrip)
    {
        const string departure = "LHR";
        const string arrival = "JFK";
        var expectedSearchDate = DateTime.Today.AddDays(expectedFlex);

        var result = Jet2QueueElement.ForFlightsOnRoute(departure, arrival, daysForward, isRoundTrip);

        result.ProviderCode.Should().Be(ProviderCodeEnum.Jet2);
        result.PaxConfiguration.Should().Be("*******");
        result.DepartureCode.Should().Be(departure);
        result.ArrivalCode.Should().Be(arrival);
        result.IsRoundTrip.Should().Be(isRoundTrip);
        result.PartnerCode.Should().Be("ADMIN");
        result.SourceName.Should().Be("Robots.Jet2");
        result.Flex.Should().Be(expectedFlex);
        result.DepartureDay.Should().Be(expectedSearchDate);
        result.DepartureDay.Kind.Should().Be(DateTimeKind.Unspecified);
        if(isRoundTrip)
        {
            result.ReturnDepartureDay.Should().Be(expectedSearchDate);
        }
        else
        {
            result.ReturnDepartureDay.Should().BeNull();
        }
        result.DeleteDepartureDayFrom.Should().Be(expectedSearchDate.AddDays(-expectedFlex));
        result.DeleteDepartureDayTo.Should().Be(expectedSearchDate.AddDays(expectedFlex));
        result.DeleteReturnDepartureDayFrom.Should().BeNull();
        result.DeleteReturnDepartureDayTo.Should().BeNull();
    }
}