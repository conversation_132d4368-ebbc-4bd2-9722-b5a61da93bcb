using Cronos;
using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.Publishers;
using FluentAssertions;
using FluentAssertions.Extensions;
using NSubstitute;
using System;
using Xunit;

namespace Esky.FlightsCache.Tests.RobotsProducers
{
    public class EvenlyDistributionParametersBuilderTests
    {
        private readonly DateTimeOffset _someMidnightTimestamp = new(2000, 1, 1, 0, 00, 00, TimeSpan.Zero);
        private readonly string _someCronExpression = "0 * * * *";

        [Theory]
        [InlineData(4, 1, 4)]
        [InlineData(5, 1, 5)]
        [InlineData(6, 2, 3)]
        [InlineData(10, 2, 5)]
        [InlineData(11, 3, 4)]
        [InlineData(36, 8, 5)]
        [InlineData(39, 8, 5)]
        [InlineData(54, 11, 5)]
        public void Given5MinutesPeriodAnd1MinuteInterval_WhenMaxProcessingWindowIsBiggerThanPeriod_ThenSplitIntoUpTo5Chunks(int elementsCount, int expectedChunkSize, int expectedChunksCount)
        {
            // arrange
            var cronExpression = "*/5 * * * *"; // every 5 minute
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(1),
                MaxProcessingWindow = TimeSpan.FromHours(24)
            };
            var options = CreateOptions(cronExpression, elementsCount);

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, _someMidnightTimestamp);

            // assert
            interval.Should().Be(TimeSpan.FromMinutes(5) / expectedChunksCount, "executed every 5 minutes");
            chunkSize.Should().Be(expectedChunkSize);
            chunksCount.Should().Be(expectedChunksCount);
        }

        [Theory]
        [InlineData(5, 1, 5)]
        [InlineData(9, 1, 9)]
        [InlineData(10, 1, 10)]
        [InlineData(11, 2, 6)]
        [InlineData(19, 2, 10)]
        [InlineData(20, 2, 10)]
        [InlineData(21, 3, 7)]
        [InlineData(115, 12, 10)]
        [InlineData(209, 21, 10)]
        public void GivenLongPeriod_WhenMaxProcessingWindowIsSmallerThanPeriod_ThenSplitIntoUpTo10Chunks(int elementsCount, int expectedChunkSize, int expectedChunksCount)
        {
            // arrange
            var cronExpression = "0 20 * * *"; // at 20:00
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(1),
                MaxProcessingWindow = TimeSpan.FromMinutes(10)
            };
            var options = CreateOptions(cronExpression, elementsCount);

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, _someMidnightTimestamp);

            // assert
            interval.Should().Be(TimeSpan.FromMinutes(10) / expectedChunksCount, "maxProcessingWindow is 10 minutes");
            chunkSize.Should().Be(expectedChunkSize);
            chunksCount.Should().Be(expectedChunksCount);
        }

        [Fact]
        public void OneChunk_HasIntervalFromSettings()
        {
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(10),
                MaxProcessingWindow = TimeSpan.FromHours(10)
            };
            var options = CreateOptions("0 0 * * *", 1);

            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, _someMidnightTimestamp);

            interval.Should().Be(settings.Interval);
            chunkSize.Should().Be(1);
            chunksCount.Should().Be(1);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(10)]
        [InlineData(100)]
        [InlineData(1000)]
        public void GivenNormalSetup_WhenTimeAvailableToNextOccurenceSmallerThanInterval_ThenGoesInto1Chunk(int elementsCount)
        {
            // arrange
            var cronExpression = "*/5 * * * *"; // every 5 minute
            var currentTimestamp = new DateTimeOffset(2000, 1, 1, 0, 04, 50, TimeSpan.Zero);
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(1),
                MaxProcessingWindow = TimeSpan.FromHours(24)
            };
            var options = CreateOptions(cronExpression, elementsCount);

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, currentTimestamp);

            // assert
            interval.Should().Be(settings.Interval);
            chunkSize.Should().Be(elementsCount == 0 ? 1 : elementsCount);
            chunksCount.Should().Be(1);
        }

        [Theory]
        [InlineData(0, 1)]  // interval = 0
        [InlineData(1, 0)]  // maxProcessingWindow = 0
        [InlineData(0, 0)]  // interval = 0 && maxProcessingWindow = 0
        [InlineData(5, 1)]  // interval > maxProcessingWindow
        public void GivenIncorrectSetup_WhenIntervalAndMaxProcessingWindowAreWrong_ThenGoesInto1Chunk(int intervalInMinutes, int maxProcessingWindowInMinutes)
        {
            // arrange
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(intervalInMinutes),
                MaxProcessingWindow = TimeSpan.FromMinutes(maxProcessingWindowInMinutes)
            };
            var options = CreateOptions(_someCronExpression, 1000);

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, _someMidnightTimestamp);

            // assert
            interval.Should().Be(settings.Interval);
            chunkSize.Should().Be(1000);
            chunksCount.Should().Be(1);
        }

        [Theory]
        [InlineData("incorrect cron expression")]
        [InlineData(null)]
        public void GivenCorrectSettingsForOneChunk_WhenIncorrectCronExpression_ThenFallbackToSingleChunk(string cronExpression)
        {
            // arrange
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(1),
                MaxProcessingWindow = TimeSpan.FromHours(24)
            };
            var options = CreateOptions(cronExpression, 1000);

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, _someMidnightTimestamp);

            // assert
            interval.Should().Be(settings.Interval);
            chunkSize.Should().Be(1000);
            chunksCount.Should().Be(1);
        }

        [Theory]
        [InlineData(8, 8, 1)]
        [InlineData(50_000, 25_000, 2)]
        [InlineData(2_000_000, 40_000, 50)]
        public void DistributeChunksUpToNextOccurrence(int elementsCount, int expectedChunksCount, int expectedChunkSize)
        {
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromSeconds(30),
                MaxProcessingWindow = TimeSpan.FromDays(15)
            };

            var utcNow = DateTime.UtcNow;
            var firstDayOfNextMonth = new DateTime(utcNow.Year, utcNow.Month, 1).AddMonths(1);
            var firstOccurrence = new DateTime(firstDayOfNextMonth.Year, firstDayOfNextMonth.Month, 9).AsUtc();
            var secondOccurrence = new DateTime(firstDayOfNextMonth.Year, firstDayOfNextMonth.Month, 23).AsUtc();
            var options = CreateOptions("0 0 9,23 * *", elementsCount);

            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, new DateTimeOffset(firstOccurrence));

            firstOccurrence.Add(interval * chunksCount).Should().Be(secondOccurrence);
            interval.Should().Be((secondOccurrence - firstOccurrence) / expectedChunksCount);
            chunkSize.Should().Be(expectedChunkSize);
            chunksCount.Should().Be(expectedChunksCount);
        }

        [Theory]
        [InlineData(9, 21, 12)]
        [InlineData(18, 21, 3)]
        [InlineData(21, 9, 12)]
        [InlineData(23, 5, 6)]
        public void GivenActivityPeriod_WhenNextOccurrenceAfterActivityPeriod_ThenDistributedTillEndOfActivityPeriod(int startHour, int activeToHour, int expectedDurationInHours)
        {
            // arrange
            var cronExpression = $"0 {startHour} * * *";
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(10),
                MaxProcessingWindow = TimeSpan.FromHours(24)
            };
            var elementsCount = 100_000;
            var startTimeStamp = new DateTimeOffset(2000, 1, 1, startHour, 0, 0, TimeSpan.Zero);
            var options = CreateOptions(cronExpression, elementsCount, activeFrom: new TimeOnly(startHour, 0), activeTo: new TimeOnly(activeToHour, 0));

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, startTimeStamp);

            // assert
            interval.Should().Be(settings.Interval);
            chunksCount.Should().Be((int)(expectedDurationInHours * 60 / settings.Interval.TotalMinutes));
        }
        [Theory]
        [InlineData(9, 12, 21, 3)]
        [InlineData(18, 20, 21, 2)]
        [InlineData(21, 3, 9, 6)]
        [InlineData(23, 2, 5, 3)]
        public void GivenActivityPeriod_WhenNextOccurrenceBeforeActivityPeriodEnd_ThenDistributedTillNextOccurence(int startHour, int nextStartHour, int activeToHour, int expectedDurationInHours)
        {
            // arrange
            var cronExpression = $"0 {startHour},{nextStartHour} * * *";
            var settings = new RobotsProducerEvenlyDistributionSettings
            {
                Interval = TimeSpan.FromMinutes(10),
                MaxProcessingWindow = TimeSpan.FromHours(24)
            };
            var elementsCount = 100_000;
            var startTimeStamp = new DateTimeOffset(2000, 1, 1, startHour, 0, 0, TimeSpan.Zero);
            var options = CreateOptions(cronExpression, elementsCount, activeFrom: new TimeOnly(startHour, 0), activeTo: new TimeOnly(activeToHour, 0));

            // act
            var (interval, chunkSize, chunksCount) = new EvenlyDistributionParametersBuilder(settings).GetEvenlyDistributionParameters(options, startTimeStamp);

            // assert
            interval.Should().Be(settings.Interval);
            chunksCount.Should().Be((int)(expectedDurationInHours * 60 / settings.Interval.TotalMinutes));
        }

        private static EvenlyDistributionPublisherOptions CreateOptions(string cron, int elementsCount, TimeOnly? activeFrom = null, TimeOnly? activeTo = null)
        {
            return new EvenlyDistributionPublisherOptions
            {
                CronExpression = cron,
                ActiveFrom = activeFrom ?? TimeOnly.MinValue,
                ActiveTo = activeTo ?? TimeOnly.MaxValue,
                ElementsCount = elementsCount,
                JobName = "JobName"
            };
        }
    }
}
