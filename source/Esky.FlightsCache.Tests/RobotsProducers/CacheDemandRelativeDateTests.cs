using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;
using System.Collections.Generic;
using Esky.FlightsCache.Robots;
using Xunit;

namespace Esky.FlightsCache.Tests.RobotsProducers;

public class CacheDemandRelativeDateTests
{
    private static readonly DateTime _today = DateTime.UtcNow.Date;
    public static readonly IReadOnlyList<object[]> CacheDemandRelativeDateTestData = new List<object[]>
    {
        new object[] { "3", "6", _today.AddDays(3), _today.AddDays(3).AddDays(6) },
        new object[] { _today.AddDays(2), "6", _today.AddDays(2), _today.AddDays(2).AddDays(6) },
        new object[] { "4", _today.AddDays(10), _today.AddDays(4), _today.AddDays(10) },
        new object[] { _today.AddDays(1), _today.AddDays(10), _today.AddDays(1), _today.AddDays(10) },
        new object[] { _today.AddDays(-1), "10", _today.AddDays(1), _today.AddDays(1).AddDays(10) },
        new object[] { _today, "10", _today.AddDays(1), _today.AddDays(1).AddDays(10) }
    };

    [Theory, MemberData(nameof(CacheDemandRelativeDateTestData))]
    public void CacheDemandRelativeDates_ResolvesToCorrectDateRange(string fromStr, string toStr, DateTime expectedFrom, DateTime expectedTo)
    {
        var from = RelativeDate.Parse(fromStr);
        var to = RelativeDate.Parse(toStr);

        var fromDate = from.GetDate().ToTomorrowIfPastOrToday();
        var toDate = to.GetDate(fromDate);

        var departureDates = new CacheDemandModelInput
        {
            DepartureDateFrom = from,
            DepartureDateTo = to,
            PartnerCode = "ESKY",
            DepartureAirportCode = "KTW",
            ArrivalAirportCode = "STN"
        }.GetDepartureDateRange();
        var totalDays = toDate.Subtract(fromDate).TotalDays + 1;

        for (var i = 0; i < totalDays; i++)
        {
            Assert.Contains(fromDate.AddDays(i), departureDates);
        }

        Assert.Equal(expectedFrom, fromDate);
        Assert.Equal(expectedTo, toDate);
        Assert.Equal(totalDays, departureDates.Count);
    }
}