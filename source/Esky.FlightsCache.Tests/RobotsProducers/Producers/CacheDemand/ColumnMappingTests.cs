using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using FluentAssertions;
using System;
using Xunit;

namespace Esky.FlightsCache.Tests.RobotsProducers.Producers.CacheDemand
{
    public class ColumnMappingTests
    {
        [Fact]
        public void Parse_WhenHeaderIsEmpty_ThenUseDeafultOrder()
        {
            // Arrange
            // Act
            var columnMapping = ColumnMapping.Parse(string.Empty);

            // Assert
            columnMapping.AirlineFilter.Should().Be(0);
            columnMapping.ProviderCode.Should().Be(1);
            columnMapping.PartnerCode.Should().Be(2);
            columnMapping.Departure.Should().Be(3);
            columnMapping.Arrival.Should().Be(4);
            columnMapping.DepartureFrom.Should().Be(5);
            columnMapping.DepartureTo.Should().Be(6);
            columnMapping.TripType.Should().Be(7);
            columnMapping.MinStayLength.Should().Be(8);
            columnMapping.MaxStayLength.Should().Be(9);
            columnMapping.OfficeId.Should().Be(-1);
            columnMapping.Requests.Should().Be(10);
            columnMapping.CollectedDates.Should().Be(-1);
            columnMapping.CollectedFlights.Should().Be(-1);
            columnMapping.CollectedDatesWithMoreThan1Stop.Should().Be(-1);
            
        }

        [Fact]
        public void Parse_WhenAllRequiredHeaders_ThenIndexColumnsByHeaderOrder()
        {
            // Arrange
            const string header = "PartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tRequests\tAirlineFilter\tProviderCode\tCollectedDates\tCollectedFlights\tCollectedDatesWithMoreThan1Stop";

            // Act
            var columnMapping = ColumnMapping.Parse(header);

            // Assert
            columnMapping.AirlineFilter.Should().Be(10);
            columnMapping.ProviderCode.Should().Be(11);
            columnMapping.PartnerCode.Should().Be(0);
            columnMapping.Departure.Should().Be(1);
            columnMapping.Arrival.Should().Be(2);
            columnMapping.DepartureFrom.Should().Be(3);
            columnMapping.DepartureTo.Should().Be(4);
            columnMapping.TripType.Should().Be(5);
            columnMapping.MinStayLength.Should().Be(6);
            columnMapping.MaxStayLength.Should().Be(7);
            columnMapping.OfficeId.Should().Be(8);
            columnMapping.Requests.Should().Be(9);
            columnMapping.CollectedDates.Should().Be(12);
            columnMapping.CollectedFlights.Should().Be(13);
            columnMapping.CollectedDatesWithMoreThan1Stop.Should().Be(14);
        }

        [Fact]
        public void Parse_WhenOptionalHeadersAreMissing_ThenOfficeIdColumnIsMissing_RequestsIsFirstEmpty()
        {
            // Arrange
            const string header = "PartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tAirlineFilter\tProviderCode";

            // Act
            var columnMapping = ColumnMapping.Parse(header);

            // Assert
            columnMapping.OfficeId.Should().Be(-1);
            columnMapping.Requests.Should().Be(10);
        }

        [Theory]
        [InlineData("AirlineFilter")]
        [InlineData("ProviderCode")]
        [InlineData("PartnerCode")]
        [InlineData("Departure")]
        [InlineData("Arrival")]
        [InlineData("DepartureFrom")]
        [InlineData("DepartureTo")]
        [InlineData("TripType")]
        [InlineData("MinStayLength")]
        [InlineData("MaxStayLength")]

        public void Parse_WhenRequiredHeaderIsMissing_ThenThrowValidationException(string requiredHeaderLine)
        {
            // Arrange
            const string defaultHeaderLine = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tRequests";
            string headerLine = defaultHeaderLine.Replace(requiredHeaderLine, "xxx");

            // Act
            var parseAction = () => ColumnMapping.Parse(headerLine);

            // Assert
            parseAction.Should().Throw<ArgumentException>().WithParameterName(requiredHeaderLine);
        }
    }
}
