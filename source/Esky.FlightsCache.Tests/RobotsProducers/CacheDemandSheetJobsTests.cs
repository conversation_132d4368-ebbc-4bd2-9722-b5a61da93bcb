using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Hangfire;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.RobotsProducers
{
    public class CacheDemandSheetJobsTests
    {
        private readonly IFlightOffersRepository _flightOffersRepository;
        private readonly IAirportsRepository _airportsRepository;
        private readonly ICacheDemandProducer _cacheDemandProducer;
        private readonly IGoogleRepository _googleDriveRepository;
        private readonly CacheDemandService _service;

        public CacheDemandSheetJobsTests()
        {
            var recurringJobManager = Substitute.For<IRecurringJobManager>();
            var backgroundJobClient = Substitute.For<IBackgroundJobClient>();
            var jobStorage = Substitute.For<JobStorage>();
            _googleDriveRepository = Substitute.For<IGoogleRepository>();
            _cacheDemandProducer = Substitute.For<ICacheDemandProducer>();
            _flightOffersRepository = Substitute.For<IFlightOffersRepository>();
            _airportsRepository = Substitute.For<IAirportsRepository>();
            var logger = Substitute.For<ILogger<CacheDemandService>>();

            _service = new CacheDemandService(recurringJobManager, backgroundJobClient, jobStorage, _cacheDemandProducer, _googleDriveRepository, _flightOffersRepository, _airportsRepository, logger);
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_EmptySheetShouldReturnError()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>());
            _cacheDemandProducer.DryRun(string.Empty, string.Empty).ReturnsForAnyArgs(new CacheDemandJob());

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(false);
            result.Errors.Should().Contain("There are no rows");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_DuplicatedRowsShouldReturnError()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "", "", "" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "14" },
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "14" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {

                                DepartureDateFrom = default,
                                DepartureDateTo = default,
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN"
                            }
                        },
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                DepartureDateFrom = default,
                                DepartureDateTo = default,
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN"
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(false);
            result.Errors.Should().Contain("Duplicated rows");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_StayLenghtMoreThan30DaysShouldReturnWarning()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "", "", "" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                MinStayLength = 3,
                                MaxStayLength = 40,
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(true);
            result.Warnings.Should().Contain("Stay length > 30 days");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_RowGeneratesMoreThan1500RequestsShouldReturnWarning()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "", "", "" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            },
                            RequestsGenerated = 1501
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(true);
            result.Warnings.Should().Contain("Row generates > 1500 requests");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_JobGeneratesMoreThan100001RequestsShouldReturnWarning()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "", "", "" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            },
                            RequestsGenerated = 100_001 }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(true);
            result.Warnings.Should().Contain("Job generates > 100 000 requests");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_InvalidCronShouldReturnError()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 *", "", "", "", "" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(false);
            result.Errors.Should().Contain("Invalid cron");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_EndDateEarlierThanStartDateShouldReturnError()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "2025-01-01", "", "2024-01-01" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(false);
            result.Errors.Should().Contain("StartDate > EndDate");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_EndDateEarlierThanNowShouldReturnError()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", "2025-01-01", "", "2020-01-01" },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(false);
            result.Errors.Should().Contain("EndDate < Now");
        }

        [Fact]
        public async Task CacheDemandSheetJobValidation_StartDateOrEndDateBiggerThanNowWith13MonthsShouldReturnWarning()
        {
            _googleDriveRepository.GetValues(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new List<IList<object>>
                {
                    new List<object>{"", "0 0 * * *", "", DateTime.Now.AddMonths(14).ToString("yyyy-MM-dd"), "", DateTime.Now.AddMonths(16).ToString("yyyy-MM-dd") },
                    new List<object>(),
                    new List<object> { "", 80, "ESKY", "WAW", "BCN", "10", "270", "RT", "3", "40" }
                });
            _cacheDemandProducer.DryRun(string.Empty, string.Empty)
                .ReturnsForAnyArgs(new CacheDemandJob
                {
                    Items = new List<CacheDemandJobItem>
                    {
                        new()
                        {
                            InputModel = new CacheDemandModelInput
                            {
                                PartnerCode = "ESKY",
                                DepartureAirportCode = "KTW",
                                ArrivalAirportCode = "STN",
                                DepartureDateFrom = default,
                                DepartureDateTo = default
                            }
                        }
                    }
                });

            var result = await _service.ValidateSheetJob(string.Empty, string.Empty);

            result.IsValid.Should().Be(true);
            result.Warnings.Should().Contain("StartDate > Now + 13 months");
            result.Warnings.Should().Contain("EndDate > Now + 13 months");
        }
        
        [Fact]
        public async Task CalculateSheetJob_ShouldReturnValidResult()
        {
            const string spreadsheetId = "spreadsheetId";
            const string sheetName = "sheetName";
            var spreadsheets = new List<Spreadsheet>
            {
                new()
                {
                    Id = spreadsheetId,
                    Directory = "directory",
                    Name = "name",
                    Sheets = new List<Sheet>
                    {
                        new()
                        {
                            Id = 123456,
                            Name = sheetName
                        }
                    }
                }
            };
            var values = new List<IList<object>>
            {
                new List<object> { "" },
                new List<object> { "PartnerCode","Departure","Arrival","DepartureFrom","DepartureTo","TripType","MinStayLength","MaxStayLength","OfficeId","Requests","AirlineFilter","ProviderCode","CollectedDates","CollectedFlights","CollectedDatesWithMoreThan1Stop"},
            };

            _googleDriveRepository.GetSpreadsheets().Returns(spreadsheets);
            _googleDriveRepository.GetValues(spreadsheetId, sheetName).Returns(values);
            _airportsRepository.GetAirportCodesAsync(Arg.Any<string>()).Returns(new List<string> { "Code1", "Code2" });
            _flightOffersRepository.GetSourceStatsOw(Arg.Any<string>(), Arg.Any<string[]>()).Returns((1, 2, 3));
            _flightOffersRepository.GetSourceStatsRt(Arg.Any<string>(), Arg.Any<string[]>()).Returns((1, 2, 3));

            var result = await _service.CalculateJobCoverage(spreadsheetId, sheetName);

            Assert.True(result.IsValid);
            Assert.Null(result.Errors);
        }
        
        [Fact]
        public async Task CalculateSheetJob_ShouldReturnNotalidResult()
        {
            const string spreadsheetId = "spreadsheetId";
            const string sheetName = "sheetName";
            var spreadsheets = new List<Spreadsheet>
            {
                new()
                {
                    Id = spreadsheetId,
                    Directory = "directory",
                    Name = "name",
                    Sheets = new List<Sheet>
                    {
                        new()
                        {
                            Id = 123456,
                            Name = sheetName
                        }
                    }
                }
            };
            var values = new List<IList<object>>
            {
                new List<object> { "" },
                new List<object> { "PartnerCode","Departure","Arrival","DepartureFrom","DepartureTo","TripType","MinStayLength","MaxStayLength","OfficeId","Requests","AirlineFilter","ProviderCode"},
            };

            _googleDriveRepository.GetSpreadsheets().Returns(spreadsheets);
            _googleDriveRepository.GetValues(spreadsheetId, sheetName).Returns(values);
            _airportsRepository.GetAirportCodesAsync(Arg.Any<string>()).Returns(new List<string> { "Code1", "Code2" });
            _flightOffersRepository.GetSourceStatsOw(Arg.Any<string>(), Arg.Any<string[]>()).Returns((1, 2, 3));
            _flightOffersRepository.GetSourceStatsRt(Arg.Any<string>(), Arg.Any<string[]>()).Returns((1, 2, 3));

            var result = await _service.CalculateJobCoverage(spreadsheetId, sheetName);

            Assert.False(result.IsValid);
            Assert.NotNull(result.Errors);
            Assert.NotEmpty(result.Errors);
        }
    }
}
