using Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

namespace Esky.FlightsCache.Tests;

public class RobotGroupTests
{
    [Fact]
    public void EqualityTest()
    {
        var record1 = new HotelInventoryRobotsConfigFeedBigRecord
        {
            HotelMetaCode = 100,
            Providers = "xyz,abc",
            Sales = 1,
            Market = "TC",
            TC_Tag = true,
            Portfolio = true
        };
        var record2 = record1 with { };

        var group1 = RobotGroup.From(record1);
        var group2 = RobotGroup.From(record2);

        group1.Should().NotBeSameAs(group2, "Should not be the same reference.");
        group1.Should().Be(group2, "Groups should be equal when all properties match.");
    }
}