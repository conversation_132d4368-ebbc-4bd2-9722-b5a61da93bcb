using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using System.Linq;

namespace Esky.FlightsCache.Tests;

public class FlexTests
{
    [Theory, InlineData(-1), InlineData(0)]
    public void InvalidFlex_ReturnsSelf(int flex)
    {
        RouteDate[] dates =
        [
            RouteDate(5),
            RouteDate(10),
            RouteDate(15),
            RouteDate(20),
            RouteDate(25)
        ];

        var result = dates.ApplyFlex(flex);
        result.Should().BeSameAs(dates, "no changes, same reference");
    }

    [Fact]
    public void OW_FlexLessThanDepartureDateGaps_ReturnDepartureDates_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5),
            RouteDate(10),
            RouteDate(15),
            RouteDate(20),
            RouteDate(25)
        ];

        var result = dates.ApplyFlex(3).ToArray();

        result.Should().HaveCount(5);
        result[0].Should().Be(RouteDate(5), "covers 2-8");
        result[1].Should().Be(RouteDate(10), "covers 7-13");
        result[2].Should().Be(RouteDate(15), "covers 12-18");
        result[3].Should().Be(RouteDate(20), "covers 17-23");
        result[4].Should().Be(RouteDate(25), "covers 22-28");
    }

    [Fact]
    public void RT_FlexLessThanDepartureDateGaps_ReturnDepartureDates_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5, 9),
            RouteDate(10, 14),
            RouteDate(15, 19),
            RouteDate(20, 24),
            RouteDate(25, 29)
        ];

        var result = dates.ApplyFlex(3).ToArray();

        result.Should().HaveCount(5);
        result[0].Should().Be(RouteDate(5, 9), "covers 2-8 -> 7-13");
        result[1].Should().Be(RouteDate(10, 14), "covers 7-13 -> 12-18");
        result[2].Should().Be(RouteDate(15, 19), "covers 12-18 -> 17-23");
        result[3].Should().Be(RouteDate(20, 24), "covers 17-23 -> 22-28");
        result[4].Should().Be(RouteDate(25, 29), "covers 22-28 -> 27-1");
    }

    [Fact]
    public void OW_FlexEqualsDepartureDateGaps_ReturnFlexed_AllDepartureDatesCovered_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5),
            RouteDate(10),
            RouteDate(15),
            RouteDate(20),
            RouteDate(25)
        ];

        var result = dates.ApplyFlex(5).ToArray();

        result.Should().HaveCount(2);
        result[0].Should().Be(RouteDate(10), "covers 5-15");
        result[1].Should().Be(RouteDate(25), "covers 20-30");
    }

    [Fact]
    public void RT_FlexEqualsDepartureDateGaps_ReturnFlexed_AllDepartureDatesCovered_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5, 10),
            RouteDate(10, 15),
            RouteDate(15 ,20),
            RouteDate(20, 25),
            RouteDate(25, 30)
        ];

        var result = dates.ApplyFlex(5).ToArray();

        result.Should().HaveCount(2);
        result[0].Should().Be(RouteDate(10, 15), "covers 5-15 -> 10-20");
        result[1].Should().Be(RouteDate(25, 30), "covers 20-30 -> 25-4");
    }

    [Fact]
    public void OW_AllDepartureDatesCovered_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5),
            RouteDate(7),
            RouteDate(15),
            RouteDate(16),
            RouteDate(25)
        ];

        var result = dates.ApplyFlex(3).ToArray();

        result.Should().HaveCount(3);
        result[0].Should().Be(RouteDate(7), "covers 4-10");
        result[1].Should().Be(RouteDate(16), "covers 13-19");
        result[2].Should().Be(RouteDate(25), "covers 22-28");
    }

    [Fact]
    public void RT_CoversAllDepartureDates_ReturnsDatesFromSetOnly()
    {
        RouteDate[] dates =
        [
            RouteDate(5, 12),
            RouteDate(7, 14),
            RouteDate(15, 22),
            RouteDate(16, 23),
            RouteDate(25, 1)
        ];

        var result = dates.ApplyFlex(3).ToArray();

        result.Should().HaveCount(3);
        result[0].Should().Be(RouteDate(7, 14), "covers 4-10 -> 11-17");
        result[1].Should().Be(RouteDate(16, 23), "covers 13-19 -> 20-26");
        result[2].Should().Be(RouteDate(25, 1), "covers 22-28 -> 29-4");
    }

    private static RouteDate RouteDate(int departureDay, int? returnDepartureDay = null)
    {
        return new RouteDate(
            new Route("KTW", "LTN"),
            departureDay.January(2024),
            returnDepartureDay?.January(2024)
        );
    }
}