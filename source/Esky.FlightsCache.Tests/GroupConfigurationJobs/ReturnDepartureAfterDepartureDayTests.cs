using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public class ReturnDepartureAfterDepartureDayTests
{
    [Fact]
    public void SingleDate_ReturnsSelfOnly()
    {
        var routeDates = new List<X>
        {
            new(new Route("WAW", "LTN"), [1.October(2024)]),
            new(new Route("WMI", "STN"), [1.October(2024)])
        };
        
        var result = routeDates.ReturnDepartureNextDay(
            e => e.Dates,
            e => e.Dates,
            (e, departure, returnDeparture) => new RouteDate(e.Route, departure, returnDeparture)
        ).ToArray();

        result
            .Should()
            .HaveCount(2)
            .And
            .Be<PERSON>quivalentTo(
                new List<RouteDate>
                {
                    new(new Route("WAW", "LTN"), 1.October(2024), 1.October(2024)),
                    new(new Route("WMI", "STN"), 1.October(2024), 1.October(2024))
                },
                opt => opt.WithStrictOrdering()
            );
    }

    [Fact]
    public void MultipleDates_ReturnsOneDayBeforeAndAfter()
    {
        var routeDates = new List<X>
        {
            new(new Route("WAW", "LTN"), [1.October(2024), 10.October(2024)]),
            new(new Route("WMI", "STN"), [1.October(2024), 10.October(2024)])
        };
        
        var result = routeDates.ReturnDepartureNextDay(
            e => e.Dates,
            e => e.Dates,
            (e, departure, returnDeparture) => new RouteDate(e.Route, departure, returnDeparture)
        ).ToArray();

        result
            .Should()
            .HaveCount(6)
            .And
            .BeEquivalentTo(
                new List<RouteDate>
                {
                    new(new Route("WAW", "LTN"), 1.October(2024), 1.October(2024)),
                    new(new Route("WAW", "LTN"), 1.October(2024), 10.October(2024)),
                    new(new Route("WMI", "STN"), 1.October(2024), 1.October(2024)),
                    new(new Route("WMI", "STN"), 1.October(2024), 10.October(2024)),
                    new(new Route("WAW", "LTN"), 10.October(2024), 10.October(2024)),
                    new(new Route("WMI", "STN"), 10.October(2024), 10.October(2024))
                },
                opt => opt.WithStrictOrdering()
            );
    }

    private record X(Route Route, List<DateTime> Dates);
}