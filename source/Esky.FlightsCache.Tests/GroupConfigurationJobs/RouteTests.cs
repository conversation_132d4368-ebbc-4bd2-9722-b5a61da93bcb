using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Newtonsoft.Json;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public class RouteTests
{
    [Theory]
    [InlineData("WAW-LTN", "WAW", "LTN")]
    public void RouteIsParsedCorrectly(string route, string departure, string arrival)
    {
        var parsed = Route.Parse(route, default);
        Assert.Equal(departure, parsed.Departure);
        Assert.Equal(arrival, parsed.Arrival);
    }

    [Fact]
    public void NewtonsoftJsonSerializationTest()
    {
        var json = JsonConvert.SerializeObject(new Route("WAW", "LTN"));
        var parsed = JsonConvert.DeserializeObject<Route>(json);

        Assert.Equal(new Route("WAW", "LTN"), parsed);
    }
}