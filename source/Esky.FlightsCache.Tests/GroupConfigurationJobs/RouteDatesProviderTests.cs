using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Hangfire;
using Hangfire.MemoryStorage;
using MassTransit;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Time.Testing;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public sealed class RouteDatesProviderTests : IAsyncDisposable
{
    private readonly RouteDatesProvider _routeDatesProvider;
    private readonly FakeTimeProvider _timeProvider = new(3.January(2024));
    private readonly ITimetableServiceClient _timetableServiceClient = Substitute.For<ITimetableServiceClient>();
    private readonly WebApplicationFactory<RouteDatesProvider> _app;

    private readonly JobSettings _jobSettings = new()
    {
        Group = "group",
        Suffix = "suffix",
        Cron = "0 0 * * *",
        RelativeDateRange = 1..10,
        Supplier = "wizzair",
        Airlines = ["W6"],
        Routes = ["WAW-PRG", new Route("WAW", "BER")]
    };

    public RouteDatesProviderTests()
    {
        _app = new WebApplicationFactory<RouteDatesProvider>()
            .WithWebHostBuilder(
                b => b
                    .ConfigureTestServices(
                        services => services
                            .AddMassTransitTestHarness()
                            .AddHangfire(e => e.UseMemoryStorage())
                            .Replace(ServiceDescriptor.Singleton<TimeProvider>(_timeProvider))
                            .Replace(ServiceDescriptor.Singleton(_timetableServiceClient))
                    )
            );

        _routeDatesProvider = _app.Services.GetRequiredService<IRouteDatesProvider>() as RouteDatesProvider;
    }

    [Fact]
    public async Task WithoutTimetables_DatesFromSettings()
    {
        const int stayLength = 3;
        var today = _timeProvider.GetUtcNow().Date;
        var jobSettings = _jobSettings with { Airlines = [], IgnoreTimetables = true, StayLengths = [stayLength] };

        var dates = await _routeDatesProvider.GetRouteDates(jobSettings, _ => { });

        dates
            .Should()
            .HaveCount(2 * (10 + 1), "2 routes * 11 days")
            .And
            .BeEquivalentTo(
                RouteDates("WAW", "PRG").Concat(RouteDates("WAW", "BER")),
                opt => opt.WithStrictOrdering(),
                "2 routes * 11 days, each with stay length = 3 days"
            );

        return;

        IEnumerable<RouteDate> RouteDates(string departure, string arrival)
        {
            return Enumerable
                .Range(1, 11)
                .Select(e => new RouteDate(new Route(departure, arrival), today.AddDays(e), today.AddDays(e + stayLength)));
        }
    }

    [Theory]
    [InlineData(false)]
    [InlineData(true)]
    public async Task Timetables_Default_ReturnDepartureIsNextDateFromTimetables(bool skipTheSameDepartureDateAsReturnDeparture)
    {
        var today = _timeProvider.GetUtcNow().Date;
        _timetableServiceClient
            .GetConnectionNetworkByAirline("W6")
            .ReturnsForAnyArgs(new List<ConnectionNetwork> { new() { DepartureAirportCode = "WAW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "PRG", IsNew = false, Months = [] }] } });
        _timetableServiceClient
            .GetFlyingDays(Arg.Any<string>(), Arg.Any<string>(), Arg.Is<IReadOnlyCollection<string>>(x => x.Single().Equals("W6")))
            .Returns([today.AddDays(4), today.AddDays(7), today.AddDays(8), today.AddDays(10), today.AddDays(12), today.AddDays(15)]);
        IEnumerable<RouteDate> expectedRouteDates = [
            new("WAW-PRG", today.AddDays(4), today.AddDays(4)),
            new("WAW-PRG", today.AddDays(4), today.AddDays(7)),
            new("WAW-PRG", today.AddDays(7), today.AddDays(8)),
            new("WAW-PRG", today.AddDays(8), today.AddDays(10)),
            new("WAW-PRG", today.AddDays(10), today.AddDays(12))
        ];

        var dates = await _routeDatesProvider.GetRouteDates(
            _jobSettings with { SkipTheSameDepartureDateAsReturnDeparture = skipTheSameDepartureDateAsReturnDeparture },
            _ => { });
        
        dates.Should()
            .HaveCount(skipTheSameDepartureDateAsReturnDeparture ? 4 : 5)
            .And
            .BeEquivalentTo(
                expectedRouteDates.Skip(skipTheSameDepartureDateAsReturnDeparture ? 1 : 0),
                opt => opt.WithStrictOrdering(),
                "default is return next day of departure from timetable"
            );
    }

    [Fact]
    public async Task Timetables_StayLength_Matches_Timetable()
    {
        var today = _timeProvider.GetUtcNow().Date;
        _timetableServiceClient
            .GetConnectionNetworkByAirline("W6")
            .Returns(new List<ConnectionNetwork> { new() { DepartureAirportCode = "WAW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "PRG", Months = [] }] } });
        _timetableServiceClient
            .GetFlyingDays(Arg.Any<string>(), Arg.Any<string>(), Arg.Is<IReadOnlyCollection<string>>(x => x.Single().Equals("W6")))
            .Returns([today.AddDays(4), today.AddDays(7), today.AddDays(8), today.AddDays(10), today.AddDays(12), today.AddDays(15)]);
        var jobSettings = _jobSettings with { StayLengths = [3] };

        var dates = await _routeDatesProvider.GetRouteDates(jobSettings, _ => { });

        dates.Should()
            .HaveCount(2)
            .And
            .BeEquivalentTo(
                [
                    new RouteDate("WAW-PRG", today.AddDays(4), today.AddDays(7)),
                    new RouteDate("WAW-PRG", today.AddDays(7), today.AddDays(10))
                ],
                opt => opt.WithStrictOrdering(),
                "stay length is 3 days and matches the timetable"
            );
    }

    [Fact]
    public async Task Timetables_Flex_StayLength_Matches_Timetable()
    {
        _timeProvider.SetUtcNow(8.January(2024));
        _timetableServiceClient
            .GetConnectionNetworkByAirline("W6")
            .Returns(new List<ConnectionNetwork> { new() { DepartureAirportCode = "WAW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "PRG", Months = [] }] } });
        int[] flyingDays = [12, 15, 16, 18, 20, 23];
        _timetableServiceClient
            .GetFlyingDays(Arg.Any<string>(), Arg.Any<string>(), Arg.Is<IReadOnlyCollection<string>>(x => x.Single().Equals("W6")))
            .Returns(flyingDays.Select(d => d.January(2024)).ToList());
        var jobSettings = _jobSettings with { StayLengths = [3], Flex = 3 };

        var dates = await _routeDatesProvider.GetRouteDates(jobSettings, _ => { });

        dates
            .Should()
            .ContainSingle()
            .Which.Should()
            .BeEquivalentTo(
                new RouteDate("WAW-PRG", 15.January(2024), 18.January(2024)),
                opt => opt.WithStrictOrdering(),
                "stay length is 3 days and matches the timetable: days 12-15 covered by flexed 15-18 +-3"
            );
    }

    [Fact]
    public async Task Calendar_OneDatePerMonth()
    {
        _timeProvider.SetUtcNow(8.January(2024));
        _timetableServiceClient
            .GetConnectionNetworkByAirline("W6")
            .Returns(new List<ConnectionNetwork> { new() { DepartureAirportCode = "WAW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "PRG", Months = [] }] } });
        int[] flyingDays = [12, 15, 16, 18, 20, 23];
        _timetableServiceClient
            .GetFlyingDays(Arg.Any<string>(), Arg.Any<string>(), Arg.Is<IReadOnlyCollection<string>>(x => x.Single().Equals("W6")))
            .Returns(flyingDays.Select(d => d.January(2024)).Concat(flyingDays.Select(d => d.February(2024))).ToList());
        var jobSettings = _jobSettings with { RelativeDateRange = 11..31, Exclusions = [1..10], StayLengths = [], UseCalendar = true };

        var dates = await _routeDatesProvider.GetRouteDates(jobSettings, _ => { });

        dates
            .Should()
            .ContainSingle()
            .Which.Should()
            .BeEquivalentTo(
                new RouteDate("WAW-PRG", 1.February(2024), 2.February(2024)),
                opt => opt.WithStrictOrdering(),
                "one for whole month not in exclusions"
            );
    }

    public async ValueTask DisposeAsync()
    {
        await _app.DisposeAsync();
    }
}