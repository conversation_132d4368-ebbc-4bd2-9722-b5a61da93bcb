using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDepartureDatesResolvers;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using NSubstitute;
using System;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public class ResolverFactoryTests
{
    private readonly JobSettings _baseJobSettings = new()
    {
        Group = "test",
        Suffix = "test",
        Cron = "0 0 0 * * *",
        RelativeDateRange = 10..30,
        Supplier = "easyjet",
        Airlines = ["W6"]
    };

    private readonly ResolverFactory _resolverFactory = new(Substitute.For<TimeProvider>(), Substitute.For<ITimetableServiceClient>());

    public static readonly TheoryData<bool, bool, bool, int[], Type, Type> TestData = new()
    {
        { true, true, false, [], typeof(FromJobSettings), typeof(OneWay) },
        { false, true, false, [], typeof(FromTimetables), typeof(OneWay) },
        { false, false, false, [], typeof(FromTimetables), typeof(ReturnDepartureAfterDepartureDay) },
        { false, false, false, [1, 2, 3], typeof(FromTimetables), typeof(StayLength) },
        { false, false, true, [], typeof(FromTimetables), typeof(SingleDatePerMonth) }
    };

    [Theory]
    [MemberData(nameof(TestData))]
    public void Create_ShouldReturn_CorrectResolvers_BasedOnJobSettings(
        bool ignoreTimetables,
        bool isOneWay,
        bool isMonthly,
        int[] stayLengths,
        Type expectedRouteDepartureDatesResolver,
        Type expectedRouteDatesResolver)
    {
        var jobSettings = _baseJobSettings with
        {
            IgnoreTimetables = ignoreTimetables,
            IsOneWay = isOneWay,
            UseCalendar = isMonthly,
            StayLengths = stayLengths
        };

        var (routeDepartureDatesResolver, routeDatesResolver) = _resolverFactory.Create(jobSettings);

        routeDepartureDatesResolver.Should().BeOfType(expectedRouteDepartureDatesResolver);
        routeDatesResolver.Should().BeOfType(expectedRouteDatesResolver);
    }
}