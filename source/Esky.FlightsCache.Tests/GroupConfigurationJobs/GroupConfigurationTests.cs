using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Hangfire;
using Newtonsoft.Json;
using System;
using System.Linq;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public class GroupConfigurationTests
{
    private readonly IJobConverter<GroupConfiguration, JobSettings> _converter = new GroupConfigurationConverter();
    private static readonly Configuration _base = new()
    {
        Cron = Cron.Daily(), RelativeDateRange = 1..13
    };

    [Fact]
    public void OverlapValidationTest()
    {
        var fakeToday = new DateTime(2024, 11, 1);
        var jobs = new[]
        {
            _base with { RelativeDateRange = 1..13 }, _base with { RelativeDateRange = 4..20 },
            _base with
            {
                RelativeDateRange = new RelativeDateRange(new DateTime(2025, 1, 1), new DateTime(2025, 1, 31))
            },
            _base with
            {
                RelativeDateRange = new RelativeDateRange(new DateTime(2024, 12, 1), new DateTime(2025, 1, 10))
            },
        };

        var validationResults = new GroupConfiguration
            {
                Name = "Test",
                Configurations = jobs,
                DepartureAirports = ["WAW"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
            .CheckForOverlaps(fakeToday)
            .ToArray();

        validationResults.Should().HaveCount(6, "every combination without duplicates");
        var expectedResult = new OverlapCheckResult[]
        {
            new(Overlaps: true, OverlapsInDays: null, JobIndex1: 0, JobIndex2: 1), new(false, 47, 0, 2),
            new(false, 16, 0, 3), new(false, 37, 1, 2), new(false, 6, 1, 3), new(true, null, 2, 3)
        };
        validationResults.Should().BeEquivalentTo(expectedResult, opt => opt.WithStrictOrdering());
    }

    [Fact]
    public void OverlapResolveDatesTest()
    {
        var fakeToday = new DateTime(2024, 11, 1);
        var jobs = new[]
        {
            _base, _base with { RelativeDateRange = 15..21 },
            _base with
            {
                RelativeDateRange = new RelativeDateRange(fakeToday.AddDays(26), fakeToday.AddDays(26 + 30))
            }
        };
        var group = new GroupConfiguration
        {
            Name = "Test",
            Configurations = jobs,
            DepartureAirports = ["WAW"],
            Supplier = "wizzair",
            Airlines = ["W6"]
        };

        var flyingDays = Enumerable.Range(0, 120).Select(e => new DateTime(2024, 10, 1).AddDays(e)).ToArray();
        var jobWithAbsoluteDates = _converter.ToJobs(fakeToday, group).Last().JobSettings;

        var allowedDates = jobWithAbsoluteDates.WithoutOverlapped(flyingDays, fakeToday).ToArray();

        var expectedDates = Enumerable.Range(0, 20).Select(e => new DateTime(2024, 12, 8).AddDays(e));
        allowedDates.Should().OnlyHaveUniqueItems().And.BeEquivalentTo(expectedDates, opt => opt.WithStrictOrdering());
    }

    [Fact]
    public void NewtonsoftJsonSerializationTest()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            DepartureAirports = ["WAW", "WMI"],
            Routes = [new Route("KTW", "LTN")],
            MaxProcessingWindow = TimeSpan.FromHours(3),
            Exclusions = [2..4, new RelativeDateRange(7, DateTime.UtcNow.Date)],
            Supplier = "wizzair",
            Airlines = ["W6"]
        };
        var settings = new JsonSerializerSettings { Converters = { new RelativeDateJsonSerializer() } };
        var json = JsonConvert.SerializeObject(jobSettings, settings);
        var jobFromJson = JsonConvert.DeserializeObject<JobSettings>(json, settings);

        jobFromJson.Should().BeEquivalentTo(jobSettings);
    }

    [Fact]
    public void Contains_NoDefinitions_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            Routes = [],
            ExcludedDepartureAirports = [],
            Airlines = ["W6"]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_ExcludedDepartureAirport_ReturnsFalse()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            Routes = [],
            Airlines = ["W6"],
            ExcludedDepartureAirports = ["WAW"]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeFalse();
    }

    [Fact]
    public void Contains_WithNullExcludedDepartureAirports_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            Routes = [],
            Airlines = ["W6"],
            ExcludedDepartureAirports = null
        };

        var route = new Route("KTW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_NotExcludedDepartureAirport_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            Routes = [],
            Airlines = ["W6"],
            ExcludedDepartureAirports = ["WAW"]
        };

        var route = new Route("KTW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_IncludedRoute_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            ExcludedDepartureAirports = [],
            Airlines = ["W6"],
            Routes = [new Route("WAW", "LTN")]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_IncludedDepartureAirport_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            ArrivalAirports = [],
            Routes = [],
            ExcludedDepartureAirports = [],
            Airlines = ["W6"],
            DepartureAirports = ["WAW"]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_IncludedArrivalAirport_ReturnsTrue()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            Routes = [],
            ExcludedDepartureAirports = [],
            Airlines = ["W6"],
            ArrivalAirports = ["LTN"]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeTrue();
    }

    [Fact]
    public void Contains_NotIncludedRoute_ReturnsFalse()
    {
        var jobSettings = new JobSettings
        {
            Group = "TestGroup",
            Suffix = "TestSuffix",
            Cron = "* * * * *",
            RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date, 14),
            Supplier = "wizzair",
            DepartureAirports = [],
            ArrivalAirports = [],
            ExcludedDepartureAirports = [],
            Airlines = ["W6"],
            Routes = [new Route("KTW", "LTN")]
        };

        var route = new Route("WAW", "LTN");
        var result = jobSettings.Contains(route);

        result.Should().BeFalse();
    }
}