using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Microsoft.Extensions.Time.Testing;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;
using VerifyTests;

namespace Esky.FlightsCache.Tests.GroupConfigurationJobs;

public class RouteDatesProviderSnapshotTests
{
    private static readonly JobSettings _jobSettings = new()
    {
        Group = "group",
        Suffix = "suffix",
        Cron = "0 0 * * *",
        RelativeDateRange = 1..10,
        Supplier = "wizzair",
        Airlines = ["W6"],
        IgnoreTimetables = false,
        Routes = [new Route("WAW", "PRG"), new Route("WAW", "BER")]
    };

    private readonly ResolverFactory _resolverFactory;
    private readonly FakeTimeProvider _timeProvider = new(7.January(2024));
    private readonly VerifySettings _verifySettings;

    public RouteDatesProviderSnapshotTests()
    {
        var today = _timeProvider.GetUtcNow().Date;
        var timetable = Substitute.For<ITimetableServiceClient>();
        timetable
            .GetConnectionNetworkByAirline("W6")
            .Returns(new List<ConnectionNetwork> { new() { DepartureAirportCode = "WAW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "PRG", Months = [] }] } });
        timetable
            .GetFlyingDays(string.Empty, string.Empty, Arg.Any<IReadOnlyCollection<string>>())
            .ReturnsForAnyArgs([today.AddDays(4), today.AddDays(7), today.AddDays(8), today.AddDays(10), today.AddDays(12)]);

        _resolverFactory = new ResolverFactory(_timeProvider, timetable);
        _verifySettings = new VerifySettings();
        _verifySettings.DontScrubDateTimes();
        _verifySettings.UseDirectory("_Snapshots");
        _verifySettings.DisableRequireUniquePrefix();
    }

    [Fact]
    public async Task Timetables_Without_StayLengths()
    {
        await VerifyRouteDates(_jobSettings);
    }

    [Fact]
    public async Task Timetables_Without_StayLengths_SkipSameDayDepartureAndReturn()
    {
        await VerifyRouteDates(_jobSettings with { SkipTheSameDepartureDateAsReturnDeparture = true });
    }

    [Fact]
    public async Task Without_Timetables_And_StayLengths()
    {
        var jobSettings = _jobSettings with { Airlines = [], IgnoreTimetables = true };
        await VerifyRouteDates(jobSettings);
    }
    
    [Fact]
    public async Task Without_Timetables_And_StayLengths_And_SameReturnDepartureAsDepartures()
    {
        var jobSettings = _jobSettings with { Airlines = [], IgnoreTimetables = true, SkipTheSameDepartureDateAsReturnDeparture = true };
        await VerifyRouteDates(jobSettings);
    }

    [Fact]
    public async Task StayLengths_Without_Timetables()
    {
        var jobSettings = _jobSettings with { Airlines = [], IgnoreTimetables = true, StayLengths = [3] };
        await VerifyRouteDates(jobSettings);
    }

    [Theory]
    [InlineData(false)]
    [InlineData(true)]
    public async Task Timetables_And_StayLengths(bool skipSameDays)
    {
        var jobSettings = _jobSettings with { StayLengths = [4, 6], SkipTheSameDepartureDateAsReturnDeparture = skipSameDays };
        await VerifyRouteDates(jobSettings);
    }

    [Fact]
    public async Task OneWay_Timetables()
    {
        var jobSettings = _jobSettings with { IsOneWay = true };
        await VerifyRouteDates(jobSettings);
    }

    [Fact]
    public async Task OneWay_Without_Timetables()
    {
        var jobSettings = _jobSettings with { Airlines = [], IgnoreTimetables = true, IsOneWay = true };
        await VerifyRouteDates(jobSettings);
    }

    private async Task VerifyRouteDates(JobSettings jobSettings)
    {
        var provider = new RouteDatesProvider(_timeProvider, _resolverFactory);
        var dates = await provider.GetRouteDates(jobSettings, _ => { });
        await Verify(dates, _verifySettings);
    }
}