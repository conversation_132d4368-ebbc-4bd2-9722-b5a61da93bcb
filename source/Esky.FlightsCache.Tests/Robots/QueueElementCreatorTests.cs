using Esky.FlightsCache.RobotsProducers;
using Esky.Framework.PartnerSettings.Enums;
using System;
using Xunit;

namespace Esky.FlightsCache.Tests.Robots
{
    public class QueueElementCreatorTests
    {
        private QueueElementCreator _serviceToTest;

        public QueueElementCreatorTests()
        {
            _serviceToTest = new QueueElementCreator();
        }

        [Fact]
        public void CreateOneWayElement_ForNonFlexOption_ShouldReturnCorrectValue()
        {
            ProviderCodeEnum providerCode = ProviderCodeEnum.TravelFusion;
            string partnerCode = "ADMIN";
            string departureCode = "KTW";
            string arrivalCode = "LTN";
            DateTime departureDay = new DateTime(2021, 3, 10);
            int flex = 30;
            string airlineCode = "W6";

            //Act
            var result = _serviceToTest
                .CreateOneWayElement(providerCode, partnerCode, departureCode, arrivalCode, departureDay, flex)
                .ConfigureAirlineCode(airlineCode)
                .Resolve();

            //Assert
            Assert.Equal(departureCode, result.DepartureCode);
            Assert.Equal(arrivalCode, result.ArrivalCode);
            Assert.Equal(airlineCode, result.AirlineCode);
            Assert.Equal(departureDay, result.DepartureDay);
            Assert.Equal(partnerCode, result.PartnerCode);
            Assert.Equal(departureDay, result.DeleteDepartureDayFrom);
            Assert.Equal(departureDay.AddDays(flex), result.DeleteDepartureDayTo);
        }

        [Fact]
        public void CreateOneWayElement_ForFlexOption_ShouldReturnCorrectValue()
        {
            ProviderCodeEnum providerCode = ProviderCodeEnum.TravelFusion;
            string partnerCode = "ADMIN";
            string departureCode = "KTW";
            string arrivalCode = "LTN";
            DateTime departureDay = new DateTime(2021, 3, 10);
            int flex = 30;
            string airlineCode = "W6";

            //Act
            var result = _serviceToTest
                .CreateOneWayElement(providerCode, partnerCode, departureCode, arrivalCode, departureDay, flex)
                .ConfigureAirlineCode(airlineCode)
                .ConfigureDeleteForFlex()
                .Resolve();

            //Assert
            Assert.Equal(departureCode, result.DepartureCode);
            Assert.Equal(arrivalCode, result.ArrivalCode);
            Assert.Equal(airlineCode, result.AirlineCode);
            Assert.Equal(departureDay, result.DepartureDay);
            Assert.Equal(partnerCode, result.PartnerCode);
            Assert.Equal(departureDay.AddDays(-flex), result.DeleteDepartureDayFrom);
            Assert.Equal(departureDay.AddDays(flex), result.DeleteDepartureDayTo);
        }

        [Fact]
        public void CreateRoundTripElement_ForNonFlexOption_ShouldReturnCorrectValue()
        {
            ProviderCodeEnum providerCode = ProviderCodeEnum.TravelFusion;
            string partnerCode = "ADMIN";
            string departureCode = "KTW";
            string arrivalCode = "LTN";
            DateTime departureDay = new DateTime(2021, 3, 10);
            int flex = 30;
            string airlineCode = "W6";
            DateTime returnDepartureDay = new DateTime(2021, 4, 10);

            //Act
            var result = _serviceToTest
                .CreateRoundTripElement(providerCode, partnerCode, departureCode, arrivalCode, departureDay, returnDepartureDay, flex)
                .ConfigureAirlineCode(airlineCode)
                .Resolve();

            //Assert
            Assert.Equal(departureCode, result.DepartureCode);
            Assert.Equal(arrivalCode, result.ArrivalCode);
            Assert.Equal(airlineCode, result.AirlineCode);
            Assert.Equal(departureDay, result.DepartureDay);
            Assert.Equal(partnerCode, result.PartnerCode);
            Assert.Equal(departureDay, result.DeleteDepartureDayFrom);
            Assert.Equal(departureDay.AddDays(flex), result.DeleteDepartureDayTo);
            Assert.Equal(returnDepartureDay, result.DeleteReturnDepartureDayFrom);
            Assert.Equal(returnDepartureDay.AddDays(flex), result.DeleteReturnDepartureDayTo);
        }

        [Fact]
        public void CreateRoundTripElement_ForFlexOption_ShouldReturnCorrectValue()
        {
            ProviderCodeEnum providerCode = ProviderCodeEnum.TravelFusion;
            string partnerCode = "ADMIN";
            string departureCode = "KTW";
            string arrivalCode = "LTN";
            DateTime departureDay = new DateTime(2021, 3, 10);
            int flex = 30;
            string airlineCode = "W6";
            DateTime returnDepartureDay = new DateTime(2021, 4, 10);

            //Act
            var result = _serviceToTest
                .CreateRoundTripElement(providerCode, partnerCode, departureCode, arrivalCode, departureDay, returnDepartureDay, flex)
                .ConfigureAirlineCode(airlineCode)
                .ConfigureDeleteForFlex()
                .Resolve();

            //Assert
            Assert.Equal(departureCode, result.DepartureCode);
            Assert.Equal(arrivalCode, result.ArrivalCode);
            Assert.Equal(airlineCode, result.AirlineCode);
            Assert.Equal(departureDay, result.DepartureDay);
            Assert.Equal(partnerCode, result.PartnerCode);
            Assert.Equal(departureDay.AddDays(-flex), result.DeleteDepartureDayFrom);
            Assert.Equal(departureDay.AddDays(flex), result.DeleteDepartureDayTo);
            Assert.Equal(returnDepartureDay.AddDays(-flex), result.DeleteReturnDepartureDayFrom);
            Assert.Equal(returnDepartureDay.AddDays(flex), result.DeleteReturnDepartureDayTo);
        }
    }
}