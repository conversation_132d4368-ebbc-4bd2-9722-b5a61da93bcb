using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using NSubstitute;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Esky.FlightsCache.Tests.Robots
{
    public class CacheDemandJbValidationExtensionsTests
    {
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task ValidatePartnerCodes_ChecksIfPartnerExists(bool partnerExists)
        {
            var substitute = Substitute.For<IPartnerSettingsService>();
            substitute.GetPartnerSettingsAsync(Arg.Any<string>())
                .ReturnsForAnyArgs(Task.FromResult(partnerExists ? new PartnerSettingsModel() : null));

            var job = new CacheDemandJob
            {
                Items = new List<CacheDemandJobItem>
                {
                    new() { InputModel = new CacheDemandModelInput
                        {
                            PartnerCode = "ESKYPL",
                            DepartureAirportCode = "KTW",
                            ArrivalAirportCode = "STN",
                            DepartureDateFrom = default,
                            DepartureDateTo = default
                        }
                    }
                },
                IsValid = true
            };

            await job.ValidatePartnerCodes(substitute);

            Assert.Equal(partnerExists, job.IsValid);
            Assert.Equal(partnerExists, string.IsNullOrEmpty(job.ErrorMessage));
        }
    }
}
