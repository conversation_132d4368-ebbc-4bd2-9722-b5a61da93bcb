using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Esky.FlightsCache.RobotsProducers;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Esky.FlightsCache.Tests.Robots
{
    public class DayByDayAlgorithmTests
    {
        private const string _partnerCode = "ADMIN";

        private readonly DayByDayAlgorithm _sut;
        private readonly ITimetableServiceClient _timetableServiceClient = Substitute.For<ITimetableServiceClient>();
        private readonly IFlightsCacheService _flightsCacheService = Substitute.For<IFlightsCacheService>();

        public DayByDayAlgorithmTests()
        {
            _sut = new DayByDayAlgorithm(_timetableServiceClient, _flightsCacheService, new QueueElementCreator(),
                Substitute.For<ILogger<DayByDayAlgorithm>>());
        }

        [Fact]
        public async Task WhenNoTimetablesSet_ThenNothingReturned()
        {
            // arrange

            // act
            var (elements, _) = await _sut.GenerateTravelFusionElements(new[] { "wizzair" }, Array.Empty<string>(), _partnerCode, DateTime.UtcNow, DateTime.UtcNow, TimeSpan.Zero, "");

            //assert
            elements.Should().BeEmpty();
        }

        [Fact]
        public async Task GivenSingleRouteWithoutFlyingDays_When4DaysRequested_ThenQueueFor4Days()
        {
            // arrange
            var startDate = DateTime.UtcNow;
            var endDate = DateTime.UtcNow.AddDays(5);
            var suppliers = new[] { "wizzair" };
            _flightsCacheService.Configure()
                .GetTimetable(Arg.Any<SearchTimetablesRequest>(), CancellationToken.None)
                .ReturnsForAnyArgs(CreateFlyingDays("KTW", "LTN", startDate, startDate.AddDays(2), startDate.AddDays(3), startDate.AddDays(4)));

            // act
            var (elements, _) = (await _sut.GenerateTravelFusionElements(suppliers, Array.Empty<string>(), _partnerCode, startDate, endDate, TimeSpan.Zero, ""));

            //assert
            elements.Should().NotBeEmpty();
            elements.Count().Should().Be(4);
        }

        [Fact]
        public async Task GivenSingleRouteWith2FlyingDays_When4DaysRequestes_ThenQueueFor2Days()
        {
            // arrange
            var startDate = DateTime.UtcNow;
            var endDate = DateTime.UtcNow.AddDays(3);
            var suppliers = new[] { "wizzair" };
            _flightsCacheService.Configure()
                .GetTimetable(Arg.Any<SearchTimetablesRequest>(), CancellationToken.None)
                .ReturnsForAnyArgs(CreateFlyingDays("KTW", "LTN", startDate, startDate.AddDays(2)));

            // act
            var (elements, _) = (await _sut.GenerateTravelFusionElements(suppliers, Array.Empty<string>(), _partnerCode, startDate, endDate, TimeSpan.Zero, ""));

            //assert
            elements.Should().NotBeEmpty();
            elements.Count().Should().Be(2);
        }

        private static Task<SearchTimetablesResponseItem[]> CreateFlyingDays(string departure, string arrival,
            params DateTime[] dates)
        {
            return Task.FromResult(new[]
            {
                new SearchTimetablesResponseItem
                {
                    DepartureAirportCode = departure, ArrivalAirportCode = arrival, Dates = dates,
                }
            });
        }

        private static Task<IList<ConnectionNetwork>> CreateConnectionNetworkList(params (string Departure, (string Arrival, bool IsNew)[] Arrivals)[] routes)
        {
            return Task.FromResult((IList<ConnectionNetwork>)
                routes
                    .Select(tuple => new ConnectionNetwork
                    {
                        DepartureAirportCode = tuple.Departure,
                        ArrivalAirportCodes = tuple.Arrivals
                            .Select(x => new ConnectionNetworkArrivalAirport { ArrivalCode = x.Arrival, IsNew = x.IsNew, Months = [] })
                            .ToList()
                    })
                    .ToList()
            );
        }
    }
}
