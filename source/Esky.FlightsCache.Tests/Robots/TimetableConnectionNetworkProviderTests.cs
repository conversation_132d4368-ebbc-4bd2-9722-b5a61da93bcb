using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Algorithms;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.Robots;

public class TimetableConnectionNetworkProviderTests
{
    [Fact]
    public async Task ReturnsCorrectConnectionNetwork()
    {
        string[] airlineCodes = ["FR"];
        var timetables = Substitute.For<ITimetableServiceClient>();
        timetables
            .GetConnectionNetworkByAirline("FR")
            .Returns(
                new List<ConnectionNetwork>
                {
                    new() { DepartureAirportCode = "KTW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "STN", Months = [202201] }] },
                    new() { DepartureAirportCode = "KTW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "LGW", Months = [202201] }] },
                    new() { DepartureAirportCode = "KTW", ArrivalAirportCodes = [new ConnectionNetworkArrivalAirport { ArrivalCode = "LGW", Months = [202201] }] }
                }
            );
        timetables.GetFlyingDays("KTW", "STN", airlineCodes).Returns(new List<DateTime> { 1.January(2022), 10.January(2022) });
        timetables.GetFlyingDays("KTW", "LGW", airlineCodes).Returns(new List<DateTime> { 15.January(2022), 20.January(2022) });
        var provider = new TimetableConnectionNetworkProvider(Substitute.For<ILogger<TimetableConnectionNetworkProvider>>(), timetables);

        var connectionNetwork = await provider.GetConnectionNetwork(airlineCodes);

        connectionNetwork
            .Should()
            .BeEquivalentTo(
                new Route[]
                {
                    new("KTW", "STN", [1.January(2022), 10.January(2022)]),
                    new("KTW", "LGW", [15.January(2022), 20.January(2022)])
                },
                opt => opt.WithStrictOrdering().WithStrictOrderingFor(e => e.FlyingDates)
            );
    }
}