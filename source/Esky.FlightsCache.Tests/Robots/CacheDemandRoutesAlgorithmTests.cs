using Esky.FlightsCache.RobotsProducers;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.Framework.PartnerSettings.Enums;
using NSubstitute;
using NSubstitute.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Tests.Robots
{
    public class CacheDemandRoutesAlgorithmTests
    {
        private readonly CacheDemandRoutesAlgorithm _algorithm = new(new QueueElementCreator());

        [Theory]
        [InlineData(new[] { "" }, 35, "", "KTW", "LON", "2122-07-01", "2122-07-31", "RT", 3, 14, 372, 42)]
        [InlineData(new[] { "" }, 35, "", "KRK", "JF<PERSON>", "2122-07-01", "2122-07-31", "OW", null, null, 31, 31)]
        [InlineData(new[] { "AA" }, 35, "", "KRK", "JFK", "2122-07-01", "2122-07-31", "OW", null, null, 31, 31)]
        [InlineData(new[] { "AA", "BB" }, 35, "", "KRK", "JFK", "2122-07-01", "2122-07-31", "OW", null, null, 62, 62)]
        [InlineData(new[] { "" }, 35, "", "KRK", "JFK", "2122-07-01", "2122-07-01", "RT", 4, 4, 1, 1)]
        [InlineData(new[] { "" }, 35, "", "KRK", "JFK", "2122-07-01", "2122-07-01", "OW", null, null, 1, 1)]
        [InlineData(new[] { "" }, 35, "", "KRK", "JFK", "2122-07-01", "10", "OW", null, null, 11, 11)]
        public void ShouldCreateMultipleQueueMessages(
            IList<string> af,
            int pc,
            string partner,
            string dac,
            string aac,
            string ddf,
            string ddt,
            string tripType,
            int? minStayLength,
            int? maxStayLength,
            int expectedRequestsCount,
            int expectedElementsCount)
        {
            var model = new CacheDemandModelInput
            {
                AirlinesFilter = af,
                ProviderCode = (ProviderCodeEnum)pc,
                PartnerCode = partner,
                DepartureAirportCode = dac,
                ArrivalAirportCode = aac,
                DepartureDateFrom = RelativeDate.Parse(ddf),
                DepartureDateTo = RelativeDate.Parse(ddt),
                IsRoundTrip = tripType == "RT",
                MinStayLength = minStayLength,
                MaxStayLength = maxStayLength
            };

            var jobItem = model.ToJobItem(_algorithm);
            jobItem.RequestsGenerated.Should().Be(expectedRequestsCount);
            jobItem.ElementsGenerated.Should().Be(expectedElementsCount);
        }

        [Fact]
        public void ShouldParseMultipleLineContent()
        {
            const string content =
                @"	35		KTW	LON	2022-07-01	2022-07-31	RT	3	14
 	 35 	 	KRK 	JFK 	2022-07-01 	2022-07-31 	 OW 	 	 
AA	35		KRK	JFK	2022-07-01	2022-07-31	OW		
AA,BB	35		KRK	JFK	2022-07-01	2022-07-31	OW		
	35		KRK	JFK	10	15	RT	4	4
	35		KRK	JFK	2022-07-01	2022-07-01	OW		";

            const string header = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tRequests";

            var mapper = new CacheDemandInputMapper();
            var columnMapping = ColumnMapping.Parse(header);
            var result = mapper.Parse(content, columnMapping, "ADMIN");

            result.Should().HaveCount(6);

            var model = result[0];

            model.AirlinesFilter.Should().ContainSingle("");
            model.ProviderCode.Should().Be(ProviderCodeEnum.Amadeus);
            model.PartnerCode.Should().Be("ADMIN");
            model.DepartureAirportCode.Should().Be("KTW");
            model.ArrivalAirportCode.Should().Be("LON");
            model.DepartureDateFrom.Should().Be(new RelativeDate(new DateTime(2022, 07, 1)));
            model.DepartureDateTo.Should().Be(new RelativeDate(new DateTime(2022, 07, 31)));
            model.IsRoundTrip.Should().BeTrue();
            model.MinStayLength.Should().Be(3);
            model.MaxStayLength.Should().Be(14);
            model.Type.Should().Be(JobType.CacheDemand);

            model = result[1];

            model.AirlinesFilter.Should().ContainSingle("");
            model.ProviderCode.Should().Be(ProviderCodeEnum.Amadeus);
            model.PartnerCode.Should().Be("ADMIN");
            model.DepartureAirportCode.Should().Be("KRK");
            model.ArrivalAirportCode.Should().Be("JFK");
            model.DepartureDateFrom.Should().Be(new RelativeDate(new DateTime(2022, 07, 1)));
            model.DepartureDateTo.Should().Be(new RelativeDate(new DateTime(2022, 07, 31)));
            model.IsRoundTrip.Should().BeFalse();
            model.MinStayLength.Should().BeNull();
            model.MaxStayLength.Should().BeNull();
            model.Type.Should().Be(JobType.CacheDemand);

            model = result[3];

            model.AirlinesFilter.Should().Equal("AA", "BB");
            model.ProviderCode.Should().Be(ProviderCodeEnum.Amadeus);
            model.PartnerCode.Should().Be("ADMIN");
            model.DepartureAirportCode.Should().Be("KRK");
            model.ArrivalAirportCode.Should().Be("JFK");
            model.DepartureDateFrom.Should().Be(new RelativeDate(new DateTime(2022, 07, 1)));
            model.DepartureDateTo.Should().Be(new RelativeDate(new DateTime(2022, 07, 31)));
            model.IsRoundTrip.Should().BeFalse();
            model.MinStayLength.Should().BeNull();
            model.MaxStayLength.Should().BeNull();
            model.Type.Should().Be(JobType.CacheDemand);

            model = result[4];

            model.AirlinesFilter.Should().ContainSingle("");
            model.ProviderCode.Should().Be(ProviderCodeEnum.Amadeus);
            model.PartnerCode.Should().Be("ADMIN");
            model.DepartureAirportCode.Should().Be("KRK");
            model.ArrivalAirportCode.Should().Be("JFK");
            model.DepartureDateFrom.Should().Be(new RelativeDate(10));
            model.DepartureDateTo.Should().Be(new RelativeDate(15));
            model.IsRoundTrip.Should().BeTrue();
            model.MinStayLength.Should().Be(4);
            model.MaxStayLength.Should().Be(4);
            model.Type.Should().Be(JobType.CacheDemand);
        }

        [Fact]
        public void ShouldParseAlcContent()
        {
            const string content =
                @"	ALC	EDESTINOSMX	GUA	MEX	4	26	OW			WAWEY38AA	27";

            const string header = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tRequests";

            var mapper = new CacheDemandInputMapper();
            var columnMapping = ColumnMapping.Parse(header);
            var result = mapper.Parse(content, columnMapping, "ADMIN");

            var model = result.Should().ContainSingle().Which;

            model.AirlinesFilter.Should().ContainSingle("");
            model.ProviderCode.Should().BeNull();
            model.PartnerCode.Should().Be("EDESTINOSMX");
            model.DepartureAirportCode.Should().Be("GUA");
            model.ArrivalAirportCode.Should().Be("MEX");
            model.DepartureDateFrom.Should().Be(new RelativeDate(4));
            model.DepartureDateTo.Should().Be(new RelativeDate(26));
            model.IsRoundTrip.Should().BeFalse();
            model.MinStayLength.Should().BeNull();
            model.MaxStayLength.Should().BeNull();
            model.Type.Should().Be(JobType.AmadeusLiveCheck);
            model.OfficeId.Should().Be("WAWEY38AA");
        }

        [Theory]
        [InlineData("\t35\t\tKTW\tLON\tXXXXXXXXXX\t2022-07-31\tRT\t3\t14", "*DepartureFrom*")]
        [InlineData("\t35\t\tKTW\tLON\t2022-07-01\tXXXXXXXXXX\tRT\t3\t14", "*DepartureTo*")]
        [InlineData("\t35\t\tKTW\tLON\t2022-07-01\t2022-07-31\tRT\tX\t14", "*MinStayLength*")]
        [InlineData("\t35\t\tKTW\tLON\t2022-07-01\t2022-07-31\tRT\t3\tXX", "*MaxStayLength*")]
        public void Parse_WhenCannotParseCell_ShouldThrowExceptionWithColumnName(string content, string expectedColumnNameException)
        {
            const string header = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tRequests";

            var mapper = new CacheDemandInputMapper();
            var columnMapping = ColumnMapping.Parse(header);

            var action = () => mapper.Parse(content, columnMapping, "ADMIN");

            action.Should().Throw<ArgumentException>().WithMessage(expectedColumnNameException);
        }

        [Fact]
        public void ParsesMultipleProviderCodes()
        {
            const string header = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tRequests";
            const string content = """
                                   	ALC ,35, 58	EDESTINOSMX	GUA	MEX	1	2	OW			WAWEY38AA
                                   	58	ESKYPL	WAW	LTN	2	3	RT	2	3	
                                   	58,35 	ESKY	KTW	STN	2	3	RT	2	3	
                                   """;
            var mapper = new CacheDemandInputMapper();
            var columnMapping = ColumnMapping.Parse(header);
            var jobItems = mapper.Parse(content, columnMapping, "ADMIN");

            // assert
            jobItems.Should().HaveCount(6);

            var expectedFirstRow = new CacheDemandModelInput
            {
                ProviderCode = null,
                AirlinesFilter = [""],
                PartnerCode = "EDESTINOSMX",
                DepartureAirportCode = "GUA",
                ArrivalAirportCode = "MEX",
                DepartureDateFrom = new RelativeDate(1),
                DepartureDateTo = new RelativeDate(2),
                IsRoundTrip = false,
                OfficeId = "WAWEY38AA",
                Type = JobType.CacheDemand
            };
            var expectedSecondRow = new CacheDemandModelInput
            {
                ProviderCode = ProviderCodeEnum.TravelFusion,
                AirlinesFilter = [""],
                PartnerCode = "ESKYPL",
                DepartureAirportCode = "WAW",
                ArrivalAirportCode = "LTN",
                DepartureDateFrom = new RelativeDate(2),
                DepartureDateTo = new RelativeDate(3),
                MinStayLength = 2,
                MaxStayLength = 3,
                IsRoundTrip = true,
                Type = JobType.CacheDemand
            };
            var expectedThirdRow = new CacheDemandModelInput
            {
                ProviderCode = ProviderCodeEnum.TravelFusion,
                AirlinesFilter = [""],
                PartnerCode = "ESKY",
                DepartureAirportCode = "KTW",
                ArrivalAirportCode = "STN",
                DepartureDateFrom = new RelativeDate(2),
                DepartureDateTo = new RelativeDate(3),
                MinStayLength = 2,
                MaxStayLength = 3,
                IsRoundTrip = true,
                Type = JobType.CacheDemand
            };

            jobItems.Should().BeEquivalentTo(new List<CacheDemandModelInput>
            {
                expectedFirstRow with { Type = JobType.AmadeusLiveCheck },
                expectedFirstRow with { ProviderCode = ProviderCodeEnum.Amadeus },
                expectedFirstRow with { ProviderCode = ProviderCodeEnum.TravelFusion },
                expectedSecondRow,
                expectedThirdRow,
                expectedThirdRow with { ProviderCode = ProviderCodeEnum.Amadeus },
            });
        }

        [Fact]
        public void ParsesExcludedAirlines()
        {
            const string header = "AirlineFilter\tProviderCode\tPartnerCode\tDeparture\tArrival\tDepartureFrom\tDepartureTo\tTripType\tMinStayLength\tMaxStayLength\tOfficeId\tAlcExcludedAirlines\tRequests";
            const string content = """
                                   	ALC	EDESTINOSMX	GUA	MEX	1	2	OW			WAWEY38AA	SK		
                                   """;
            var mapper = new CacheDemandInputMapper();
            var columnMapping = ColumnMapping.Parse(header);
            var jobItems = mapper.Parse(content, columnMapping, "ADMIN");

            // assert
            jobItems.Should().HaveCount(1);

            var expectedFirstRow = new CacheDemandModelInput
            {
                ProviderCode = null,
                AirlinesFilter = [""],
                PartnerCode = "EDESTINOSMX",
                DepartureAirportCode = "GUA",
                ArrivalAirportCode = "MEX",
                DepartureDateFrom = new RelativeDate(1),
                DepartureDateTo = new RelativeDate(2),
                IsRoundTrip = false,
                OfficeId = "WAWEY38AA",
                Type = JobType.CacheDemand
            };

            jobItems.Should().BeEquivalentTo(new List<CacheDemandModelInput>
            {
                expectedFirstRow with { Type = JobType.AmadeusLiveCheck, AlcExcludedAirlines = ["SK"]}
            });
        }

        [Fact]
        public void ProducesCorrectNumberOfRequestsOwAndRt()
        {
            var ow = new CacheDemandModelInput
            {
                ProviderCode = ProviderCodeEnum.Amadeus,
                DepartureDateFrom = new RelativeDate(1),
                DepartureDateTo = new RelativeDate(2),
                IsRoundTrip = false,
                OfficeId = "QWERTY",
                AirlinesFilter = [""],
                PartnerCode = "ESKY",
                DepartureAirportCode = "KTW",
                ArrivalAirportCode = "STN",
            };
            var rt = new CacheDemandModelInput
            {
                ProviderCode = ProviderCodeEnum.TravelFusion,
                DepartureDateFrom = new RelativeDate(2),
                DepartureDateTo = new RelativeDate(3),
                IsRoundTrip = true,
                MinStayLength = 2,
                MaxStayLength = 3,
                AirlinesFilter = [""],
                PartnerCode = "ESKY",
                DepartureAirportCode = "KTW",
                ArrivalAirportCode = "STN",
            };
            var jobItems = new[] { ow, rt }.ToJobItems(_algorithm).ToArray();

            var creator = Substitute.For<IQueueElementCreator>();
            creator.ReturnsForAll(creator);
            creator.ReturnsForAll(new QueueElement());
            var algorithm = new CacheDemandRoutesAlgorithm(creator);
            var generatedElements =
                algorithm.GenerateQueueElements<QueueElement>(new CacheDemandJob { Items = jobItems });

            jobItems.Should().HaveCount(2);
            jobItems[0].RequestsGenerated.Should().Be(3);
            jobItems[0].ElementsGenerated.Should().Be(3);
            jobItems[1].RequestsGenerated.Should().Be(8);
            jobItems[1].ElementsGenerated.Should().Be(5);
            generatedElements.Should().HaveCount(3 + 5);
        }

        [Fact]
        public void ProducesCorrectNumberOfRequestsRt()
        {
            var rt = new CacheDemandModelInput
            {
                DepartureDateFrom = new RelativeDate(91),
                DepartureDateTo = new RelativeDate(238),
                IsRoundTrip = true,
                MinStayLength = 3,
                MaxStayLength = 14,
                AirlinesFilter = [""],
                PartnerCode = "ESKY",
                DepartureAirportCode = "KTW",
                ArrivalAirportCode = "STN"
            };
            var jobItems = new List<CacheDemandJobItem> { rt.ToJobItem(_algorithm) };

            var creator = Substitute.For<IQueueElementCreator>();
            creator.ReturnsForAll(creator);
            creator.ReturnsForAll(new QueueElement());
            var algorithm = new CacheDemandRoutesAlgorithm(creator);
            var generatedElements =
                algorithm.GenerateQueueElements<QueueElement>(new CacheDemandJob { Items = jobItems });

            jobItems.Should().HaveCount(1);
            jobItems[0].RequestsGenerated.Should().Be(2868);
            jobItems[0].ElementsGenerated.Should().Be(250);
            generatedElements.Should().HaveCount(250);
        }

        [InlineData("2024-01-01", 3, 1, 2, 4, 1)]
        [InlineData("2024-01-01", 3, 6, 8, 5, 7)]
        [InlineData("2024-01-01", 4, 6, 8, 6, 7)]
        [InlineData("2024-01-01", 5, 5, 10, 10, 7)]
        [InlineData("2024-01-01", 239, 3, 14, 250, 7)]
        [Theory]
        public void ProducesCorrectRtSeparableDates(DateTime startDate, int departureDays, int startStayLength,
            int endStayLength, int expectedRequiredDates, int preferredStayLength)
        {
            var dates = new List<CacheDemandRoutesAlgorithm.Dates>();
            for (var i = 0; i < departureDays; i++)
            {
                dates.AddRange(CacheDemandRoutesAlgorithm.GetRtSeparableDates(
                    date: startDate.AddDays(i),
                    returnDays: Enumerable.Range(startStayLength, endStayLength - startStayLength + 1).Order(),
                    isFirst: i == 0,
                    isLast: i == departureDays - 1));
            }

            var sumOfRequiredDays = dates.Sum(d => d.Returns.Count(r => r.IsRequired));

            var mostlyPreferredStayLength = GetMostlyPreferredStayLengths(dates);

            sumOfRequiredDays.Should().Be(expectedRequiredDates);
            mostlyPreferredStayLength.Should().Be(preferredStayLength);
        }

        [Fact]
        public void ProducesCorrectRtSeparableDates_SeparatingEachSingleRequiredDate()
        {
            var startDate = new DateTime(2024, 1, 1);
            var expected = new[]
            {
                new CacheDemandRoutesAlgorithm.Dates(startDate.AddDays(0),
                [
                    new ReturnDepartureDate(startDate.AddDays(6), true) ]
                ),
                new CacheDemandRoutesAlgorithm.Dates(startDate.AddDays(0),
                [
                    new ReturnDepartureDate(startDate.AddDays(7), true),
                    new ReturnDepartureDate(startDate.AddDays(8), false)
                ]),
                new CacheDemandRoutesAlgorithm.Dates(startDate.AddDays(1),
                [
                    new ReturnDepartureDate(startDate.AddDays(1+6), false),
                    new ReturnDepartureDate(startDate.AddDays(1+7), true),
                    new ReturnDepartureDate(startDate.AddDays(1+8), false)
                ]),
                new CacheDemandRoutesAlgorithm.Dates(startDate.AddDays(2),
                [
                    new ReturnDepartureDate(startDate.AddDays(2+6), false),
                    new ReturnDepartureDate(startDate.AddDays(2+7), true)
                ]),
                new CacheDemandRoutesAlgorithm.Dates(startDate.AddDays(2),
                [
                    new ReturnDepartureDate(startDate.AddDays(2+8), true)
                ])
            };

            var dates = new List<CacheDemandRoutesAlgorithm.Dates>();

            Enumerable.Range(0, 3).ToList()
                .ForEach(i => dates
                    .AddRange(CacheDemandRoutesAlgorithm.GetRtSeparableDates(
                    date: startDate.AddDays(i),
                    returnDays: Enumerable.Range(6, 3).Order(),
                    isFirst: i == 0,
                    isLast: i == 2)));

            var sumOfRequiredDays = dates.Sum(d => d.Returns.Count(r => r.IsRequired));

            sumOfRequiredDays.Should().Be(5);
            var mostlyPreferredStayLength = GetMostlyPreferredStayLengths(dates);
            mostlyPreferredStayLength.Should().Be(7);

            dates.Should().BeEquivalentTo(expected, config => config.WithStrictOrdering());
        }

        private int GetMostlyPreferredStayLengths(List<CacheDemandRoutesAlgorithm.Dates> dates) => dates
            .SelectMany(d => d
                .Returns
                .Where(r => r.IsRequired)
                .Select(r => (int)(r.Return - d.Departure).TotalDays))
            .GroupBy(stayLength => stayLength, (stayLength, group) => (StayLength: stayLength, Count: group.Count()))
            .MaxBy(d => d.Count)
            .StayLength;


        [Fact]
        public void ShouldCreateWithPaxConfiguration_1_0_1_1()
        {
            var model = new CacheDemandModelInput
            {
                AirlinesFilter = [""],
                ProviderCode = ProviderCodeEnum.Amadeus,
                PartnerCode = "THOMASCOOKUKPACKAGES",
                DepartureAirportCode = "KRK",
                ArrivalAirportCode = "JFK",
                DepartureDateFrom = RelativeDate.Parse("2122-07-01"),
                DepartureDateTo = RelativeDate.Parse("2122-07-01"),
                IsRoundTrip = true,
                MinStayLength = 7,
                MaxStayLength = 7
            };

            var queueElements = _algorithm
               .GenerateQueueElements<QueueElement>(model, "")
               .ToList();

            queueElements
                .Should().ContainSingle()
                .Which.PaxConfiguration.Should().Be("*******");
        }
    }
}