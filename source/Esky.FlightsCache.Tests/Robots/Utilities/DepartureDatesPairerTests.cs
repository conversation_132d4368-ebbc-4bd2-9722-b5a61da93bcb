using Esky.FlightsCache.Robots.Utilities;
using FluentAssertions.Collections;
using FluentAssertions.Extensions;
using System.Linq;
using static Esky.FlightsCache.Robots.Utilities.DepartureDatesPairer;

namespace Esky.FlightsCache.Tests.Robots.Utilities;
public class DepartureDatesPairerTests
{
    [Fact]
    public void PairDates_WhenUnorderedButMatchableSameLengthLists_SameResultLengthAsInputLength()
    {
        var pairingResult = PairDates(
            [1.March(2024), 1.February(2024), 1.January(2024)],
            [15.March(2024), 15.January(2024), 15.February(2024)]);

        pairingResult.Should().HaveCount(3)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(1.January(2024), 15.January(2024)),
                new PairingResult(1.February(2024), 15.February(2024)),
                new PairingResult(1.March(2024), 15.March(2024))
             ]);
    }

    [Fact]
    public void PairDates_WhenEmptyInboundDatesList_AllOutboundDatesAreMatchedWithThemselves()
    {
        var pairingResult = PairDates(
            [1.January(2024), 1.February(2024), 1.March(2024)],
            []);

        pairingResult.Should().HaveCount(3)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(1.January(2024), 1.January(2024)),
                new PairingResult(1.February(2024), 1.February(2024)),
                new PairingResult(1.March(2024), 1.March(2024))
            ]);
    }

    [Fact]
    public void PairDates_WhenEmptyOutboundDatesList_AllInboundDatesAreMatchedWithThemselves()
    {
        var pairingResult = PairDates(
            [],
            [2.January(2024), 2.February(2024), 2.March(2024)]);

        pairingResult.Should().HaveCount(3)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(2.January(2024), 2.January(2024)),
                new PairingResult(2.February(2024), 2.February(2024)),
                new PairingResult(2.March(2024), 2.March(2024))
            ]);
    }

    [Fact]
    public void PairDates_WhenDatesCannotBeMatched_OutboundsLaterThenInbounds_DatesAreMatchedWithThemselves()
    {
        var pairingResult = PairDates(
            [1.February(2024), 2.February(2024), 3.February(2024)],
            [1.January(2024), 2.January(2024), 3.January(2024)]);

        pairingResult.Should().HaveCount(6)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(1.January(2024), 1.January(2024)),
                new PairingResult(2.January(2024), 2.January(2024)),
                new PairingResult(3.January(2024), 3.January(2024)),
                new PairingResult(1.February(2024), 1.February(2024)),
                new PairingResult(2.February(2024), 2.February(2024)),
                new PairingResult(3.February(2024), 3.February(2024))
            ]);
    }

    [Fact]
    public void PairDates_WhenUnpairedOutboundElementMappedWithItselves_PreferEarlierDatesAsAdditionalElement()
    {
        var pairingResult = PairDates(
            [10.January(2024), 20.January(2024)],
            [30.January(2024)]);

        pairingResult.Should().HaveCount(2)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(10.January(2024), 10.January(2024)),
                new PairingResult(20.January(2024), 30.January(2024))
            ])
            .And.NotContain(
                new PairingResult(20.January(2024), 20.January(2024))
            );
    }

    [Fact]
    public void PairDates_WhenUnpairedinboundElementMappedWithItselves_PreferEarlierDatesAsAdditionalElement()
    {
        var pairingResult = DepartureDatesPairer.PairDates(
            [10.January(2024)],
            [20.January(2024), 30.January(2024)]);

        pairingResult.Should().HaveCount(2)
            .And.ShouldSatisfyCommonPairingRequirements()
            .And.BeEquivalentTo([
                new PairingResult(10.January(2024), 30.January(2024)),
                new PairingResult(20.January(2024), 20.January(2024))
            ])
            .And.NotContain(
                new PairingResult(30.January(2024), 30.January(2024))
            ); ;
    }
}

public static class Extensions
{
    public static AndConstraint<GenericCollectionAssertions<PairingResult>> ShouldSatisfyCommonPairingRequirements(
        this GenericCollectionAssertions<PairingResult> pairingResultConstraint)
    {
        var constraint = pairingResultConstraint
            .AllSatisfy(x => x.outboundDate.Should().NotBeAfter(x.inboundDate));

        constraint.And.Subject.Select(x => x.outboundDate).Should().OnlyHaveUniqueItems();
        constraint.And.Subject.Select(x => x.inboundDate).Should().OnlyHaveUniqueItems();

        return constraint;
    }
}