using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.RobotsProducers.Algorithms;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Tests.Robots;

public class ApplyFlexTests
{
    [Fact]
    public void ContinuousRangeTest()
    {
        var dates = Enumerable.Range(1, 29).Select(i => new DateTime(2024, 1, i)).ToArray();

        var result = dates.ApplyFlex(3).ToArray();

        result
            .Should()
            .BeEquivalentTo(
                new List<DateTime>
                {
                    new(2024, 1, 4),
                    new(2024, 1, 11),
                    new(2024, 1, 18),
                    new(2024, 1, 25),
                    new(2024, 2, 1),
                },
                opt => opt.WithStrictOrdering()
            );
    }

    [Fact]
    public void FlexLessThanDepartureDateGaps_CoversAllDepartureDates()
    {
        var dates = new DateTime[]
        {
            new(2024, 1, 5),
            new(2024, 1, 10),
            new(2024, 1, 15),
            new(2024, 1, 20),
            new(2024, 1, 25)
        };

        var result = dates.ApplyFlex(3).ToArray();
        
        result.Should().HaveCount(3);
        result[0].Should().Be(new DateTime(2024, 1, 8), "5 - 11 range covered");
        result[1].Should().Be(new DateTime(2024, 1, 18), "15 - 21 range covered");
        result[2].Should().Be(new DateTime(2024, 1, 28), "25 - 31 range covered");
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void InvalidFlex_ReturnsSelf(int flex)
    {
        var dates = new DateTime[]
        {
            new(2024, 1, 5),
            new(2024, 1, 10)
        };

        var result = dates.ApplyFlex(flex);

        result.Should().BeSameAs(dates);
    }

    [Fact]
    public void Route_ApplyFlex()
    {
        int[] bbbDays = [1, 2, 3, 4, 5, 10, 11, 14, 15, 18, 20, 21, 22, 23, 27, 29, 31];
        int[] cccDays = [11, 14, 15, 18, 23, 29, 31];
        List<Route> routes =
        [
            new("AAA", "BBB", bbbDays.Select(day => day.January(2022)).ToList()),
            new("AAA", "CCC", cccDays.Select(day => day.January(2022)).ToList())
        ];

        var routesWithFlex = routes.ApplyFlex(3, 5.January(2022), 27.January(2022)).ToArray();

        routesWithFlex.Should().HaveCount(2);
        routesWithFlex[0].ArrivalCode.Should().Be("BBB");
        routesWithFlex[0].FlyingDates.Should().BeEquivalentTo([8.January(2022), 17.January(2022), 24.January(2022)], opt => opt.WithStrictOrdering());
        routesWithFlex[^1].ArrivalCode.Should().Be("CCC");
        routesWithFlex[^1].FlyingDates.Should().BeEquivalentTo([14.January(2022), 21.January(2022)], opt => opt.WithStrictOrdering());
    }
}