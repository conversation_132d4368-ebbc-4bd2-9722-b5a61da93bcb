using Esky.FlightsCache.RobotsProducers.Composite;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.Composite;

public class CompositeQueueElementProducerTests
{
    private readonly IRouteDatesProvider _routeDatesProvider = Substitute.For<IRouteDatesProvider>();
    private readonly IQueueElementPublisherBuilder<CompositeQueueElement> _publisherBuilder =
        Substitute.For<IQueueElementPublisherBuilder<CompositeQueueElement>>();

    private readonly Action<string> _logStub = _ => { };
    
    private readonly CompositeQueueElementProducer _sut;

    public CompositeQueueElementProducerTests()
    {
        _sut = new CompositeQueueElementProducer(_routeDatesProvider, _publisherBuilder);
    }

    [Fact]
    public async Task CreateQueueElements_WhenNoNestedLayers_ThenFlatQueueElements()
    {
        var jobSettings = CreateJobSettings(1, 1.January(2025),7.January(2025) ) with
        {
            Flex = 3,
        };
        _routeDatesProvider.GetRouteDates(jobSettings, _logStub)
            .Returns(_ => [
                new RouteDate("LGW-HRG", 4.January(2025), 4.January(2025))
            ]);
        
        var result = await _sut.CreateQueueElements(jobSettings, _logStub);

        result.Should().BeEquivalentTo([new CompositeQueueElement
        {
            ProviderCode = Provider(1),
            Supplier = Supplier(1),
            PartnerCode = PartnerCode(1),
            NextQueueElements = [],
            DepartureCode = "LGW",
            ArrivalCode = "HRG",
            PaxConfigurations = [PaxConfiguration(1)],
            IsRoundTrip = true,
            Flex = 3,
            DepartureDay = 4.January(2025),
            ReturnDepartureDay = 4.January(2025),
            
            DeleteDepartureDayFrom = 1.January(2025),
            DeleteDepartureDayTo = 7.January(2025),
            DeleteReturnDepartureDayFrom = 1.January(2025),
            DeleteReturnDepartureDayTo = 7.January(2025)
        }]);
    }
    
    [Fact]
    public async Task CreateQueueElements_WhenNestedLayers_ThenNestedQueueElements()
    {
        var jobSettings = CreateJobSettings(1, 1.January(2025),2.January(2025) ) with
        {
            ProceedNextOnSuccess = false,
            OverrideSettings = new CompositeQueueElement.Override(new CompositeQueueElement.ProviderOverride(Provider(100)), new CompositeQueueElement.SupplierOverride(Supplier(100))),
            Next = CreateNextInCompositeJobSettings(2) with
            {
                ProceedNextOnSuccess = true,
                Next = CreateNextInCompositeJobSettings(3) with { ProceedNextOnSuccess = false }
            }
        };
        _routeDatesProvider.GetRouteDates(Arg.Any<CompositeJobSettings>(), _logStub)
            .Returns(_ => [
                new RouteDate("LGW-HRG", 1.January(2025), 2.January(2025))
            ]);
        
        var result = await _sut.CreateQueueElements(jobSettings, _logStub);

        result.Should().BeEquivalentTo([
            CompositeQueueElement(Provider(1), Supplier(1), PartnerCode(1), PaxConfiguration(1), depth: 0,
                proceedOnSuccess: false,
                next: () =>
                [
                    CompositeQueueElement(Provider(2), Supplier(2), PartnerCode(2), PaxConfiguration(2), depth: 1,
                        proceedOnSuccess: true,
                        next: () =>
                        [
                            CompositeQueueElement(Provider(3), Supplier(3), PartnerCode(3), PaxConfiguration(3), depth: 2,
                                proceedOnSuccess: false,
                                next: () => [])
                        ])
                ])
        ]);
        return;

        CompositeQueueElement CompositeQueueElement(
            ProviderCodeEnum providerCodeEnum, 
            string supplier, 
            string partnerCode,
            string paxConfiguration,
            int depth,
            bool proceedOnSuccess,
            Func<IReadOnlyCollection<CompositeQueueElement>> next)
        {
            return new CompositeQueueElement
            {
                ProceedNextOnSuccess = proceedOnSuccess,
                ProviderCode = providerCodeEnum,
                Supplier = supplier,
                PartnerCode = partnerCode,
                NextQueueElements = next(),
                DepartureCode = "LGW",
                ArrivalCode = "HRG",
                PaxConfigurations = [paxConfiguration],
                IsRoundTrip = true,
                Flex = 0,
                Depth = depth,
                OverrideSettings = jobSettings.OverrideSettings,
                
                DepartureDay = 1.January(2025),
                DeleteDepartureDayFrom = 1.January(2025),
                DeleteDepartureDayTo = 1.January(2025),
                
                ReturnDepartureDay = 2.January(2025),
                DeleteReturnDepartureDayFrom = 2.January(2025),
                DeleteReturnDepartureDayTo = 2.January(2025),
            };
        }
    }
    
    [Fact]
    public async Task CreateQueueElements_WhenNestedJobFlexZero_ThenNestedQueueElementsAreSingleDayDepartures()
    {
        var jobSettings = CreateJobSettings(1, 1.January(2025),14.January(2025) ) with
        {
            Flex = 3,
            Next = CreateNextInCompositeJobSettings(1) with
            {
                Flex = 0,
                SkipTheSameDepartureDateAsReturnDeparture = true,
            }
        };
        _routeDatesProvider.GetRouteDates(Arg.Is<CompositeJobSettings>(x => x.Flex == 3), _logStub)
            .Returns(_ => [
                new RouteDate("LGW-HRG", 4.January(2025), 4.January(2025)),
                new RouteDate("LGW-HRG", 11.January(2025), 11.January(2025))
            ]);
        _routeDatesProvider.GetRouteDates(Arg.Is<CompositeJobSettings>(x => x.Flex == 0 && x.SkipTheSameDepartureDateAsReturnDeparture), _logStub)
            .Returns(_ => Enumerable.Range(1, 13).Select(x => 
                new RouteDate("LGW-HRG", x.January(2025), (x+1).January(2025))));
        
        var result = await _sut.CreateQueueElements(jobSettings, _logStub);

        result.Should().HaveCount(2);
        
        result[0].DepartureDay.Should().Be(4.January(2025));
        result[0].ReturnDepartureDay.Should().Be(4.January(2025));
        result[0].Flex.Should().Be(3);
        result[0].NextQueueElements.Should().HaveCount(7);
        result[0].NextQueueElements
            .Zip(Enumerable.Range(1, 7)).ToList()
            .ForEach(tuple =>
            {
                var (qe, index) = tuple;
                qe.DepartureDay.Should().Be(index.January(2025));
                qe.ReturnDepartureDay.Should().Be((index + 1).January(2025));
                qe.Flex.Should().Be(0);
            });

        result[1].DepartureDay.Should().Be(11.January(2025));
        result[1].ReturnDepartureDay.Should().Be(11.January(2025));
        result[1].Flex.Should().Be(3);
        result[1].NextQueueElements.Should().HaveCount(6);
        result[1].NextQueueElements
            .Zip(Enumerable.Range(8, 13)).ToList()
            .ForEach(tuple =>
            {
                var (qe, index) = tuple;
                qe.DepartureDay.Should().Be(index.January(2025));
                qe.ReturnDepartureDay.Should().Be((index + 1).January(2025));
            });
    }
    
    private static ProviderCodeEnum Provider(int p) => (ProviderCodeEnum)p;
    private static string Supplier(int s) => $"supplier{s}";
    private static string PartnerCode(int c) => $"partnerCode{c}";
    private static string PaxConfiguration(int a) => $"{a}.0.0.0";

    private static CompositeJobSettings CreateJobSettings(int identifiers, RelativeDate start, RelativeDate end) => new()
    {
        Provider = Provider(identifiers),
        Supplier = Supplier(identifiers),
        PartnerCode = PartnerCode(identifiers),
        PaxConfigurations = [PaxConfiguration(identifiers)],
        RelativeDateRange = new RelativeDateRange(start, end),
        Group = "Composite",
        Suffix = null,
        Cron = Cron.Never(),
        Airlines = [],
    };

    private static NextInCompositeJobSettings CreateNextInCompositeJobSettings(int identifiers) => new()
    {
        ProceedNextOnSuccess = false,
        Provider = Provider(identifiers),
        Supplier = Supplier(identifiers),
        PartnerCode = PartnerCode(identifiers),
        PaxConfigurations = [PaxConfiguration(identifiers)],
    };
}