using Esky.FlightsCache.Robots;
using System;
using Xunit;

namespace Esky.FlightsCache.Tests;

public class PaxConfigurationTests
{
    [Fact]
    public void PaxConfiguration_EmptyCtor_CreatesEmptyPax()
    {
        var pax = new PaxConfiguration();

        Assert.Equal(0, pax.Adult);
        Assert.Equal(0, pax.Youth);
        Assert.Equal(0, pax.Child);
        Assert.Equal(0, pax.Infant);
    }

    [Fact]
    public void PaxConfiguration_StringCtor_ParsesPax()
    {
        var pax = new PaxConfiguration("1.2.3.4");

        Assert.Equal(1, pax.Adult);
        Assert.Equal(2, pax.Youth);
        Assert.Equal(3, pax.Child);
        Assert.Equal(4, pax.Infant);
    }

    [Theory]
    [InlineData(1, 2, 3, 4)]
    [InlineData(1, 2, null, 4)]
    [InlineData(null, 2, null, 0)]
    [InlineData(null, null, null, null)]
    public void PaxConfiguration_NumberCtor_ParsesPax(
        int? adult = null,
        int? youth = null,
        int? child = null,
        int? infant = null)
    {
        var pax = new PaxConfiguration(adult, youth, child, infant);

        Assert.Equal(adult ?? 0, pax.Adult);
        Assert.Equal(youth ?? 0, pax.Youth);
        Assert.Equal(child ?? 0, pax.Child);
        Assert.Equal(infant ?? 0, pax.Infant);
    }

    [Theory]
    [InlineData("1.0,0,0")]
    [InlineData("1.1.0")]
    [InlineData("1.1..0")]
    [InlineData("1.X.0.0")]
    public void PaxConfiguration_Throws_OnInvalidInput(string paxConfig)
    {
        Assert.Throws<ArgumentException>(() => new PaxConfiguration(paxConfig));
    }

    [Fact]
    public void PaxConfiguration_ImplicitToStringConversion()
    {
        var pax = new PaxConfiguration(1, 2, 3, 4);

        Assert.Equal("1.2.3.4", pax);
    }

    [Fact]
    public void PaxConfiguration_ImplicitFromStringConversion()
    {
        PaxConfiguration pax = "1.2.3.4";

        Assert.Equal("1.2.3.4", pax.ToString());
    }
}