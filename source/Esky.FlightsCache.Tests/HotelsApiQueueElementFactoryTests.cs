using Esky.FlightsCache.RobotsProducers.Producers.Hotels;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;

namespace Esky.FlightsCache.Tests;

public class HotelsApiQueueElementFactoryTests
{
    [Fact]
    public void FlexConfiguration_Returns_CheckInAscendingOrder()
    {
        var stayLengths = new[] { 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14 };
        var occupancy = new RoomConfiguration[]
        {
            new() { Adults = 1, ChildAges = [] },
            new() { Adults = 1, ChildAges = [11] },
            new() { Adults = 1, ChildAges = [17] },
            new() { Adults = 2, ChildAges = [] },
            new() { Adults = 2, ChildAges = [1] },
            new() { Adults = 2, ChildAges = [11] },
            new() { Adults = 2, ChildAges = [17] },
            new() { Adults = 2, ChildAges = [11, 11] },
            new() { Adults = 2, ChildAges = [11, 17] },
            new() { Adults = 2, ChildAges = [17, 17] },
            new() { Adults = 3, ChildAges = [] },
            new() { Adults = 4, ChildAges = [] },
        };
        var config = new HotelRobotFlexConfiguration
        {
            HotelMetaCodes = [100, 200, 300],
            MinCheckinDaysFromGeneration = 5,
            MaxCheckinDaysFromGeneration = 365,
            StayLengths = stayLengths,
            Occupancy = occupancy,
            PartnerCode = "ESKYPLPACKAGES",
        };
        var factory = new HotelsApiQueueElementsFactory();

        var elements = factory.CreateFromConfiguration(config);

        elements.Should().BeInAscendingOrder(e => e.CheckInDate);
    }
}