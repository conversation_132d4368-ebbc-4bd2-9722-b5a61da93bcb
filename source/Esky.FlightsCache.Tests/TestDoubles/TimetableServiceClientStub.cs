using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.TestDoubles
{
    internal class TimetableServiceClientStub : ITimetableServiceClient
    {
        private Dictionary<(string airlineCode, string departureCode, string arrivalCode), int[]> _connections = new();

        IList<ConnectionNetwork> GetConnectionNetwork(string airlineCode) => _connections
            .Where(x => x.Key.airlineCode == airlineCode)
            .GroupBy(x => x.Key.departureCode)
            .Select(g => new ConnectionNetwork
            {
                DepartureAirportCode = g.Key,
                ArrivalAirportCodes = g.Select(x => new ConnectionNetworkArrivalAirport
                {
                    ArrivalCode = x.Key.arrivalCode,
                    Months = x.Value.ToList()
                }).ToList()
            }).ToList();

        public TimetableServiceClientStub AddConnection(string airlineCode, string departureCode, string arrivalCode, params int[] months)
        {
            _connections[(airlineCode, departureCode, arrivalCode)] = months;
            return this;
        }

        public Task<IList<ConnectionNetwork>> GetConnectionNetworkByAirline(string airlineCode)
            => Task.FromResult(GetConnectionNetwork(airlineCode));

        public Task<IList<ConnectionNetwork>> GetConnectionNetworkByProviderCode(int providerCode, IEnumerable<string> suppliers, bool? newRoutes = null)
            => throw new NotImplementedException();

        public Task<IList<DateTime>> GetFlyingDays(string departureCode, string arrivalCode, IReadOnlyCollection<string> airlineCodes)
        {
            throw new NotImplementedException();
        }

        public Task<IList<DateTime>> GetFlyingDays(string airlineCode, string departureCode, string arrivalCode)
            => throw new NotImplementedException();
    }
}
