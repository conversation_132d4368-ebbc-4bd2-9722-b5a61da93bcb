<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions" Version="6.12.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" Version="8.8.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="Verify.Xunit" Version="28.3.2" />
    <PackageReference Include="xunit" Version="2.9.2" />  

    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="4.1.0" />
    <DotNetCliToolReference Include="dotnet-xunit" Version="2.3.1" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Utils\ResponseData\ESKY_80_WAW_JKT_20221120_20221127.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.RobotsProducers\Esky.FlightsCache.RobotsProducers.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots\Esky.FlightsCache.Robots.csproj" />
  </ItemGroup>
</Project>
