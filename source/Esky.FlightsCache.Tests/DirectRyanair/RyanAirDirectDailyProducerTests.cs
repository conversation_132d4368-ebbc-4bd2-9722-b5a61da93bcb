using Esky.FlightsCache.RobotsProducers.Controllers;
using Esky.FlightsCache.RobotsProducers.DirectRyanair;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire;
using Hangfire.MemoryStorage;
using MassTransit;
using MassTransit.Testing;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Time.Testing;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.DirectRyanair;

public class RyanAirDirectDailyProducerTests
{
    [Fact]
    public async Task ProducesCorrectQueueElements()
    {
        var timetableServiceClient = Substitute.For<ITimetableServiceClient>();
        timetableServiceClient
            .GetConnectionNetworkByAirline("FR")
            .Returns(
                new List<ConnectionNetwork>
                {
                    new()
                    {
                        DepartureAirportCode = "WMI",
                        ArrivalAirportCodes =
                        [
                            new ConnectionNetworkArrivalAirport { ArrivalCode = "STN", Months = [202201, 202202] },
                            new ConnectionNetworkArrivalAirport { ArrivalCode = "LGW", Months = [202201, 202202] }
                        ]
                    }
                }
            );
        timetableServiceClient
            .GetFlyingDays("WMI", "STN", Arg.Is<IReadOnlyCollection<string>>(e => e.Single() == "FR"))
            .Returns(Enumerable.Range(1, 60).Select(e => 1.January(2022).AddDays(e)).ToList());
        timetableServiceClient
            .GetFlyingDays("WMI", "LGW", Arg.Is<IReadOnlyCollection<string>>(e => e.Single() == "FR"))
            .Returns(Enumerable.Range(1, 10).Select(e => 1.January(2022).AddDays(e)).ToList());
        var fakeTimeProvider = new FakeTimeProvider(1.January(2022));

        await using var app = new WebApplicationFactory<RyanAirDirectRobotsController>()
            .WithWebHostBuilder(
                b => b
                    .ConfigureTestServices(
                        services =>
                        {
                            services.AddMassTransitTestHarness();
                            services.AddHangfire(e => e.UseMemoryStorage());

                            services.Replace(ServiceDescriptor.Singleton<TimeProvider>(fakeTimeProvider));
                            services.Replace(ServiceDescriptor.Singleton(_ => timetableServiceClient));
                        }
                    )
            );

        var serviceProvider = app.Services.CreateScope().ServiceProvider;

        // act
        var producer = serviceProvider.GetRequiredService<RyanAirDirectDailyProducer>();
        await producer.ProduceDailyForward(9, 30);

        serviceProvider
            .GetTestHarness()
            .Published
            .Select<RyanAirDirectQueueElement>()
            .Select(e => e.Context.Message)
            .Should()
            .BeEquivalentTo(
                Enumerable
                    .Range(9, 30)
                    .Select(e => Element("STN", e))
                    .Concat(Enumerable.Range(9, 2).Select(e => Element("LGW", e)))
                    .OrderBy(e => e.DepartureDay),
                opt => opt.WithStrictOrdering(),
                "ordered by departure day"
            );

        return;

        RyanAirDirectQueueElement Element(string arrival, int departureDateOffset)
        {
            var element = new RyanAirDirectQueueElement
            {
                DepartureCode = "WMI",
                ArrivalCode = arrival,
                DepartureDay = 1.January(2022).AddDays(departureDateOffset),
                PartnerCode = "ADMIN",
                DeleteDepartureDayFrom = 1.January(2022).AddDays(departureDateOffset),
                DeleteDepartureDayTo = 1.January(2022).AddDays(departureDateOffset),
                Flex = 0,
                ProviderCode = ProviderCodeEnum.DirectRyanair,
                ReturnDepartureDay = null,
                DeleteReturnDepartureDayFrom = null,
                DeleteReturnDepartureDayTo = null
            };
            return element;
        }
    }
}