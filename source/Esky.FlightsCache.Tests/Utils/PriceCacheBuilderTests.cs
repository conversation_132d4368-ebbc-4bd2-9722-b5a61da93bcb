using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;

namespace Esky.FlightsCache.Tests.Utils
{
    public class PriceCacheBuilderTests
    {
        readonly PriceCacheBuilder _sut = new();
        SearchFlightsProviderQuery _query = new()
        {
            Passengers = [
                new SearchFlightsProviderQuery.Passenger() { Code = PassengerTypeEnum.ADT, Count = 1 },
                new SearchFlightsProviderQuery.Passenger() { Code = PassengerTypeEnum.CHD, Count = 1 },
                new SearchFlightsProviderQuery.Passenger() { Code = PassengerTypeEnum.INF, Count = 1 },
            ]
        };

        private readonly FlightDto _amadeusOffer = new()
        {
            Legs = [
                new LegDto() { Segments = [ new SegmentDto()] },
                new LegDto() { Segments = [ new SegmentDto()] },
            ],
            Prices = [
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 1546 },
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = 906.08m },
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 1200 },
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Tax, Amount = 906.08m },
                new PriceDto { PassengerType = PassengerTypeEnum.INF, PriceType = PriceType.Base, Amount = 80 },
                new PriceDto { PassengerType = PassengerTypeEnum.INF, PriceType = PriceType.Tax, Amount = 0m },
            ]
        };

        [Fact]
        public void Amadeus_Adt()
        {
            var result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.ADT, legIndex: 0);

            Assert.Equal(1546, result.BasePrice);
            Assert.Equal(906.08m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.ADT, legIndex: 1);

            result.BasePrice.Should().Be(0);
            result.TaxPrice.Should().Be(0);
        }

        [Fact]
        public void Amadeus_Chd()
        {
            var result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.CHD, legIndex: 0);

            Assert.Equal(1200, result.BasePrice);
            Assert.Equal(906.08m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.ADT, legIndex: 1);

            result.BasePrice.Should().Be(0);
            result.TaxPrice.Should().Be(0);
        }

        [Fact]
        public void Amadeus_Inf()
        {
            var result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.INF, legIndex: 0);

            Assert.Equal(80, result.BasePrice);
            Assert.Equal(0m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, _amadeusOffer, PassengerTypeEnum.ADT, legIndex: 1);

            result.BasePrice.Should().Be(0);
            result.TaxPrice.Should().Be(0);
        }

        private readonly FlightDto _travelFusionOffer = new()
        {
            Legs = [
                new LegDto() { Segments = [ new SegmentDto()] },
                new LegDto() { Segments = [ new SegmentDto()] },
            ],
            Prices = [
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = 39.03m, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = 39.04m, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 31, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 31, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = 43.69m, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = 43.68m, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 341, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 341, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Tax, Amount = 39.03m, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Tax, Amount = 39.04m, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 20, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 20, Segments = [0]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Tax, Amount = 43.69m, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Tax, Amount = 43.68m, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 300, Segments = [1]},
                new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 300, Segments = [1]},
            ]
        };

        [Fact]
        public void TravelFusion_Adt()
        {
            var result = _sut.Build(_query, _travelFusionOffer, PassengerTypeEnum.ADT, legIndex: 0);

            Assert.Equal(31m, result.BasePrice);
            Assert.Equal(39.03m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, _travelFusionOffer, PassengerTypeEnum.ADT, legIndex: 1);

            Assert.Equal(341m, result.BasePrice);
            Assert.Equal(43.69m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);
        }

        [Fact]
        public void TravelFusion_Chd()
        {
            var result = _sut.Build(_query, _travelFusionOffer, PassengerTypeEnum.CHD, legIndex: 0);

            Assert.Equal(20, result.BasePrice);
            Assert.Equal(39.03m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, _travelFusionOffer, PassengerTypeEnum.CHD, legIndex: 1);

            Assert.Equal(300, result.BasePrice);
            Assert.Equal(43.69m, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);
        }

        [Fact]
        public void MulitisegmentSeparableOffer()
        {
            FlightDto offer = new()
            {
                Legs = [
                    new LegDto() { Segments = [ new SegmentDto(), new SegmentDto() ] },
                    new LegDto() { Segments = [ new SegmentDto(), new SegmentDto() ] },
                ],
                Prices = [
                    new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 30, Segments = [0, 1]},
                    new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 30, Segments = [0, 1]},
                    new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 40, Segments = [2, 3]},
                    new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 40, Segments = [2, 3]},
                ]
            };

            var result = _sut.Build(_query, offer, PassengerTypeEnum.ADT, legIndex: 0);

            Assert.Equal(30, result.BasePrice);
            Assert.Equal(0, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, offer, PassengerTypeEnum.ADT, legIndex: 1);

            Assert.Equal(40, result.BasePrice);
            Assert.Equal(0, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);
        }


        [Fact]
        public void SSCProvider()
        {
            FlightDto sscOffer = new()
            {
                Prices = [
                    new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = 69 },
                    new PriceDto { PassengerType = PassengerTypeEnum.CHD, PriceType = PriceType.Base, Amount = 59 },
                    new PriceDto { PassengerType = PassengerTypeEnum.INF, PriceType = PriceType.Base, Amount = 120 },
                ]
            };

            var result = _sut.Build(_query, sscOffer, PassengerTypeEnum.ADT, legIndex: 0);

            Assert.Equal(69, result.BasePrice);
            Assert.Equal(0, result.TaxPrice);
            Assert.Equal(1, result.MinimumNumberOfPaxes);

            result = _sut.Build(_query, sscOffer, PassengerTypeEnum.ADT, legIndex: 1);

            result.BasePrice.Should().Be(0);
            result.TaxPrice.Should().Be(0);
        }
    }
}
