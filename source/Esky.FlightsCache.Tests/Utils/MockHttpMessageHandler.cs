using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.Tests.Utils
{
    internal class MockHttpMessageHandler : HttpMessageHandler
    {
        private string Response { get; set; }
        private HttpStatusCode StatusCode { get; set; }

        public string Input { get; private set; }
        public int NumberOfCalls { get; private set; }

        public void Configure(HttpStatusCode statusCode)
        {
            StatusCode = statusCode;
        }

        public void Configure(string response, HttpStatusCode statusCode)
        {
            Response = response;
            StatusCode = statusCode;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
            CancellationToken cancellationToken)
        {
            NumberOfCalls++;
            if (request.Content != null)
            {
                Input = await request.Content.ReadAsStringAsync(cancellationToken);
            }
            return new HttpResponseMessage
            {
                StatusCode = StatusCode,
                Content = new StringContent(Response ?? string.Empty)
            };
        }
    }
}
