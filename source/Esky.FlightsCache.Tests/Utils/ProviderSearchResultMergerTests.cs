using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using Xunit;
using static Esky.Flights.Integration.Providers.Contract.SearchFlightsProviderQuery;

namespace Esky.FlightsCache.Tests.Utils
{
    public class ProviderSearchResultMergerTests
    {
        [Fact]
        public void ShouldMergeTwoOneWayFlightsIntoOneRoundTrip()
        {
            using var resourceStream = typeof(ProviderSearchResultMergerTests).GetTypeInfo().Assembly.GetManifestResourceStream("Esky.FlightsCache.Tests.Utils.ResponseData.ESKY_80_WAW_JKT_20221120_20221127.json");
            using var streamReader = new StreamReader(resourceStream, Encoding.UTF8);

            var response = JsonConvert.DeserializeObject<SearchResponse>(streamReader.ReadToEnd());

            var query = new SearchFlightsProviderQuery
            {
                Legs = new List<Leg>
                {
                    new Leg { DepartureCode = "WAW", ArrivalCode = "JKT", DepartureDate = new DateTime(2022,11,20)},
                    new Leg { DepartureCode = "JKT", ArrivalCode = "WAW", DepartureDate = new DateTime(2022, 11, 27)}
                }
            };

            var l1 = response.Data[0];
            var l2 = response.Data.Where(o => o.Legs.First().Segments[0].OriginAirport == "CGK").First();

            var input = new List<FlightDto> { l1, l2 };

            ProviderSearchResultMerger merger = new ProviderSearchResultMerger(new PriceCacheBuilder());
            var result = merger.Map(query, input).ToList();

            Assert.Single(result);

            var totalBase = l1.Prices.Single(o => o.PriceType == PriceType.Base).Amount + l2.Prices.Single(o => o.PriceType == PriceType.Base).Amount;
            var totalTax = l1.Prices.Single(o => o.PriceType == PriceType.Tax).Amount + l2.Prices.Single(o => o.PriceType == PriceType.Tax).Amount;

            Assert.Equal(totalBase, result.First().Prices.Single(o => o.PriceType == PriceType.Base).Amount);
            Assert.Equal(totalTax, result.First().Prices.Single(o => o.PriceType == PriceType.Tax).Amount);
        }
    }
}
