using Esky.FlightsCache.Database;
using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.OpenTelemetry;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.ProviderMapping;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.Robots.BigQuery;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache;
using Esky.FlightsCache.RobotsProducers.Algorithms;
using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.DirectRyanair;
using Esky.FlightsCache.RobotsProducers.Examples;
using Esky.FlightsCache.RobotsProducers.Hangfire;
using Esky.FlightsCache.RobotsProducers.HealthChecks;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;
using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.FlightsCache.RobotsProducers.SSC;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.FlightsCache.ServiceBus;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.BigQuery.V2;
using Hangfire;
using Hangfire.Console;
using Hangfire.Dashboard;
using Hangfire.MemoryStorage;
using Hangfire.Mongo;
using Hangfire.Mongo.Migration.Strategies;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using OpenTelemetry.Trace;
using RabbitMQ.Client;
using Swashbuckle.AspNetCore.Filters;
using System;
using System.IO;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace Esky.FlightsCache.RobotsProducers.IoC
{
    public static class CoreConfiguration
    {
        public static IServiceCollection RegisterAllServices(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddScoped<IJobConverter<GroupConfiguration, JobSettings>, GroupConfigurationConverter>()
                .AddScoped<IJobConverter<CompositeGroupConfiguration, CompositeJobSettings>, CompositeGroupConfigurationConverter>();
            
            services.AddSingleton(TimeProvider.System);
            services.AddHttpClient(Options.DefaultName, client => client.DefaultRequestHeaders.UserAgent.ParseAdd("esky-flightscache-robotsproducers"));
            services.ConfigurePartnerSettings(configuration);
            services.AddSingleton<ITimetableServiceClient, TimetableServiceClient.TimetableServiceClient>();

            services.Configure<RobotsProducerEvenlyDistributionSettings>(configuration.GetSection("RobotsProducerEvenlyDistributionSettings"));
            services.AddSingleton(s => s.GetService<IOptions<RobotsProducerEvenlyDistributionSettings>>()!.Value);

            //Services
            services.AddSingleton<IQueuePublisher, QueuePublisher>();
            services.AddScoped<IQueueElementCreator, QueueElementCreator>();
            services.AddScoped<ITimetableAlgorithm, TimetableAlgorithm>();
            services.AddScoped<IDayByDayAlgorithm, DayByDayAlgorithm>();
            services.AddScoped<IWeekDayAlgorithm, WeekDayAlgorithm>();
            services.AddScoped<IEvenlyDistributionParametersBuilder, EvenlyDistributionParametersBuilder>();
            services.ConfigureServiceBus(configuration);
            services.ConfigureMessageProcessor(configuration);
            services.AddSwagger();
            services.AddBackgroundProcessing(configuration);
            services.AddCustomHealthCheck(configuration);
            services.AddTransient(typeof(IQueueElementPublisherBuilder<>), typeof(QueueElementPublisherBuilder<>));
            services.AddSingleton(typeof(QueuePublisherHangfireWrapper<>));
            services.AddSingleton<IConnectionNetworkProvider, TimetableConnectionNetworkProvider>();

            // group jobs
            services.AddSingleton<IResolverFactory, ResolverFactory>();
            services.AddSingleton<IRouteDatesProvider, RouteDatesProvider>();

            //TravelFusion
            services.AddScoped<TravelFusionProducer>();
            services.AddScoped<ITravelFusionSupplierRepository, TravelFusionSupplierRepository>();
            services.AddScoped<TravelFusionCategorizedProducer>();
            services.AddScoped<IFlightsCacheService, FlightsCacheService>();
            services.AddScoped<DiscoverRoutesProducer>();
            services.AddScoped<TravelFusionSuppliersProducer>();
            services.AddScoped<DiscoverRoutesProducer>();

            //RyanAir
            services.AddAirportCurrencies(configuration);
            services.AddTransient<RyanAirDirectDailyProducer>();

            // CacheDemand
            services.AddScoped<CacheDemandService>();
            services.AddScoped<ICacheDemandRoutesAlgorithm, CacheDemandRoutesAlgorithm>();
            services.AddScoped<ICacheDemandInputMapper, CacheDemandInputMapper>();
            services.AddScoped<ICacheDemandProducer, CacheDemandProducer>();
            services.AddScoped<CacheDemandProducer>();
            services.AddSingleton<ICacheDemandJobRepository, CacheDemandJobRepository>();
            services.Configure<DatabaseSettings>(configuration.GetSection("DatabaseSettings"));
            services.AddSingleton(s => s.GetService<IOptions<DatabaseSettings>>()!.Value);
            services.AddScoped<IGoogleRepository, GoogleRepository>();

            //Hotels API
            services.AddScoped<IHotelsRobotsProducer, HotelsRobotsProducer>();
            services.AddScoped<IHotelsRobotsFlexProducer, HotelsRobotsFlexProducer>();
            services.AddScoped<IHotelsRobotsInstantFlexProducer, HotelsRobotsInstantFlexProducer>();
            services.AddScoped<IHotelsApiQueueElementsFactory, HotelsApiQueueElementsFactory>();
            services.AddScoped<IHotelJobsConfigurator, HotelJobsConfigurator>();
            services.AddSingleton<IHotelInventoryRobotsConfigFeedRepository, HotelInventoryRobotsConfigFeedRepository>();

            services.ConfigureOpenTelemetry(
                configuration.GetSection("OpenTelemetry").Get<OpenTelemetryOptions>()!,
                tracesConfigurator: b => b.SetSampler(new AlwaysOffSampler())
            ).RegisterOpenTracingShim();
            
            services.AddSingleton<ICoverageStorage, CoverageBigQueryStorage>();
            services.Configure<BigQuerySettings>(configuration.GetSection("BigQuerySettings"));
            services.AddSingleton(sp => sp.GetRequiredService<IOptions<BigQuerySettings>>().Value);
            
            services.AddSingleton<Lazy<BigQueryClient>>(provider =>
            {
                var settings = provider.GetRequiredService<BigQuerySettings>();
                return new Lazy<BigQueryClient>(() =>
                {
                    var credentials = File.ReadAllText(settings.GoogleCredentialsPath);
                    return BigQueryClient.Create(settings.ProjectId, GoogleCredential.FromJson(credentials));
                });
            });

            return services;
        }

        public static IServiceCollection ConfigureServiceBus(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddLogging()
                .AddBusWithInstrumentation(
                    configuration.GetSection("RobotServiceBus").Get<BusSettings>(),
                    bus =>
                    {
                        bus.Configure<MassTransitHostOptions>(opt => opt.WaitUntilStarted = true);
                        bus.ConfigureHealthCheckOptions(x =>
                        {
                            x.Tags.Add(HealthChecks.HealthChecks.Tags.Readiness);
                            x.Tags.Add(HealthChecks.HealthChecks.Tags.Liveness);
                        });
                    },
                    (context, configurator) =>
                    {
                        configurator.Message<SSCQueueElement>(c => c.SetEntityName(SSCQueueElement.RobotPrefix));
                        configurator.Send<SSCQueueElement>(c => c.UseRoutingKeyFormatter(x => x.Message.Supplier.ToLowerInvariant()));
                        configurator.Publish<SSCQueueElement>(
                            c =>
                            {
                                c.ExchangeType = ExchangeType.Direct;
                                c.BindAlternateExchangeQueue(SSCQueueElement.QueueNameForUnmatched());
                            }
                        );
                        configurator.Message<SSCCalendarQueueElement>(c => c.SetEntityName(SSCCalendarQueueElement.RobotPrefix));
                        configurator.Send<SSCCalendarQueueElement>(c => c.UseRoutingKeyFormatter(x => x.Message.Supplier.ToLowerInvariant()));
                        configurator.Publish<SSCCalendarQueueElement>(
                            c =>
                            {
                                c.ExchangeType = ExchangeType.Direct;
                                c.BindAlternateExchangeQueue(SSCCalendarQueueElement.QueueNameForUnmatched());
                            }
                        );
                    }
                );

            services.Configure<TimetableServiceConfiguration>(configuration.GetSection("ExternalServices:TimeTableService"));
            services.Configure<CacheDemandSettings>(configuration.GetSection("CacheDemandSettings"));
            services.AddSingleton(s => s.GetService<IOptions<CacheDemandSettings>>()!.Value);
            services.Configure<FlightsCacheServiceConfiguration>(configuration.GetSection("ExternalServices:FlightsCacheService"));
            services.AddSingleton(s => s.GetService<IOptions<FlightsCacheServiceConfiguration>>()!.Value);
            services.Configure<SscRobotSettings>(configuration.GetSection("SscRobotSettings"));
            services.AddSingleton(s => s.GetService<IOptions<SscRobotSettings>>()!.Value);
            
            return services;
        }

        public static void ConfigureApp(this IApplicationBuilder app, IWebHostEnvironment env, IConfiguration configuration)
        {
            app.UseOpenTelemetryDefaults();
            app.UseBackgroundProcessing(env, configuration);
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "FlightsCacheRobotsProducers");
                c.DisplayRequestDuration();
            });
            app.UseCustomHealthChecks();
        }

        private static void AddSwagger(this IServiceCollection services)
        {
            services.AddSwaggerExamplesFromAssemblyOf<DirectRyanairDailyJobExample>();
            services.AddSwaggerGen(c =>
            {
                c.ExampleFilters();
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "FlightsCacheRobotsProducers", Version = "v1" });
                c.IncludeXmlComments(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Esky.FlightsCache.RobotsProducers.xml"));
                c.MapType<TimeSpan>(() => new OpenApiSchema { Type = "string", Example = new OpenApiString("24:00:00"), Description = "HH:mm:ss" });
                c.MapType<RelativeDate>(() => new OpenApiSchema { Type = "string", Example = new OpenApiInteger(4) });
            });
        }

        private static void UseBackgroundProcessing(this IApplicationBuilder app, IWebHostEnvironment env, IConfiguration configuration)
        {
            app.UseHangfireDashboard("/hangfire", new DashboardOptions
            {
                Authorization = [new AllowAllConnectionsFilter()],
                IgnoreAntiforgeryToken = true
            });
        }

        private static void AddBackgroundProcessing(this IServiceCollection services, IConfiguration configuration)
        {
            var settings = configuration.GetSection("HangfireSettings").Get<HangfireSettings>()!;
            services.AddSingleton(settings);
            services.AddHostedService<HangfireMetricReader>();
            services.AddHangfire(conf =>
            {
                if (string.IsNullOrEmpty(settings.ConnectionString))
                {
                    conf.UseMemoryStorage();
                }
                else
                {
                    conf.UseMongoStorage(settings.ConnectionString, new MongoStorageOptions
                    {
                        QueuePollInterval = TimeSpan.FromSeconds(30),
                        Prefix = settings.Prefix,
                        MigrationOptions = new MongoMigrationOptions { MigrationStrategy = new MigrateMongoMigrationStrategy() },
                        CheckQueuedJobsStrategy = CheckQueuedJobsStrategy.TailNotificationsCollection,
                        InvisibilityTimeout = TimeSpan.FromMinutes(60)
                    });
                }

                conf.UseRecommendedSerializerSettings(e => e.Converters.Add(new RelativeDateJsonSerializer()));
                conf.UseFilter(new AutomaticRetryAttribute { Attempts = 3, DelaysInSeconds = [60, 60, 60] });
                conf.UseConsole(new ConsoleOptions
                {
                    ExpireIn = TimeSpan.FromDays(14),
                    FollowJobRetentionPolicy = true,
                });
            });
            
            if (settings.WorkerCount > 0)
            {
                services.AddHangfireServer(opt =>
                {
                    opt.WorkerCount = settings.WorkerCount;
                    opt.MaxDegreeOfParallelismForSchedulers = settings.MaxDegreeOfParallelismForSchedulers;
                });
            }
        }

        private static void AddAirportCurrencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<AirportCurrencySettings>(configuration.GetSection("AirportCurrencySettings"));
            services.AddSingleton<IAirportCurrencyRepository, AirportCurrencyRepository>();
            services.AddSingleton<IAirportCurrencyService, AirportCurrencyService>();
        }

        private static void ConfigureMessageProcessor(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<FlightOffersConfiguration>(configuration.GetSection("FlightOffersConfiguration"));
            services.AddSingleton(sp => sp.GetRequiredService<IOptions<FlightOffersConfiguration>>().Value);
            services.AddSingleton(sp => new DatabaseContext(sp.GetRequiredService<FlightOffersConfiguration>().ConnectionString));

            services.AddSingleton<IFlightOffersRepository, FlightOffersRepository>();
            services.AddSingleton<IAirportsRepository, AirportsRepository>();

            services.AddSingleton<IProviderConfigurationValidator, ProviderConfigurationValidator>();

            services.AddSingleton(_ => configuration.GetSection("FlightsCacheConfiguration").Get<ProviderConfigurationDatabase.Settings>()!);
            services.AddSingleton<IProviderConfigurationDatabase, ProviderConfigurationDatabase>();
        }
    }

    public class AllowAllConnectionsFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            return true;
        }
    }
}