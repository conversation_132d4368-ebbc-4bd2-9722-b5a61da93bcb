using Esky.FlightsCache.RobotsProducers.Coverage;
using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Examples;

public class CoverageRequestExamples : IMultipleExamplesProvider<IEnumerable<CoverageRobotSettings>>
{
    public IEnumerable<SwaggerExample<IEnumerable<CoverageRobotSettings>>> GetExamples()
    {
        yield return SwaggerExample.Create<IEnumerable<CoverageRobotSettings>>("sample",
            [new CoverageRobotSettings { Airlines = "U2", ProviderSuppliers = [new ProviderSupplierConfig(226, "easyjet")] }]);
    }
}