using System;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels;
using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;
using Esky.Framework.PartnerSettings.Enums;

namespace Esky.FlightsCache.RobotsProducers.Examples;

public class HotelRobotConfigurationRequestExamples : IMultipleExamplesProvider<HotelRobotConfiguration>
{
    public IEnumerable<SwaggerExample<HotelRobotConfiguration>> GetExamples()
    {
        yield return SwaggerExample.Create("sample", new HotelRobotConfiguration
        {
            ProviderCode = ProviderCodeEnum.HotelBeds,
            HotelMetaCodes = [100004, 107402],
            CheckInDateFrom = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(7)),
            CheckInDateTo = DateOnly.FromDateTime(DateTime.UtcNow.Date.AddDays(360)),
            StayLengthFrom = 1,
            StayLengthTo = 21,
            PartnerCode = "ESKY",
            ProviderConfigurationId = "epa",
            Occupancy =
            [
                new RoomConfiguration {Adults = 2},
                new RoomConfiguration {Adults = 2, ChildAges = [12]}
            ]
        });
    }
}