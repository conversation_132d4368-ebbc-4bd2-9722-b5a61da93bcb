using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;
using Hangfire;

using LowAvailabilitySettings = Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController.LowAvailabilitySettings;

namespace Esky.FlightsCache.RobotsProducers.Examples;

public class CronExamples : IMultipleExamplesProvider<LowAvailabilitySettings>
{
    public IEnumerable<SwaggerExample<LowAvailabilitySettings>> GetExamples()
    {
        yield return SwaggerExample.Create("disabled", new LowAvailabilitySettings(Cron.Never(), AvailableSeatsCount: 2, DaysForward: 45));
        yield return SwaggerExample.Create("every hour", new LowAvailabilitySettings(Cron.Hourly(), AvailableSeatsCount: 2, DaysForward: 45));
        yield return SwaggerExample.Create("every day at 7", new LowAvailabilitySettings(Cron.Daily(7), AvailableSeatsCount: 2, DaysForward: 45));
    }
}

