using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers
{
    public interface IQueuePublisher
    {
        Task PublishSingle<T>(T queueElement, CancellationToken cancellationToken) where T : class;
        Task Publish<T>(IEnumerable<T> queueElements, CancellationToken cancellationToken) where T : class;
        Task Publish<T>(IEnumerable<QueueElement> queueElements, Func<QueueElement, T> creator, CancellationToken cancellationToken) where T : QueueElement;
    }
}