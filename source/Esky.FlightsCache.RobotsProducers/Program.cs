using Esky.FlightsCache.RobotsProducers;
using Esky.FlightsCache.RobotsProducers.IoC;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NLog;
using NLog.Extensions.Logging;
using System;
using System.ComponentModel.DataAnnotations;

MongoConfiguration.RegisterSerializersAndConventions();

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;
var services = builder.Services;

services
    .RegisterAllServices(configuration)
    .AddControllers()
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
        options.SerializerSettings.DefaultValueHandling = DefaultValueHandling.Ignore;
        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
        options.SerializerSettings.Converters.Add(new RelativeDateJsonSerializer());
    });
services.AddMvc();

builder.Logging
    .ClearProviders()
    .AddNLog();

builder.Host
    .UseDefaultServiceProvider((ctx, opt) =>
    {
        if (!ctx.HostingEnvironment.IsDevelopment())
            return;

        opt.ValidateOnBuild = true;
        opt.ValidateScopes = true;
    });

var app = builder.Build();

if (builder.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.Use(async (context, next) =>
{
    try
    {
        await next();
    }
    catch (ValidationException e)
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsJsonAsync(e.Message);
    }
    catch (Exception e)
    {
        LogManager.GetCurrentClassLogger().Error(e);
        throw;
    }
});

app
    .UseDefaultFiles()
    .UseStaticFiles()
    .UseRouting()
    .ConfigureApp(builder.Environment, configuration);

app.MapControllers();

app.Run();