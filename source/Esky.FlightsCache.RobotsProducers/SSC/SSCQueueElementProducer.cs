using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Esky.FlightsCache.RobotsProducers.SSC;

public class SSCQueueElementProducer
{
    private readonly IRouteDatesProvider _routeDatesProvider;
    private readonly IQueueElementPublisherBuilder<SSCQueueElement> _publisherBuilder;
    private readonly IQueueElementPublisherBuilder<SSCCalendarQueueElement> _calendarPublisherBuilder;
    private readonly SscRobotSettings _settings;

    public SSCQueueElementProducer(
        IRouteDatesProvider routeDatesProvider,
        IQueueElementPublisherBuilder<SSCQueueElement> publisherBuilder,
        IQueueElementPublisherBuilder<SSCCalendarQueueElement> calendarPublisherBuilder,
        SscRobotSettings settings)
    {
        _routeDatesProvider = routeDatesProvider;
        _publisherBuilder = publisherBuilder;
        _calendarPublisherBuilder = calendarPublisherBuilder;
        _settings = settings;
    }

    [DisplayName("SSC {0}")]
    public async Task Produce(JobSettings jobSettings, PerformContext? performContext, CancellationToken cancellationToken)
    {
        Action<string> log = performContext is null ? Console.WriteLine : performContext.WriteLine;
        var currentTime = TimeOnly.FromDateTime(DateTime.UtcNow);
        if (!jobSettings.IsWithinActivityPeriod(currentTime))
        {
            log($"Provider inactive");
            return;
        }

        var routeDates = await _routeDatesProvider.GetRouteDates(jobSettings, log);
        var excludedAirports = _settings.ExcludedAirportCodes.TryGetValue(jobSettings.Supplier, out var airports) ? airports : [];
        var filteredRouteDates = routeDates
                    .Where(e => !excludedAirports.Contains(e.Route.Departure) && !excludedAirports.Contains(e.Route.Arrival))
                    .ToArray();
        var elementsCount = filteredRouteDates.Count();

        if (excludedAirports.Any())
        {
            log($"{routeDates.Count() - elementsCount} of {routeDates.Count()} elements with excluded airport codes filtered out");
        }

        if (elementsCount == 0)
        {
            log("No queue elements generated");
            return;
        }

        if (jobSettings.UseCalendar)
        {
            var elements = filteredRouteDates
                .Select(
                    e => SSCCalendarQueueElement.Create(
                        e.Route.Departure,
                            e.Route.Arrival,
                            e.DepartureDay,
                            e.ReturnDepartureDay,
                            jobSettings.Supplier
                    )
                );

            await PublishElements(_calendarPublisherBuilder, elements, elementsCount, jobSettings, "SSCCalendar", log, cancellationToken);
        }
        else
        {
            var elements = filteredRouteDates
                .Select(
                    e => SSCQueueElement.Create(
                        e.Route.Departure,
                        e.Route.Arrival,
                        e.DepartureDay,
                        e.ReturnDepartureDay,
                        jobSettings.Supplier,
                        jobSettings.Flex
                    )
                );

            await PublishElements(_publisherBuilder, elements, elementsCount, jobSettings, "SSC", log, cancellationToken);
        }
    }

    private async Task PublishElements<T>(IQueueElementPublisherBuilder<T> builder, IEnumerable<T> elements, int elementsCount, JobSettings jobSettings, string namePrefix, Action<string> log, CancellationToken cancellationToken)
    {
        var publisher = builder
            .WithEvenlyDistribution(elementsCount)
            .WithMaxProcessingWindow(jobSettings.MaxProcessingWindow)
            .WithActivityPeriod(jobSettings.ActiveFrom, jobSettings.ActiveTo)
            .WithCron(jobSettings.Cron)
            .WithLogger(log)
            .WithJobName($"{namePrefix}[{jobSettings.Supplier}]")
            .Build();

        await publisher.Publish(elements, cancellationToken);
        await publisher.Complete(cancellationToken);
    }
}