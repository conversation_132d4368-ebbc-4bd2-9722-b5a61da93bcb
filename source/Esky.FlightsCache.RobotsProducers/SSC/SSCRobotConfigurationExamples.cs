using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Hangfire;
using Swashbuckle.AspNetCore.Filters;
using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.SSC;

public class GroupConfigurationExamples : IMultipleExamplesProvider<GroupConfiguration>
{
    public IEnumerable<SwaggerExample<GroupConfiguration>> GetExamples()
    {
        var reference = new Miscellaneous.Configuration
        {
            Cron = Cron.Never(),
            RelativeDateRange = 1..14
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "with departure airports and specific routes",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations = [],
                DepartureAirports = ["WAW"],
                ArrivalAirports = ["BCN"],
                Routes = [new Route("GDN", "LTN"), new Route("KTW", "LTN")],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "absolute jobs",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations = [reference with { RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date.AddDays(7), DateTime.UtcNow.Date.AddDays(60)) }],
                DepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "rolling relative dates",
            Description = "runs indefinitely",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations =
                [
                    reference with
                    {
                        RelativeDateRange = 1..13
                    }
                ],
                DepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "every day one day less up to absolute end date",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations = [reference with { RelativeDateRange = new RelativeDateRange(1, DateTime.UtcNow.Date.AddDays(7)) }],
                DepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "rolling dates with relative end date",
            Description = $"runs indefinitely, starts with cron, but searches for flights since {DateTime.UtcNow.AddDays(7):yyyy-MM-dd} 1 week forward",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations = [reference with { RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date.AddDays(7), 7) }],
                DepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "two jobs with flex",
            Value = new GroupConfiguration
            {
                Name = "PL",
                Configurations =
                [
                    reference with { RelativeDateRange = 1..4 },
                    reference with { RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date.AddDays(7), DateTime.UtcNow.Date.AddDays(60)) }
                ],
                DepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"],
                Flex = 2
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "one way with flex",
            Value = new GroupConfiguration
            {
                Name = "easyjet_OW",
                Configurations = [reference with { RelativeDateRange = 1..4 }],
                ArrivalAirports = ["LTN", "STN"],
                Supplier = "easyjet",
                Airlines = ["U2"],
                Flex = 2,
                IsOneWay = true
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "skip timetables",
            Value = new GroupConfiguration
            {
                Name = "wizzair_without_timetables",
                Configurations = [reference with { RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date.AddDays(7), DateTime.UtcNow.Date.AddDays(60)) }],
                Routes = [new Route("GDN", "LTN"), new Route("KTW", "LTN")],
                Supplier = "wizzair",
                IgnoreTimetables = true
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "RT based on Timetables with route filter and stay lengths",
            Value = new GroupConfiguration
            {
                Name = "RT with SL",
                Configurations = [reference with { RelativeDateRange = 10..20 }],
                Routes = [new Route("GDN", "LTN"), new Route("KTW", "LTN")],
                Supplier = "wizzair",
                Airlines = ["W6"],
                StayLengths = [4, 5, 6]
            }
        };
        yield return new SwaggerExample<GroupConfiguration>
        {
            Name = "wizzair low priority with excluded departure airports",
            Value = new GroupConfiguration
            {
                Name = "wizzair_low",
                Configurations = [reference with { RelativeDateRange = 10..20 }],
                ExcludedDepartureAirports = ["WAW", "WMI"],
                Supplier = "wizzair",
                Airlines = ["W6"]
            }
        };
    }
}