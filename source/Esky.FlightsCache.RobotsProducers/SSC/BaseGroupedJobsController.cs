using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Hangfire;
using Hangfire.Storage;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.SSC;

public record GroupValidation(OverlapCheckResult[] OverlapCheckResults, IEnumerable<string> Errors);

public abstract class BaseGroupedJobsController<TGroup, TJobSettings> : ControllerBase
    where TGroup : IGroup
    where TJobSettings : JobSettings
{
    private readonly IJobConverter<TGroup, TJobSettings> _converter;

    protected BaseGroupedJobsController(IJobConverter<TGroup, TJobSettings> converter)
    {
        _converter = converter;
    }
    /// <summary>
    /// get all grouped configurations
    /// </summary>
    [HttpGet("Groups")]
    [ProducesResponseType<GroupConfiguration[]>(200)]
    public IActionResult GetAllGroups([FromServices] JobStorage storage)
    {
        var recurringJobs = storage.GetConnection().GetRecurringJobs();
        var configurations = recurringJobs
            .Where(e => e.Job?.Args.Any(a => a?.GetType() == typeof(TJobSettings)) ?? false)
            .Select(e => (e.Job.Args.First(a => a?.GetType() == typeof(TJobSettings)) as TJobSettings)!);
        var groups = _converter.FromJobs(configurations);

        return Ok(groups);
    }
    
    public virtual IActionResult UpsertGroupConfiguration(
        [FromBody] TGroup groupConfiguration,
        [FromServices] IRecurringJobManager recurringJobManager)
    {
        var hasContentErrors = !groupConfiguration.IsValid(out var contentErrors);
        var overlapCheckResults = groupConfiguration.CheckForOverlaps(DateTime.UtcNow).ToArray();
        if (hasContentErrors)
        {
            return BadRequest(new GroupValidation(overlapCheckResults, contentErrors));
        }

        foreach (var jobName in groupConfiguration.GetJobNames())
        {
            recurringJobManager.RemoveIfExists(jobName);
        }

        var jobs = _converter.ToJobs(DateTime.UtcNow, groupConfiguration).ToArray();
        foreach (var (jobName, settings) in jobs)
        {
            AddJob(recurringJobManager, jobName, settings);
        }

        return Ok(
            new
            {
                Group = groupConfiguration,
                Jobs = jobs.Select(j => new { j.JobName, j.JobSettings }),
                OverlapValidation = overlapCheckResults
            }
        );
    }

    private protected abstract void AddJob(IRecurringJobManager recurringJobManager, string jobName,
        TJobSettings settings);

    private protected abstract IEnumerable<string> GetJobNames(string name);

    [HttpDelete("Groups/{name}")]
    [ProducesResponseType(204)]
    public IActionResult RemoveGroup(string name, [FromServices] IRecurringJobManager recurringJobManager)
    {
        foreach (var jobName in GetJobNames(name))
        {
            recurringJobManager.RemoveIfExists(jobName);
        }

        return NoContent();
    }
}