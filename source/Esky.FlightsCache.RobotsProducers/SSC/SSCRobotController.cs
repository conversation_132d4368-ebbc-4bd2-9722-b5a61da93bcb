using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;
using System.Threading;

namespace Esky.FlightsCache.RobotsProducers.SSC;

[ApiController]
[Route("api/[controller]/Configuration")]
public class SSCRobotController : BaseGroupedJobsController<GroupConfiguration, JobSettings>
{
    public SSCRobotController(IJobConverter<GroupConfiguration, JobSettings> converter) : base(converter)
    { }
    
    /// <summary>
    /// upsert configuration by Name as id
    /// </summary>
    [HttpPost("Groups")]
    [ProducesResponseType(200)]
    [ProducesResponseType<GroupValidation>(400)]
    [SwaggerRequestExample(typeof(GroupConfiguration), typeof(GroupConfigurationExamples))]
    public override IActionResult UpsertGroupConfiguration(
        [FromBody] GroupConfiguration groupConfiguration,
        [FromServices] IRecurringJobManager recurringJobManager)
        => base.UpsertGroupConfiguration(groupConfiguration, recurringJobManager);
    
    
    private protected override void AddJob(IRecurringJobManager recurringJobManager, string jobName, JobSettings settings)
    {
        recurringJobManager.AddOrUpdate<SSCQueueElementProducer>(
            jobName,
            p => p.Produce(settings, null, CancellationToken.None),
            settings.Cron
        );
    }

    private protected override IEnumerable<string> GetJobNames(string name) => GroupConfiguration.GetJobNames(name);
}