using Esky.FlightsCache.RobotsProducers.DirectRyanair;
using Esky.FlightsCache.RobotsProducers.Examples;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;
using System;

namespace Esky.FlightsCache.RobotsProducers.Controllers;

[ApiController]
[Route("api/Robots/RyanAirDirect")]
public class RyanAirDirectRobotsController: ControllerBase
{
    private readonly IRecurringJobManager _jobManager;

    public RyanAirDirectRobotsController(IRecurringJobManager jobManager)
    {
        _jobManager = jobManager;
    }

    /// <summary>
    /// Insert or update job for specified days with a given cron expression
    /// </summary>
    /// <param name="job"></param>
    [HttpPut("JobConfiguration")]
    [SwaggerRequestExample(typeof(Job), typeof(DirectRyanairDailyJobExample))]
    public void UpsertDailyJob([FromBody]Job job)
    {
        _jobManager.AddOrUpdate<RyanAirDirectDailyProducer>(
            RyanAirDirectDailyProducer.GetJobName(job.StartingDayOffset, job.NumberOfDaysForward),
            producer => producer.ProduceDailyForward(job.StartingDayOffset, job.NumberOfDaysForward),
            job.CronExpression,
            TimeZoneInfo.Utc
        );
    }

    /// <summary>
    /// Insert or update job for low availabity DirectRyanair producer
    /// </summary>
    /// <param name="settings"></param>
    [HttpPut("LowAvailability/JobConfiguration")]
    [SwaggerRequestExample(typeof(LowAvailabilitySettings), typeof(CronExamples))]
    public void SetupLowAvailability([FromBody] LowAvailabilitySettings settings)
    {
        _jobManager.AddOrUpdate<RyanAirDirectLowAvailabilityProducer>(
            RyanAirDirectLowAvailabilityProducer.JobName,
            producer => producer.Publish(settings.AvailableSeatsCount, settings.DaysForward, null),
            settings.CronExpression,
            TimeZoneInfo.Utc
        );
    }

    public record LowAvailabilitySettings(string CronExpression, int AvailableSeatsCount, int DaysForward);
    public record Job(int StartingDayOffset, int NumberOfDaysForward, string CronExpression);
}