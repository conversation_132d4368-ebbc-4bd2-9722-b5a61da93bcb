using Hangfire;
using Hangfire.Server;
using Microsoft.AspNetCore.Mvc;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers;

[Route("api/[controller]")]
[ApiController]
public class HotelsController
{
    [HttpPut]
    [Route("Robots/JobsConfiguration")]
    public void ConfigureRecurringJobs(
    [FromServices] IRecurringJobManager jobManager,
    [FromQuery] string cronExpression)
    {
        jobManager.AddOrUpdate<HotelJobsConfigurator>(
            nameof(HotelJobsConfigurator),
            producer => producer.ConfigureRobots(default!, default!),
            cronExpression,
            TimeZoneInfo.Utc
        );
    }

    /// <summary>
    /// Configure job for hotels API robots
    /// </summary>
    [HttpPut]
    [Route("Robots/{jobName}/JobConfiguration")]
    public void ConfigureRobot(
        [FromServices] IRecurringJobManager jobManager,
        string jobName,
        HotelRobotConfiguration config,
        [FromQuery] string cronExpression)
    {
        jobManager.AddOrUpdate<HotelsRobotsProducer>(
            jobName,
            producer => producer.Publish(jobName, config, cronExpression, null, CancellationToken.None),
            cronExpression,
            TimeZoneInfo.Utc
        );
    }

    /// <summary>
    /// Configure job for hotels API robots
    /// </summary>
    [HttpPut]
    [Route("Robots/Flex/{jobName}/JobConfiguration")]
    public void ConfigureFlexRobot(
        [FromServices] IRecurringJobManager jobManager,
        string jobName,
        HotelRobotFlexConfiguration config,
        [FromQuery] string cronExpression)
    {
        jobManager.AddOrUpdate<HotelsRobotsFlexProducer>(
            jobName,
            producer => producer.Publish(jobName, config, cronExpression, null, CancellationToken.None),
            cronExpression,
            TimeZoneInfo.Utc
        );
    }

    /// <summary>
    /// Configure job for hotels API robots
    /// </summary>
    [HttpPut]
    [Route("Robots/Flex/Instant/{jobName}/JobConfiguration")]
    public void ConfigureInstantFlexRobot(
        [FromServices] IRecurringJobManager jobManager,
        string jobName,
        HotelRobotFlexConfiguration config,
        [FromQuery] string cronExpression)
    {
        jobManager.AddOrUpdate<HotelsRobotsInstantFlexProducer>(
            jobName,
            producer => producer.Publish(jobName, config, cronExpression, null, CancellationToken.None),
            cronExpression,
            TimeZoneInfo.Utc
        );
    }

    /// <summary>
    /// Run job for hotels API robots
    /// </summary>
    [HttpPut]
    [Route("Robots/Flex/Instant/{jobName}/JobConfiguration/run")]
    public async Task RunInstantFlexRobot(
        [FromServices] IHotelsRobotsInstantFlexProducer hotelsRobotsInstantFlexProducer,
        [FromServices] IRecurringJobManager jobManager,
        string jobName,
        HotelRobotFlexConfiguration config,
        [FromQuery] string cronExpression)
    {
        await hotelsRobotsInstantFlexProducer.Publish(jobName, config, cronExpression, null, CancellationToken.None);
    }

    /// <summary>
    /// Delete job
    /// </summary>
    [HttpDelete]
    [Route("Robots/{jobName}/JobConfiguration")]
    public void DeleteRobot([FromServices] IRecurringJobManager jobManager, string jobName)
    {
        jobManager.RemoveIfExists(jobName);
    }
}
