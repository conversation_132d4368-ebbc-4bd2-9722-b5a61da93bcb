using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Hangfire;
using Microsoft.AspNetCore.Mvc;

namespace Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion;

[ApiController]
[Route("[controller]")]
public class TravelFusionSuppliersProducerController : ControllerBase
{
    private readonly IRecurringJobManager _jobManager;

    public TravelFusionSuppliersProducerController(IRecurringJobManager jobManager)
    {
        _jobManager = jobManager;
    }

    /// <summary>
    /// Upserts TravelFusionSuppliersProducer job
    /// </summary>
    [HttpPut("Cron/{cron}")]
    public void UpsertTravelFusionSuppliersProducer(string cron)
    {
        _jobManager.AddOrUpdate<TravelFusionSuppliersProducer>(
            TravelFusionSuppliersProducer.JobName,
            p => p.Execute(new TravelFusionSuppliersProducer.Parameters { Cron = cron }, default, default),
            cron
        );
    }
        
    /// <summary>
    /// Removes TravelFusionSuppliersProducer job
    /// </summary>
    [HttpDelete]
    public void DeleteTravelFusionSuppliersProducer()
    {
        _jobManager.RemoveIfExists(TravelFusionSuppliersProducer.JobName);
    }
}