using Esky.FlightsCache.RobotsProducers.Producers;
using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion
{
    [Route("api/Robots/TravelFusion")]
    [ApiController]
    public class TravelFusionRobotsController : ControllerBase
    {
        private readonly IRecurringJobManager _jobManager;

        public TravelFusionRobotsController(IRecurringJobManager jobManager)
        {
            _jobManager = jobManager;
        }

        /// <summary>
        /// Produce TravelFusion robot messages for multiple suppliers in days period
        /// </summary>
        [HttpPost]
        [Route("Producers")]
        public Task ProduceMessagesForSuppliers([FromServices] TravelFusionProducer producer, string[] suppliers, int daysToDepartureFrom, int daysToDepartureTo, string partnerCode = RobotConstants.PARTNER_CODE)
        {
            var jobUniqueName = TravelFusionProducer.GetJobName(suppliers, daysToDepartureFrom, daysToDepartureTo);
            
            var request = new GenerateForSuppliersRequest
            {
                JobName = jobUniqueName,
                Suppliers = suppliers,
                CronExpression = "invalid cron expression",
                PartnerCode = partnerCode,
                DaysToDepartureFrom = daysToDepartureFrom,
                DaysToDepartureTo = daysToDepartureTo
            };

            return producer.GenerateForSuppliersEvenlyDistributed(request, null, CancellationToken.None);
        }

        /// <summary>
        /// Configure job for messages for multiple suppliers in days period
        /// </summary>
        [HttpPost]
        [Route("Producers/JobConfiguration")]
        public void ConfigureDayByDayRobot(string[] suppliers, int daysToDepartureFrom, int daysToDepartureTo,
            [FromQuery] string cronExpression, string partnerCode = RobotConstants.PARTNER_CODE)
        {
            var jobUniqueName = TravelFusionProducer.GetJobName(suppliers, daysToDepartureFrom, daysToDepartureTo);

            var request = new GenerateForSuppliersRequest
            {
                JobName = jobUniqueName,
                Suppliers = suppliers,
                CronExpression = cronExpression,
                PartnerCode = partnerCode,
                DaysToDepartureFrom = daysToDepartureFrom,
                DaysToDepartureTo = daysToDepartureTo
            };

            _jobManager.AddOrUpdate<TravelFusionProducer>(jobUniqueName,
                producer => producer.GenerateForSuppliersEvenlyDistributed(request, null, CancellationToken.None),
                () => cronExpression,
                TimeZoneInfo.Utc);
        }

        /// <summary>
        /// Delete job for messages for multiple suppliers in days period
        /// </summary>
        [HttpDelete]
        [Route("Producers/JobConfiguration")]
        public void DeleteSupplierRobot(string[] suppliers, int daysToDepartureFrom, int daysToDepartureTo)
        {
            var jobUniqueName = TravelFusionProducer.GetJobName(suppliers, daysToDepartureFrom, daysToDepartureTo);

            _jobManager.RemoveIfExists(jobUniqueName);
        }
    }
}