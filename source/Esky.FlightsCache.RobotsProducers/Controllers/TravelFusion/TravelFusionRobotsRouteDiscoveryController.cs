using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion
{
    [Route("api/Robots/TravelFusion/Producers/routeDiscovery")]
    public class TravelFusionRobotsRouteDiscoveryController : ControllerBase
    {
        /// <summary>
        /// Produce TravelFusion robot messages for all suppliers routes discovery
        /// </summary>
        [HttpPost]
        [Route("Producers")]
        public Task ProduceMessagesForSuppliers([FromServices] DiscoverRoutesProducer producer)
        {
            return producer.GenerateForAllSuppliers(new DiscoverRoutesProducer.Parameters { Cron = null }, default, default);
        }

        /// <summary>
        /// Configure job for messages for all suppliers routes discovery
        /// </summary>
        [HttpPost]
        [Route("Producers/JobConfiguration")]
        public void ConfigureDayByDayRobot([FromServices] IRecurringJobManager jobManager, [FromQuery] string cronExpression)
        {
            var jobUniqueName = DiscoverRoutesProducer.GetJobName();

            jobManager.AddOrUpdate<DiscoverRoutesProducer>(jobUniqueName,
                producer => producer.GenerateForAllSuppliers(
                    new DiscoverRoutesProducer.Parameters { Cron = cronExpression }, default, default),
                () => cronExpression,
                TimeZoneInfo.Utc);
        }

        /// <summary>
        /// Delete job for messages for all suppliers routes discovery
        /// </summary>
        [HttpDelete]
        [Route("Producers/JobConfiguration")]
        public void DeleteSupplierRobot([FromServices] IRecurringJobManager jobManager)
        {
            var jobUniqueName = DiscoverRoutesProducer.GetJobName();

            jobManager.RemoveIfExists(jobUniqueName);
        }
    }
}