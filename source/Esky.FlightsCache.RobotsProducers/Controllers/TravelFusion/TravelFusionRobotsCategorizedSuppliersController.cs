using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion
{
    [Route("api/Robots/TravelFusion/Producers/categorized")]
    [ApiController]
    public class TravelFusionRobotsCategorizedSuppliersController : ControllerBase
    {
        /// <summary>
        /// Update or Insert TravelFusion supplier details
        /// </summary>
        [HttpPut]
        [Route("suppliers/{supplier}")]
        public Task PutSupplierDetails(
            [FromServices] ITravelFusionSupplierRepository repository,
            string supplier,
            [FromBody] TravelFusionSupplierRequest supplierRequest)
        {
            return repository.UpsertSupplier(supplier, supplierRequest);
        }

        /// <summary>
        /// Get TravelFusion suppliers with details
        /// </summary>
        [HttpGet]
        [Route("suppliers/")]
        public Task<List<TravelFusionSupplier>> GetTravelFusionSuppliers(
            [FromServices] ITravelFusionSupplierRepository repository,
            TravelFusionSupplierCategory? category,
            CancellationToken cancellationToken)
        {
            return repository.GetSuppliers(category, cancellationToken);
        }

        /// <summary>
        /// Remove TravelFusion supplier details
        /// </summary>
        [HttpDelete]
        [Route("suppliers/{supplier}")]
        public async Task<IActionResult> RemoveSuppliers([FromServices] ITravelFusionSupplierRepository repository, string supplier)
        {
            await repository.RemoveSuppliers(supplier);
            return Ok();
        }

        /// <summary>
        /// Produce TravelFusion robot messages for a category
        /// </summary>
        [HttpPost]
        [Route("{category}")]
        public Task ProduceMessagesForSupplierCategory(
            [FromServices] TravelFusionCategorizedProducer producer,
            TravelFusionSupplierCategory category)
        {
            var jobUniqueName = TravelFusionCategorizedProducer.GetJobName(category);
            return producer.GenerateForCategoryEvenlyDistributed(category, "invalid cron expression - execute all at once", jobUniqueName, null, CancellationToken.None);
        }

        /// <summary>
        /// Configure job for messages for a category of suppliers
        /// </summary>
        [HttpPut]
        [Route("{category}/JobConfiguration")]
        public void ConfigureDayByDayRobot(
            [FromServices] IRecurringJobManager jobManager,
            TravelFusionSupplierCategory category,
            [FromQuery] string cronExpression)
        {
            var jobUniqueName = TravelFusionCategorizedProducer.GetJobName(category);

            jobManager.AddOrUpdate<TravelFusionCategorizedProducer>(
                jobUniqueName,
                producer => producer.GenerateForCategoryEvenlyDistributed(category, cronExpression, jobUniqueName, null, CancellationToken.None),
                cronExpression,
                TimeZoneInfo.Utc
            );
        }

        /// <summary>
        /// Delete job for messages for single category of suppliers
        /// </summary>
        [HttpDelete]
        [Route("{category}/JobConfiguration")]
        public void DeleteRobot([FromServices] IRecurringJobManager jobManager, TravelFusionSupplierCategory category)
        {
            var jobUniqueName = TravelFusionCategorizedProducer.GetJobName(category);

            jobManager.RemoveIfExists(jobUniqueName);
        }
    }
}