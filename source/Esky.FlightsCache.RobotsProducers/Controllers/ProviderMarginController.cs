using Esky.FlightsCache.ProviderMapping;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProviderMarginController : ControllerBase
    {
        private readonly IProviderConfigurationDatabase _repository;

        public ProviderMarginController(IProviderConfigurationDatabase repository)
        {
            _repository = repository;
        }

        // GET: api/<ProviderMargin>
        [HttpGet]
        public async Task<IEnumerable<ProviderMarginConfiguration>> Get()
        {
            return await _repository.GetMarginList();
        }

        // GET api/<ProviderMargin>/5
        [HttpGet("{providerCode}")]
        public async Task<ProviderMarginConfiguration?> Get(int providerCode)
        {
            return (await _repository.GetMarginList()).FirstOrDefault(x => x.ProviderCode == providerCode);
        }

        // PUT api/<ProviderMargin>/5
        [HttpPut("{providerCode}")]
        public async Task Put(int providerCode,
            [FromBody] ProviderMarginConfiguration config,
            [FromServices] IProviderConfigurationValidator validator)
        {
            if (config.ProviderCode != providerCode)
            {
                throw new ValidationException("Unable to change CacheMarginConfig - ProviderCode can't be changed");
            }
            
            validator.Validate(config);

            await _repository.AddOrUpdate(providerCode, config);
        }

        // DELETE api/<ProviderMargin>/5
        [HttpDelete("{providerCode}")]
        public async Task Delete(int providerCode)
        {
            await _repository.DeleteMargin(providerCode);
        }
    }
}
