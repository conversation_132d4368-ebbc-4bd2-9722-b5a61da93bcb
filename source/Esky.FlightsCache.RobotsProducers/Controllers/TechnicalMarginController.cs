using Esky.FlightsCache.ProviderMapping;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TechnicalMarginController(IProviderConfigurationDatabase repository) : ControllerBase
    {
        // GET: api/<TechnicalMargin>
        [HttpGet]
        public async Task<IEnumerable<TechnicalMarginConfiguration>> Get()
        {
            return await repository.GetTechnicalMarginList();
        }

        // GET api/<TechnicalMargin>/5
        [HttpGet("{id}")]
        public async Task<TechnicalMarginConfiguration?> Get(string id)
        {
            return await repository.GetTechnicalMargin(id);
        }

        // PUT api/<TechnicalMargin>/5
        [HttpPut("{id}")]
        public async Task Put(string id,
            [FromBody] TechnicalMarginConfiguration config,
            [FromServices] IProviderConfigurationValidator validator)
        {
            await repository.AddOrUpdate(id, config);
        }

        // POST api/<TechnicalMargin>/5
        [HttpPost]
        public async Task Post([FromBody] TechnicalMarginConfiguration config,
            [FromServices] IProviderConfigurationValidator validator)
        {
            await repository.AddOrUpdate(null, config);
        }

        // DELETE api/<TechnicalMargin>/5
        [HttpDelete("{id}")]
        public async Task Delete(string id)
        {
            await repository.DeleteTechnicalMargin(id);
        }
    }
}
