using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.ProviderMapping;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProviderMappingController : ControllerBase
    {
        private readonly IProviderConfigurationDatabase _repository;

        public ProviderMappingController(IProviderConfigurationDatabase repository)
        {
            _repository = repository;
        }

        // GET: api/<ProviderMapping>
        [HttpGet]
        public async Task<IEnumerable<CacheProviderConfiguration>> Get()
        {
            return await _repository.GetMappingList();
        }

        // GET api/<ProviderMapping>/5
        [HttpGet("{cacheProviderCode}")]
        public async Task<CacheProviderConfiguration?> Get(int cacheProviderCode)
        {
            return (await _repository.GetMappingList()).FirstOrDefault(x => x.CacheProviderCode == cacheProviderCode);
        }

        // PUT api/<ProviderMapping>/5
        [HttpPut("{cacheProviderCode}")]
        public async Task Put(int cacheProviderCode,
            [FromBody] CacheProviderConfiguration config,
            [FromServices] IProviderConfigurationValidator validator)
        {
            if (config.CacheProviderCode != cacheProviderCode)
            {
                throw new ValidationException("Unable to change CacheProviderConfig code - CacheProviderCode can't be changed");
            }

            validator.Validate(config);

            await _repository.AddOrUpdate(cacheProviderCode, config);
        }

        // DELETE api/<ProviderMapping>/5
        [HttpDelete("{cacheProviderCode}")]
        public async Task Delete(int cacheProviderCode)
        {
            await _repository.DeleteMapping(cacheProviderCode);
        }

        /// <summary>
        /// Clear cache flights in background. Returns hangfire job id.
        /// </summary>
        /// <param name="backgroundJobClient"></param>
        /// <param name="cacheProviderCode"></param>
        /// <param name="removeFromCalendarOw"></param>
        /// <param name="removeFromCalendarRt"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpDelete("{cacheProviderCode}/flights")]
        public string DeleteFlights([FromServices] IBackgroundJobClient backgroundJobClient, int cacheProviderCode, bool removeFromCalendarOw, bool removeFromCalendarRt)
        {
            if (removeFromCalendarOw || removeFromCalendarRt)
            {
                return backgroundJobClient.Enqueue<IFlightOffersRepository>(repo => repo.RemoveByCacheProviderCode(cacheProviderCode, removeFromCalendarOw, removeFromCalendarRt));
            }
            else
            {
                throw new ArgumentException("At least one argument should true: removeFromCalendarOw or removeFromCalendarRt");
            }
        }
    }
}
