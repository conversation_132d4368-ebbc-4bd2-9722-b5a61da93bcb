using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Controllers
{
    [Route("api/Robots/CacheDemand")]
    [ApiController]
    public class CacheDemandController : ControllerBase
    {
        private readonly CacheDemandService _service;

        public CacheDemandController(CacheDemandService service)
        {
            _service = service;
        }

        /// <summary>
        /// Returns list of CacheDemand jobs
        /// </summary>
        [HttpGet]
        [Route("Jobs")]
        public async Task<IList<CacheDemandJob>> GetJobList([FromServices] ICacheDemandJobRepository jobRepository, int limit = 100)
        {
            return await jobRepository.GetList(limit);
        }

        /// <summary>
        /// Returns list of CacheDemand sheet jobs
        /// </summary>
        [HttpGet]
        [Route("SheetJobs")]
        public async Task<IEnumerable<CacheDemandSheetJob>> GetSheetJobs()
        {
            return await _service.GetSheetJobs();
        }

        /// <summary>
        /// Validates sheet job
        /// </summary>
        [HttpPost]
        [Route("SheetJobs/{spreadsheetId}/{sheetName}/validate")]
        public async Task<CacheDemandValidationResult> ValidateSheetJob([FromRoute] string spreadsheetId, [FromRoute] string sheetName)
        {
            return await _service.ValidateSheetJob(spreadsheetId, sheetName);
        }

        /// <summary>
        /// Run/refresh sheet job
        /// </summary>
        [HttpPost]
        [Route("SheetJobs/{spreadsheetId}/{sheetName}/run")]
        public async Task<CacheDemandValidationResult> RunSheetJob([FromRoute] string spreadsheetId, [FromRoute] string sheetName, [FromQuery] string? jobId, [FromQuery] string? removalJobId)
        {
            return await _service.RunSheetJob(spreadsheetId, sheetName, jobId, removalJobId);
        }

        /// <summary>
        /// Remove sheet job
        /// </summary>
        [HttpDelete("SheetJobs/{id}")]
        public void RemoveJob([FromRoute] string id)
        {
            _service.RemoveCacheDemandJob(id);
        }
        
        /// <summary>
        /// Calculates collected flights for each row in the sheet
        /// </summary>
        [HttpPost]
        [Route("SheetJobs/{spreadsheetId}/{sheetName}/calculate")]
        public async Task<CacheDemandValidationResult> CalculateSheetJob([FromRoute] string spreadsheetId, [FromRoute] string sheetName)
        {
            return await _service.CalculateJobCoverage(spreadsheetId, sheetName);
        }
    }
}
