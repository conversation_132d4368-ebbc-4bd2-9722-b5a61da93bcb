using Hangfire.Storage.Monitoring;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers;

public class Metrics
{
    private static readonly Meter _meter = new("FlightsCache.RobotsProducers");
    
    private static StatisticsDto? _hangfireJobStatistics;

    private static readonly ObservableGauge<long> _hangfire = _meter.CreateObservableGauge(
        "hangfire_jobs",
        () => new Measurement<long>[]
        {
            new(_hangfireJobStatistics?.Scheduled ?? 0, new KeyValuePair<string, object?>("type", "scheduled")),
            new(_hangfireJobStatistics?.Enqueued ?? 0, new KeyValuePair<string, object?>("type", "enqueued")),
            new(_hangfireJobStatistics?.Processing ?? 0, new KeyValuePair<string, object?>("type", "processing")),
            new(_hangfireJobStatistics?.Succeeded ?? 0, new KeyValuePair<string, object?>("type", "succeeded")),
            new(_hangfireJobStatistics?.Failed ?? 0, new KeyValuePair<string, object?>("type", "failed")),
            new(_hangfireJobStatistics?.Deleted ?? 0, new KeyValuePair<string, object?>("type", "deleted")),
            new(_hangfireJobStatistics?.Recurring ?? 0, new KeyValuePair<string, object?>("type", "recurring")),
        },
        description: "refreshed every 1 minute"
    );
    
    public static void SetHangfireStats(StatisticsDto stats)
    {
        _hangfireJobStatistics = stats;
    }

    private static readonly ConcurrentDictionary<string, (string Type, int Count)> _jobElementsCountValues = [];
    private static readonly ObservableGauge<int> _jobElementsCount = _meter.CreateObservableGauge(
        "job_elements_count",
        () =>
            _jobElementsCountValues.Select(e => new Measurement<int>(
                    e.Value.Count,
                    new KeyValuePair<string, object?>("name", e.Key),
                    new KeyValuePair<string, object?>("type", e.Value.Type)
                )
            ),
        description: "Number of elements processed by job"
    );

    public static void SetHotelJobElementsCount(string jobName, int elementsCount)
    {
        _jobElementsCountValues[jobName] = ("hotels", elementsCount);
    }
}