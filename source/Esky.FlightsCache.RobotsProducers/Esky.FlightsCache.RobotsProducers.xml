<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Esky.FlightsCache.RobotsProducers</name>
    </assembly>
    <members>
        <member name="M:Esky.FlightsCache.RobotsProducers.Composite.CompositeRobotController.UpsertGroupConfiguration(Esky.FlightsCache.RobotsProducers.Miscellaneous.CompositeGroupConfiguration,Hangfire.IRecurringJobManager)">
            <summary>
            upsert configuration by Name as id
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.GetJobList(Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.ICacheDemandJobRepository,System.Int32)">
            <summary>
            Returns list of CacheDemand jobs
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.GetSheetJobs">
            <summary>
            Returns list of CacheDemand sheet jobs
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.ValidateSheetJob(System.String,System.String)">
            <summary>
            Validates sheet job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.RunSheetJob(System.String,System.String,System.String,System.String)">
            <summary>
            Run/refresh sheet job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.RemoveJob(System.String)">
            <summary>
            Remove sheet job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.CacheDemandController.CalculateSheetJob(System.String,System.String)">
            <summary>
            Calculates collected flights for each row in the sheet
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.HotelsController.ConfigureRobot(Hangfire.IRecurringJobManager,System.String,Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelRobotConfiguration,System.String)">
            <summary>
            Configure job for hotels API robots
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.HotelsController.ConfigureFlexRobot(Hangfire.IRecurringJobManager,System.String,Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex.HotelRobotFlexConfiguration,System.String)">
            <summary>
            Configure job for hotels API robots
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.HotelsController.ConfigureInstantFlexRobot(Hangfire.IRecurringJobManager,System.String,Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex.HotelRobotFlexConfiguration,System.String)">
            <summary>
            Configure job for hotels API robots
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.HotelsController.RunInstantFlexRobot(Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex.IHotelsRobotsInstantFlexProducer,Hangfire.IRecurringJobManager,System.String,Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex.HotelRobotFlexConfiguration,System.String)">
            <summary>
            Run job for hotels API robots
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.HotelsController.DeleteRobot(Hangfire.IRecurringJobManager,System.String)">
            <summary>
            Delete job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.ProviderMappingController.DeleteFlights(Hangfire.IBackgroundJobClient,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Clear cache flights in background. Returns hangfire job id.
            </summary>
            <param name="backgroundJobClient"></param>
            <param name="cacheProviderCode"></param>
            <param name="removeFromCalendarOw"></param>
            <param name="removeFromCalendarRt"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.ProviderMappingController.DeleteFlights(Hangfire.IBackgroundJobClient,Esky.FlightsCache.ProviderMapping.IProviderConfigurationDatabase,System.Int32,System.String,System.DateTime,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Clear cache flights in background for specific provider code and supplier with modification date earlier than specified.
            Provider code is resolved to cache codes.
            </summary>
            <returns>hangfire job id</returns>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController.UpsertDailyJob(Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController.Job)">
            <summary>
            Insert or update job for specified days with a given cron expression
            </summary>
            <param name="job"></param>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController.SetupLowAvailability(Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController.LowAvailabilitySettings)">
            <summary>
            Insert or update job for low availabity DirectRyanair producer
            </summary>
            <param name="settings"></param>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.PutSupplierDetails(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.ITravelFusionSupplierRepository,System.String,Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionSupplierRequest)">
            <summary>
            Update or Insert TravelFusion supplier details
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.GetTravelFusionSuppliers(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.ITravelFusionSupplierRepository,System.Nullable{Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionSupplierCategory},System.Threading.CancellationToken)">
            <summary>
            Get TravelFusion suppliers with details
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.RemoveSuppliers(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.ITravelFusionSupplierRepository,System.String)">
            <summary>
            Remove TravelFusion supplier details
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.ProduceMessagesForSupplierCategory(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionCategorizedProducer,Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionSupplierCategory)">
            <summary>
            Produce TravelFusion robot messages for a category
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.ConfigureDayByDayRobot(Hangfire.IRecurringJobManager,Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionSupplierCategory,System.String)">
            <summary>
            Configure job for messages for a category of suppliers
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsCategorizedSuppliersController.DeleteRobot(Hangfire.IRecurringJobManager,Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionSupplierCategory)">
            <summary>
            Delete job for messages for single category of suppliers
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsController.ProduceMessagesForSuppliers(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.TravelFusionProducer,System.String[],System.Int32,System.Int32,System.String)">
            <summary>
            Produce TravelFusion robot messages for multiple suppliers in days period
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsController.ConfigureDayByDayRobot(System.String[],System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Configure job for messages for multiple suppliers in days period
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsController.DeleteSupplierRobot(System.String[],System.Int32,System.Int32)">
            <summary>
            Delete job for messages for multiple suppliers in days period
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsRouteDiscoveryController.ProduceMessagesForSuppliers(Esky.FlightsCache.RobotsProducers.Producers.TravelFusion.DiscoverRoutesProducer)">
            <summary>
            Produce TravelFusion robot messages for all suppliers routes discovery
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsRouteDiscoveryController.ConfigureDayByDayRobot(Hangfire.IRecurringJobManager,System.String)">
            <summary>
            Configure job for messages for all suppliers routes discovery
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionRobotsRouteDiscoveryController.DeleteSupplierRobot(Hangfire.IRecurringJobManager)">
            <summary>
            Delete job for messages for all suppliers routes discovery
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionSuppliersProducerController.UpsertTravelFusionSuppliersProducer(System.String)">
            <summary>
            Upserts TravelFusionSuppliersProducer job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Controllers.TravelFusion.TravelFusionSuppliersProducerController.DeleteTravelFusionSuppliersProducer">
            <summary>
            Removes TravelFusionSuppliersProducer job
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Coverage.CoverageController.PutCoverageRobot(System.Collections.Generic.IEnumerable{Esky.FlightsCache.RobotsProducers.Coverage.CoverageRobotSettings},System.String)">
            <summary>
            Update robots to check cache coverage for airlines. Use GET to get current settings!
            </summary>
            <param name="cron">Cron</param>
            <param name="configs">Job settings airlines + provider/supplier pairs (use cache provider codes)</param>
        </member>
        <member name="P:Esky.FlightsCache.RobotsProducers.Hangfire.HangfireSettings.ConnectionString">
            <summary>
            Mongo connection string. Database name should be provided in connection string.
            </summary>
        </member>
        <member name="P:Esky.FlightsCache.RobotsProducers.Hangfire.HangfireSettings.Prefix">
            <summary>
            Prefix for collections
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Miscellaneous.Extensions.ApplyFlex(System.Collections.Generic.IEnumerable{Esky.FlightsCache.RobotsProducers.Miscellaneous.RouteDate},System.Int32)">
            <summary>
            applies flex to departure and return departure dates
            in such way that dates after flex must be in provided dates
            </summary>
            <param name="self">for proper handling it should be for one route</param>
            <param name="flex">flex in days, no effect when less or equal to 0 </param>
            <returns>new RouteDate enumeration with flex applied when flex > 0 otherwise self</returns>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model.RelativeDate.GetDate(System.Nullable{System.DateTime})">
            <summary>
            Returns date from relative date
            </summary>
            <param name="relativeTo">if null resolves to current utc date</param>
            <returns>calculated date</returns>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.RyanairRouteFeeRefreshing.RouteFeeRefreshingController.Upsert(System.String)">
            <summary>
            Updates cron 
            </summary>
            <param name="cron">leave empty to disable</param>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.SSC.BaseGroupedJobsController`2.GetAllGroups(Hangfire.JobStorage,System.String)">
            <summary>
            get all grouped configurations
            </summary>
        </member>
        <member name="M:Esky.FlightsCache.RobotsProducers.SSC.SSCRobotController.UpsertGroupConfiguration(Esky.FlightsCache.RobotsProducers.Miscellaneous.GroupConfiguration,Hangfire.IRecurringJobManager)">
            <summary>
            upsert configuration by Name as id
            </summary>
        </member>
        <member name="T:Refit.Implementation.Generated">
            <inheritdoc />
        </member>
        <member name="T:Refit.Implementation.Generated.EskyFlightsCacheRobotsProducersProducersHotelsHotelJobsConfiguratorHotelsStaticApiIHotelsStaticApiClient">
            <inheritdoc />
        </member>
        <member name="P:Refit.Implementation.Generated.EskyFlightsCacheRobotsProducersProducersHotelsHotelJobsConfiguratorHotelsStaticApiIHotelsStaticApiClient.Client">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.EskyFlightsCacheRobotsProducersProducersHotelsHotelJobsConfiguratorHotelsStaticApiIHotelsStaticApiClient.#ctor(System.Net.Http.HttpClient,Refit.IRequestBuilder)">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.EskyFlightsCacheRobotsProducersProducersHotelsHotelJobsConfiguratorHotelsStaticApiIHotelsStaticApiClient.GetHotelsMappingData(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.EskyFlightsCacheRobotsProducersProducersHotelsHotelJobsConfiguratorHotelsStaticApiIHotelsStaticApiClient.Esky#FlightsCache#RobotsProducers#Producers#Hotels#HotelJobsConfigurator#HotelsStaticApi#IHotelsStaticApiClient#GetHotelsMappingData(System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
    </members>
</doc>
