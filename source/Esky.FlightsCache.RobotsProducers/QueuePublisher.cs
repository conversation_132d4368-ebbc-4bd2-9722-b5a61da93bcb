using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers
{
    public class QueuePublisher : IQueuePublisher
    {
        private readonly IBus _bus;
        private readonly ILogger<QueuePublisher> _logger;

        public QueuePublisher(IBus bus, ILogger<QueuePublisher> logger)
        {
            _bus = bus;
            _logger = logger;
        }

        public async Task PublishSingle<T>(T queueElement, CancellationToken cancellationToken) where T : class
        {
            await _bus.Publish(queueElement, cancellationToken);
        }

        public async Task Publish<T>(IEnumerable<T> queueElements, CancellationToken cancellationToken) where T : class
        {
            try
            {
                await _bus.PublishBatch(queueElements, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while sending to queue");
                throw;
            }
        }

        public Task Publish<T>(IEnumerable<QueueElement> queueElements, Func<QueueElement, T> creator, CancellationToken cancellationToken) where T : QueueElement
        {
            return Publish(queueElements.Select(creator), cancellationToken);
        }
    }
}