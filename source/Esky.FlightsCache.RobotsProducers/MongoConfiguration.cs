using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using System;

namespace Esky.FlightsCache.RobotsProducers;

public static class MongoConfiguration
{
    private static readonly NullableSerializer<decimal> _nullableDecimalSerializer = new(DecimalSerializer.Decimal128Instance);
    public static void RegisterSerializersAndConventions()
    {
        BsonSerializer.TryRegisterSerializer(typeof(decimal), DecimalSerializer.Decimal128Instance);
        BsonSerializer.TryRegisterSerializer(typeof(decimal?), _nullableDecimalSerializer);
        BsonSerializer.TryRegisterSerializer(RelativeDateBsonSerializer.Instance);

        var conventionPack = new ConventionPack { new IgnoreExtraElementsConvention(true) };
        ConventionRegistry.Register("IgnoreExtraElements", conventionPack, _ => true);

        ConventionRegistry.Register(
            "Queue elements intermediate storage",
            new ConventionPack { new IgnoreIfDefaultConvention(true), new DateTimeAsDateOnlyConvention() },
            type => type == typeof(QueueElement) || type == typeof(ReturnDepartureDate)
        );
    }
}

public sealed class DateTimeAsDateOnlyConvention : ConventionBase, IMemberMapConvention
{
    private static readonly NullableSerializer<DateTime> _nullableSerializer = new(DateTimeSerializer.DateOnlyInstance);

    public void Apply(BsonMemberMap memberMap)
    {
        if (memberMap.MemberType == typeof(DateTime))
        {
            memberMap.SetSerializer(DateTimeSerializer.DateOnlyInstance);
        }
        else if (memberMap.MemberType == typeof(DateTime?))
        {
            memberMap.SetSerializer(_nullableSerializer);
        }
    }
}