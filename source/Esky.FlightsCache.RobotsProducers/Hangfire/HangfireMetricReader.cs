using Hangfire;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Hangfire;

internal class HangfireMetricReader : BackgroundService
{
    private readonly JobStorage _jobStorage;

    public HangfireMetricReader(JobStorage jobStorage)
    {
        _jobStorage = jobStorage;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromMinutes(1));
        do
        {
            var stats = _jobStorage.GetMonitoringApi().GetStatistics();
            Metrics.SetHangfireStats(stats);
        } while (await timer.WaitForNextTickAsync(stoppingToken));
    }
}