{"DatabaseSettings": {"ConnectionString": "mongodb://ets:<EMAIL>,flightscontent-mongodb2.service.gcp-staging.consul,flightscontent-mongodb-arbiter.service.gcp-staging.consul/RobotsProducers?authSource=admin&sslVerifyCertificate=false&appname=esky-flightscache-robotsproducers"}, "AirportCurrencySettings": {"ConnectionString": "mongodb://ets:<EMAIL>,flightscontent-mongodb2.service.gcp-staging.consul,flightscontent-mongodb-arbiter.service.gcp-staging.consul/RobotsProducers?authSource=admin&sslVerifyCertificate=false&appname=esky-flightscache-robotsproducers"}, "ExternalServices": {"TimeTableService": {"Url": "http://esky-timetables-api.query.consul./"}, "FlightsCacheService": {"Url": "http://esky-flightscache-api.query.consul./"}}, "PartnerSettings": {"Environment": "staging"}, "CacheDemandSettings": {"GoogleCredentialsPath": "/creds/account.json"}, "BigQuerySettings": {"ProjectId": "esky-ets-logs-ci", "GoogleCredentialsPath": "/creds/account.json"}, "RedisCaching": {"ConnectionString": "secret"}, "HangfireSettings": {"ConnectionString": "mongodb://ets:<EMAIL>,flightscontent-mongodb2.service.gcp-staging.consul,flightscontent-mongodb-arbiter.service.gcp-staging.consul/RobotsProducers?authSource=admin&sslVerifyCertificate=false&appname=esky-flightscache-robotsproducers", "WorkerCount": 1}, "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "System.Net.Http.HttpClient.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "<PERSON><PERSON>*", "minLevel": "<PERSON><PERSON>", "writeTo": "rabbit", "final": true}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}