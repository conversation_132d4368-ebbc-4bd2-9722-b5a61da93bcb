{
  "FlightsCacheConfiguration": {
    "ConnectionUrl": "mongodb://host.docker.internal:27002/FlightsCacheConfiguration?appname=esky-flightscache-robotsproducers"
  },
  "DatabaseSettings": {
    "ConnectionString": "mongodb://host.docker.internal:27002/RobotsProducers?appname=esky-flightscache-robotsproducers"
  },
  "AirportCurrencySettings": {
    "ConnectionString": "mongodb://host.docker.internal:27002/RobotsProducers?appname=esky-flightscache-robotsproducers"
  },
  "FlightOffersConfiguration": {
    "ConnectionString": "mongodb://host.docker.internal:27003/FlightOffers?appname=esky-flightscache-robotsproducers"
  },
  "ExternalServices": {
    "TimeTableService": {
      "Url": "http://host.docker.internal:1090/"
    },
    "FlightsCacheService": {
      "Url": "http://host.docker.internal:1090/"
    }
  },
  "PartnerSettings": {
    "ApiKey": "",
    "Environment": "",
    "System": "",
    "Url": "http://host.docker.internal:1090/"
  },
  "CacheDemandSettings": {
    "GoogleCredentialsPath": "googleKey.json"
  },
  "BigQuerySettings": {
    "ProjectId": "esky-ets-logs-ci",
    "GoogleCredentialsPath": "googleKey.json"
  },
  "RedisCaching": {
    "ConnectionString": "host.docker.internal:6379,password=k8rBZeXosd,asyncTimeout=120000",
    "InstanceName": "redis-1"
  },
  "HangfireSettings": {
    "ConnectionString": "mongodb://host.docker.internal:27002/RobotsProducers?appname=esky-flightscache-robotsproducers",
    "WorkerCount": 1
  },
  "RobotServiceBus": {
    "Url": "rabbitmq://host.docker.internal",
    "Login": "robotuser",
    "Password": "robotpassword"
  },
  "ServiceBus_Login": "robotuser",
  "ServiceBus_Password": "robotpassword",
  "ServiceBus_Clustermembers": "host.docker.internal",
  "ServiceBus_VHost": "/",
  "NLog": {
    "targets": {
      "console": { "type": "Console" }
    },
    "rules": [
      { "logger": "Microsoft.AspNetCore.*", "finalMinLevel": "Warn" },
      { "logger": "System.Net.Http.HttpClient.*", "finalMinLevel": "Warn" },
      { "logger": "*", "minLevel": "Info", "writeTo": "console", "final": true },
      { "logger": "*", "minLevel": "Info", "writeTo": "rabbit" } // to avoid Warn: Unused target detected
    ]
  }
}