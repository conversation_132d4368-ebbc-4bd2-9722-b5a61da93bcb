using Hangfire;
using Microsoft.AspNetCore.Mvc;

namespace Esky.FlightsCache.RobotsProducers.RyanairRouteFeeRefreshing;

[ApiController]
[Route("api/Robots/[controller]/Ryanair")]
public class RouteFeeRefreshingController : ControllerBase
{
    private readonly IRecurringJobManager _recurringJobManager;

    public RouteFeeRefreshingController(IRecurringJobManager recurringJobManager)
    {
        _recurringJobManager = recurringJobManager;
    }

    /// <summary>
    /// Updates cron 
    /// </summary>
    /// <param name="cron">leave empty to disable</param>
    [HttpPut("Cron/{cron?}")]
    public IActionResult Upsert([FromRoute] string? cron = null)
    {
        _recurringJobManager.AddOrUpdate<RyanAirRouteFeeRefreshProducer>(
            "RyanairRouteFeeRefreshing",
            e => e.Produce(default),
            cron ?? Cron.Never()
        );

        return Ok();
    }
}