using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using MassTransit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.RyanairRouteFeeRefreshing;

public class RyanAirRouteFeeRefreshProducer
{
    private readonly IBus _bus;
    private readonly ITimetableServiceClient _timetables;
    private readonly TimeProvider _timeProvider;

    public RyanAirRouteFeeRefreshProducer(IBus bus, ITimetableServiceClient timetables, TimeProvider timeProvider)
    {
        _bus = bus;
        _timetables = timetables;
        _timeProvider = timeProvider;
    }

    public async Task Produce(CancellationToken cancellationToken)
    {
        var earliestDepartureDate = _timeProvider.GetUtcNow().Date.AddMonths(1); // add one month to increase chance that there are some flights available
        await foreach (var msg in GetRefreshMessages(["FR", "RK"], earliestDepartureDate).WithCancellation(cancellationToken))
        {
            await _bus.Publish(msg, cancellationToken);
        }
    }
    
    private async IAsyncEnumerable<RyanairRefreshRouteFee> GetRefreshMessages(string[] airlines, DateTime earliestDepartureDate)
    {
        var connections = new List<ConnectionNetwork>();
        foreach (var airline in airlines)
        {
            var connectionNetwork = await _timetables.GetConnectionNetworkByAirline(airline);
            connections.AddRange(connectionNetwork);
        }

        var allRoutes = from connection in connections
            from arrival in connection.ArrivalAirportCodes
            select (connection.DepartureAirportCode, arrival.ArrivalCode);

        var routes = allRoutes.Distinct();

        foreach (var route in routes)
        {
            var departureDates = await _timetables.GetFlyingDays(route.DepartureAirportCode, route.ArrivalCode, airlines);
            var departureDate = departureDates.FirstOrDefault(dateTime => earliestDepartureDate <= dateTime.Date);
            if (departureDate == default)
            {
                continue;
            }

            yield return new RyanairRefreshRouteFee
            {
                Departure = route.DepartureAirportCode,
                Arrival = route.ArrivalCode,
                DepartureDate = DateOnly.FromDateTime(departureDate)
            };
        }
    }
}