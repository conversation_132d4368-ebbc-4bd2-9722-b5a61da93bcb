<Project Sdk="Microsoft.NET.Sdk.Web">

<PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Exe</OutputType>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <DocumentationFile>Esky.FlightsCache.RobotsProducers.xml</DocumentationFile>
    <Nullable>enable</Nullable>
  </PropertyGroup>

<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
  <NoWarn>1701;1702;1591</NoWarn>
</PropertyGroup>

<ItemGroup>
  <Content Remove="appsettings.Development.json" />
  <Content Remove="appsettings.Production.json" />
  <Content Remove="appsettings.Staging.json" />
</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Cronos" Version="0.7.1" />
    <PackageReference Include="Esky.FlightsCache.OpenTelemetry" Version="1.7.0-rc.1" />
    <PackageReference Include="Esky.NLog.RabbitMQ.Target" Version="1.1.1">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Esky.FlightsCache.ServiceBus" Version="2.0.2" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.64.0.3256" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.64.0.3148" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.2.1">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="HangFire" Version="1.8.18" />
    <PackageReference Include="Hangfire.Console" Version="1.4.3">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.1" />
    <PackageReference Include="HangFire.Mongo" Version="1.9.16" /> 
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.12" />
    <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="6.5.0">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
        <None Include="appsettings.Production.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Include="appsettings.Staging.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Include="appsettings.Development.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Include="appsettings.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
    <ProjectReference Include="..\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots.Messages\Esky.FlightsCache.Robots.Messages.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots\Esky.FlightsCache.Robots.csproj" /> 
  </ItemGroup>

    <ItemGroup>
      <Content Update="wwwroot\index.html">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

</Project>
