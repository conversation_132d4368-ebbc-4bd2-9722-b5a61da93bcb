using System;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

public interface IDistributedPublisherBuilder<in T>
{
    IDistributedPublisherBuilder<T> WithJobName(string? name);
    IDistributedPublisherBuilder<T> WithCron(string? cron);
    IDistributedPublisherBuilder<T> WithLogger(Action<string> logger);
    IDistributedPublisherBuilder<T> WithMaxProcessingWindow(TimeSpan? window);
    IDistributedPublisherBuilder<T> WithActivityPeriod(TimeOnly? from, TimeOnly? to);
    IQueueElementPublisher<T> Build();
}