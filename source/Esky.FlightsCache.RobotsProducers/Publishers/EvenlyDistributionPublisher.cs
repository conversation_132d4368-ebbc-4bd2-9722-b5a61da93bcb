using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

internal sealed class EvenlyDistributionPublisher<T> : IQueueElementPublisher<T> where T : class
{
    private readonly BatchBlock<T> _batch;
    private readonly ActionBlock<T[]> _scheduler;

    private readonly DateTimeOffset _now;
    private readonly Action<string> _log;
    private readonly int _elementsCount;
    private readonly TimeSpan _interval;
    private readonly int _chunksCount;

    private int _index = -1;

    public EvenlyDistributionPublisher(
        IEvenlyDistributionParametersBuilder evenlyDistributionParametersBuilder,
        QueuePublisherHangfireWrapper<T> hangfireWrapper,
        EvenlyDistributionPublisherOptions options)
    {
        _log = options.Logger ?? EmptyLogger;
        _elementsCount = options.ElementsCount;
        _now = DateTimeOffset.UtcNow;

        (_interval, var chunkMaxSize, _chunksCount) = evenlyDistributionParametersBuilder.GetEvenlyDistributionParameters(options, _now);
        _log($"Elements {_elementsCount}, ChunksCount {_chunksCount}, ChunkMaxSize {chunkMaxSize}");

        const int maxDegreeOfParallelism = 4;
        _batch = new BatchBlock<T>(chunkMaxSize, new GroupingDataflowBlockOptions { BoundedCapacity = chunkMaxSize * maxDegreeOfParallelism, EnsureOrdered = true });
        _scheduler = new ActionBlock<T[]>(
            async elements =>
            {
                var index = Interlocked.Increment(ref _index);
                var delay = _interval * index;

                await hangfireWrapper.Schedule($"{options.JobName}, part {index + 1} of {_chunksCount}", elements, delay, CancellationToken.None);
            },
            new ExecutionDataflowBlockOptions
            {
                MaxDegreeOfParallelism = maxDegreeOfParallelism,
                BoundedCapacity = maxDegreeOfParallelism,
                EnsureOrdered = true
            }
        );

        _batch.LinkTo(_scheduler, new DataflowLinkOptions { PropagateCompletion = true });
    }

    public async Task Publish(T element, CancellationToken cancellationToken)
    {
        await _batch.SendAsync(element, cancellationToken);
    }

    public async Task Publish(IEnumerable<T> elements, CancellationToken cancellationToken)
    {
        foreach (var element in elements)
        {
            await Publish(element, cancellationToken);
        }
    }

    public async Task Complete(CancellationToken cancellationToken)
    {
        _log("Queue elements generation complete");
        _batch.Complete();
        await _scheduler.Completion.WaitAsync(cancellationToken);
        var delay = _index * _interval;
        _log($"Produced {_elementsCount} messages into {_chunksCount} delayed jobs with interval {_interval}, starting from now ({_now:yyyy-MM-dd HH:mm:ss.ff}) up to +{delay} ({_now + delay:yyyy-MM-dd HH:mm:ss.ff}).");
    }

    private static void EmptyLogger(string _) { }
}
public record EvenlyDistributionPublisherOptions
{
    public required int ElementsCount { get; init; }
    public required string JobName { get; init; }
    public required string CronExpression { get; init; }
    public required TimeOnly ActiveFrom { get; init; }
    public required TimeOnly ActiveTo { get; init; }
    public Action<string>? Logger { get; init; }
}