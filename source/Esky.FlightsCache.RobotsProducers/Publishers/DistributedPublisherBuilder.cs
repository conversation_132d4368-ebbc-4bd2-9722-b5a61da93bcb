using Esky.FlightsCache.RobotsProducers.Configuration;
using System;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

internal class DistributedPublisherBuilder<T> : IDistributedPublisherBuilder<T> where T : class
{
    private readonly RobotsProducerEvenlyDistributionSettings _baseSettings;
    private readonly QueuePublisherHangfireWrapper<T> _hangfireWrapper;

    private RobotsProducerEvenlyDistributionSettings _settings;
    private readonly int _elementsCount;
    private Action<string>? _logger;
    private string? _jobName;
    private string? _cron;
    private TimeOnly? _activeFrom;
    private TimeOnly? _activeTo;

    public DistributedPublisherBuilder(
        RobotsProducerEvenlyDistributionSettings baseSettings,
        QueuePublisherHangfireWrapper<T> hangfireWrapper,
        int elementsCount,
        Action<string>? logger = null)
    {
        if (elementsCount <= 0)
            throw new ArgumentOutOfRangeException(nameof(elementsCount), "should be greater than zero");

        _baseSettings = baseSettings;
        _settings = baseSettings;
        _hangfireWrapper = hangfireWrapper;
        _elementsCount = elementsCount;
        _logger = logger;
    }

    public IDistributedPublisherBuilder<T> WithJobName(string? name)
    {
        _jobName = name;
        return this;
    }

    public IDistributedPublisherBuilder<T> WithCron(string? cron)
    {
        _cron = cron;
        return this;
    }

    public IDistributedPublisherBuilder<T> WithLogger(Action<string> logger)
    {
        _logger = logger;
        return this;
    }

    public IDistributedPublisherBuilder<T> WithActivityPeriod(TimeOnly? from, TimeOnly? to)
    {
        _activeFrom = from;
        _activeTo = to;
        return this;
    }

    public IDistributedPublisherBuilder<T> WithMaxProcessingWindow(TimeSpan? window)
    {
        _settings = _settings with { MaxProcessingWindow = window ?? _baseSettings.MaxProcessingWindow };
        return this;
    }

    public IQueueElementPublisher<T> Build()
    {
        return new EvenlyDistributionPublisher<T>(
            new EvenlyDistributionParametersBuilder(_settings),
            _hangfireWrapper,
            new EvenlyDistributionPublisherOptions
            {
                JobName = _jobName ?? $"Job-{Guid.NewGuid()}",
                CronExpression = _cron ?? "* * * * *",
                ElementsCount = _elementsCount,
                Logger = _logger,
                ActiveFrom = _activeFrom ?? TimeOnly.MinValue,
                ActiveTo = _activeTo ?? TimeOnly.MaxValue
            }
        );
    }
}