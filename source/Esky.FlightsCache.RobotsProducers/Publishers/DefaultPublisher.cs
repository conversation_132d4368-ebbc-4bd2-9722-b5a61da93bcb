using Esky.FlightsCache.RobotsProducers.Messages;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

public sealed class DefaultPublisher<T> : IQueueElementPublisher<T> where T : class
{
    private readonly ActionBlock<T> _scheduler;

    public DefaultPublisher(IQueuePublisher publisher)
    {
        const int maxDegreeOfParallelism = 4;

        _scheduler = new ActionBlock<T>(
            async element => await publisher.PublishSingle(element, CancellationToken.None),
            new ExecutionDataflowBlockOptions
            {
                MaxDegreeOfParallelism = maxDegreeOfParallelism,
                BoundedCapacity = maxDegreeOfParallelism,
                EnsureOrdered = false
            }
        );
    }

    public async Task Publish(T element, CancellationToken cancellationToken)
    {
        await _scheduler.SendAsync(element, cancellationToken);
    }

    public async Task Publish(IEnumerable<T> elements, CancellationToken cancellationToken)
    {
        foreach (var element in elements)
        {
            await Publish(element, cancellationToken);
        }
    }

    public async Task Complete(CancellationToken cancellationToken)
    {
        _scheduler.Complete();
        await _scheduler.Completion.WaitAsync(cancellationToken);
    }
}