using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.Messages;
using System;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

internal class QueueElementPublisherBuilder<T> : IQueueElementPublisherBuilder<T> where T : class
{
    private readonly RobotsProducerEvenlyDistributionSettings _evenlyDistributionSettings;
    private readonly QueuePublisherHangfireWrapper<T> _hangfireWrapper;
    private readonly IQueuePublisher _queuePublisher;

    private Action<string>? _logger;

    public QueueElementPublisherBuilder(
        RobotsProducerEvenlyDistributionSettings evenlyDistributionSettings,
        IQueuePublisher queuePublisher, 
        QueuePublisherHangfireWrapper<T> hangfireWrapper)
    {
        _evenlyDistributionSettings = evenlyDistributionSettings;
        _queuePublisher = queuePublisher;
        _hangfireWrapper = hangfireWrapper;
    }

    public IDistributedPublisherBuilder<T> WithEvenlyDistribution(int elementsCount)
    {
        return new DistributedPublisherBuilder<T>(
            _evenlyDistributionSettings,
            _hangfireWrapper,
            elementsCount,
            _logger
        );
    }

    public IQueueElementPublisherBuilder<T> WithLogger(Action<string>? logger)
    {
        _logger = logger;
        return this;
    }

    public IQueueElementPublisher<T> Build()
    {
        return new DefaultPublisher<T>(_queuePublisher);
    }
}