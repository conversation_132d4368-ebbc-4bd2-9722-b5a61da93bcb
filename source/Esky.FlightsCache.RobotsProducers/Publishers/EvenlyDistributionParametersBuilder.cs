using Cronos;
using Esky.FlightsCache.RobotsProducers.Configuration;
using System;

namespace Esky.FlightsCache.RobotsProducers.Publishers
{
    public record EvenlyDistributionParameters(TimeSpan Interval, int ChunkSize, int ChunksCount);

    public interface IEvenlyDistributionParametersBuilder
    {
        EvenlyDistributionParameters GetEvenlyDistributionParameters(EvenlyDistributionPublisherOptions options, DateTimeOffset currentTimestamp);
    }

    public class EvenlyDistributionParametersBuilder : IEvenlyDistributionParametersBuilder
    {
        private readonly RobotsProducerEvenlyDistributionSettings _settings;

        public EvenlyDistributionParametersBuilder(RobotsProducerEvenlyDistributionSettings settings)
        {
            _settings = settings;
        }

        public EvenlyDistributionParameters GetEvenlyDistributionParameters(EvenlyDistributionPublisherOptions options, DateTimeOffset currentTimestamp)
        {
            if (options.ElementsCount == 0)
            {
                return new EvenlyDistributionParameters(_settings.Interval, 1, 1);
            }

            var processingWindow = GetProcessingWindow(options.CronExpression, currentTimestamp, _settings.MaxProcessingWindow, options.ActiveFrom, options.ActiveTo);
            var availableIntervals = GetAvailableIntervals(processingWindow);
            var chunkSize = (int)Math.Ceiling((decimal)options.ElementsCount / availableIntervals);
            var chunksCount = (int)Math.Ceiling((decimal)options.ElementsCount / chunkSize);
            var interval = chunksCount == 1
                ? _settings.Interval
                : processingWindow / chunksCount;

            return new EvenlyDistributionParameters(interval, chunkSize, chunksCount);

            static TimeSpan GetProcessingWindow(string cronExpression, DateTimeOffset currentTimestamp, TimeSpan maxProcessingWindow, TimeOnly activeFrom, TimeOnly activeTo)
            {
                try
                {
                    var expression = CronExpression.Parse(cronExpression);
                    var nextOccurenceDateTime = expression.GetNextOccurrence(currentTimestamp.UtcDateTime);
                    if (nextOccurenceDateTime == null)
                    {
                        return TimeSpan.Zero;
                    }
                    var end = AdjustEndDateToActivityPeriod(currentTimestamp, nextOccurenceDateTime.Value, activeFrom, activeTo);
                    var diff = end - currentTimestamp.UtcDateTime;
                    return diff > maxProcessingWindow ? maxProcessingWindow : diff;
                }
                catch (ArgumentNullException)
                {
                    return TimeSpan.Zero;
                }
                catch (CronFormatException)
                {
                    return TimeSpan.Zero;
                }
            }

            int GetAvailableIntervals(TimeSpan processingWindow)
            {
                if (_settings.Interval == TimeSpan.Zero
                    || processingWindow == TimeSpan.Zero
                    || processingWindow < _settings.Interval)
                    return 1;

                return (int)Math.Ceiling(processingWindow / _settings.Interval);
            }

            static DateTimeOffset AdjustEndDateToActivityPeriod(DateTimeOffset start, DateTimeOffset end, TimeOnly activeFrom, TimeOnly activeTo)
            {
                if(activeFrom == TimeOnly.MinValue && activeTo == TimeOnly.MaxValue)
                {
                    return end;
                }

                var activeToDateTime = start.UtcDateTime.Date.Add(activeTo.ToTimeSpan());

                if (activeToDateTime <= start.UtcDateTime)
                {
                    activeToDateTime = activeToDateTime.AddDays(1);
                }

                return end.UtcDateTime <= activeToDateTime
                    ? end
                    : activeToDateTime;
            }
        }
    }
}