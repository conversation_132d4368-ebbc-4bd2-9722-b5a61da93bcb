using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.RobotsProducers.Hangfire;
using Esky.FlightsCache.RobotsProducers.Messages;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Publishers;

internal class QueuePublisherHangfireWrapper<T> where T : class
{
    private readonly IQueuePublisher _endpoint;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IMongoCollection<StoragePart> _mongoCollection;
    private readonly ILogger<QueuePublisherHangfireWrapper<T>> _logger;

    public QueuePublisherHangfireWrapper(
        IQueuePublisher endpoint,
        HangfireSettings databaseSettings,
        IBackgroundJobClient backgroundJobClient,
        ILogger<QueuePublisherHangfireWrapper<T>> logger)
    {
        _endpoint = endpoint;
        _backgroundJobClient = backgroundJobClient;
        _logger = logger;
        
        var mongoUrl = MongoUrl.Create(databaseSettings.ConnectionString);
        _mongoCollection = new MongoClient(mongoUrl)
            .GetDatabase(mongoUrl.DatabaseName)
            .GetCollection<StoragePart>("Hangfire.queueElements");

        _mongoCollection.EnsureIndex("key_IX", builder => builder.Ascending(e => e.Key));
    }

    public async Task Schedule(string jobName, T[] elements, TimeSpan delay, CancellationToken cancellationToken)
    {
        var key = await Store(elements, cancellationToken);
        _backgroundJobClient.Schedule<QueuePublisherHangfireWrapper<T>>(p => p.Publish(jobName, elements.Length, key, default, default), delay);
    }

    [DisplayName("ProducerPublish - job: {0}")]
    public async Task Publish(string jobName, int count, string key, PerformContext? context, CancellationToken cancellationToken)
    {
        context?.WriteLine($"Publishing {count} elements to Consumer's queue started for type {typeof(T).Name}");
        var counter = 0;
        var progress = context?.WriteProgressBar();
        
        var filter = Builders<StoragePart>.Filter.Eq(e => e.Key, key);
        var cursor = await _mongoCollection.FindAsync(filter, new FindOptions<StoragePart> { BatchSize = 4 }, cancellationToken: cancellationToken);
        await cursor.ForEachAsync(
            async e =>
            {
                await _endpoint.Publish(e.Elements, cancellationToken);
                counter += e.Elements.Count;
                progress?.SetValue((double)counter / count * 100.0);
            },
            cancellationToken: cancellationToken
        );

        await _mongoCollection.DeleteManyAsync(e => e.Key == key, CancellationToken.None);
        
        context?.WriteLine($"Publishing {counter} elements to Consumer's queue done");
    }

    private async Task<string> Store(IEnumerable<T> elements, CancellationToken cancellationToken)
    {
        try
        {
            var key = Guid.NewGuid().ToString();
            var storageParts = elements
                .Chunk(200)
                .Select(e => new StoragePart { Key = key, Elements = e.ToList() });
            await _mongoCollection.InsertManyAsync(storageParts, new InsertManyOptions { IsOrdered = false }, cancellationToken);
            return key;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Could not store queue elements");
            throw;
        }
    }

    private class StoragePart
    {
        [BsonId]
        public ObjectId Id { get; set; }
        public required string Key { get; set; }
        public required List<T> Elements { get; set; }
    }
}