using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire;
using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Composite;

public class CompositeGroupConfigurationExamples : IMultipleExamplesProvider<CompositeGroupConfiguration>
{
    public IEnumerable<SwaggerExample<CompositeGroupConfiguration>> GetExamples()
    {
        var reference = new Miscellaneous.Configuration { Cron = Cron.Never(), RelativeDateRange = 1..14 };

        yield return new SwaggerExample<CompositeGroupConfiguration>
        {
            Name = "2 levels composed SSC + TravelFusion instant search",
            Value = new CompositeGroupConfiguration
            {
                Name = "Composite 2 layer EasyJet",
                Configurations =
                    [reference with { RelativeDateRange = 1..8 }, reference with { RelativeDateRange = 22..53 }],
                DepartureAirports = ["LGW", "LCY"],
                ArrivalAirports = ["BCN", "PMI"],
                Provider = ProviderCodeEnum.SSCProvider,
                Supplier = "easyjet",
                PartnerCode = "ADMIN",
                Airlines = ["U2"],
                Flex = 7,
                PaxConfigurations = ["*******"],
                ProceedNextOnSuccess = false,
                Next = new NextInCompositeJobSettings
                {
                    Provider = ProviderCodeEnum.TravelFusion,
                    Supplier = "ezy",
                    PartnerCode = "ADMIN",
                    Flex = 0,
                    ProceedNextOnSuccess = false,
                    SkipTheSameDepartureDateAsReturnDeparture = true,
                },
                ActiveFromHour = 5,
                ActiveToHour = 0,
            }
        };

        yield return new SwaggerExample<CompositeGroupConfiguration>
        {
            Name = "3 levels composed SSC + TravelFusion instant search + TF live search",
            Value = new CompositeGroupConfiguration
            {
                Name = "Composite 3lvl EasyJet",
                Configurations =
                    [reference with { RelativeDateRange = 1..8 }, reference with { RelativeDateRange = 22..53 }],
                DepartureAirports = ["LGW", "LCY"],
                ArrivalAirports = ["BCN", "PMI"],
                Provider = ProviderCodeEnum.SSCProvider,
                Supplier = "easyjet",
                PartnerCode = "ADMIN",
                Airlines = ["U2"],
                Flex = 7,
                PaxConfigurations = ["*******"],
                ProceedNextOnSuccess = false,
                Next = new NextInCompositeJobSettings
                {
                    Provider = ProviderCodeEnum.TravelFusion,
                    Supplier = "ezy",
                    PartnerCode = "ADMIN",
                    Flex = 0,
                    SkipTheSameDepartureDateAsReturnDeparture = true,
                    ProceedNextOnSuccess = false,
                    Next = new NextInCompositeJobSettings
                    {
                        PartnerCode = "MASTER",
                        ProceedNextOnSuccess = false,
                        SkipTheSameDepartureDateAsReturnDeparture = true,
                    },
                },
                ActiveFromHour = 5,
                ActiveToHour = 0,
            }
        };
    }
}