using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.SSC;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;
using System.Collections.Generic;
using System.Threading;

namespace Esky.FlightsCache.RobotsProducers.Composite;

[ApiController]
[Route("api/[controller]/Configuration")]
public class CompositeRobotController : BaseGroupedJobsController<CompositeGroupConfiguration, CompositeJobSettings>
{
    public CompositeRobotController(IJobConverter<CompositeGroupConfiguration, CompositeJobSettings> converter) : base(converter)
    {
    }
    
    /// <summary>
    /// upsert configuration by Name as id
    /// </summary>
    [HttpPost("Groups")]
    [ProducesResponseType(200)]
    [ProducesResponseType<GroupValidation>(400)]
    [SwaggerRequestExample(typeof(CompositeGroupConfiguration), typeof(CompositeGroupConfigurationExamples))]
    public override IActionResult UpsertGroupConfiguration(
        [FromBody] CompositeGroupConfiguration groupConfiguration,
        [FromServices] IRecurringJobManager recurringJobManager)
        => base.UpsertGroupConfiguration(groupConfiguration, recurringJobManager);
    
    private protected override void AddJob(IRecurringJobManager recurringJobManager, string jobName, CompositeJobSettings settings)
    {
        recurringJobManager.AddOrUpdate<CompositeQueueElementProducer>(
            jobName,
            p => p.Produce(settings, null, CancellationToken.None),
            settings.Cron
        );
    }

    private protected override IEnumerable<string> GetJobNames(string name) =>
        CompositeGroupConfiguration.GetJobNames(name);
}