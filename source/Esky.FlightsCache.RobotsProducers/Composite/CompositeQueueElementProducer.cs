using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Composite;

public class CompositeQueueElementProducer
{
    private readonly IRouteDatesProvider _routeDatesProvider;
    private readonly IQueueElementPublisherBuilder<CompositeQueueElement> _publisherBuilder;

    public CompositeQueueElementProducer(
        IRouteDatesProvider routeDatesProvider,
        IQueueElementPublisherBuilder<CompositeQueueElement> publisherBuilder)
    {
        _routeDatesProvider = routeDatesProvider;
        _publisherBuilder = publisherBuilder;
    }

    [DisplayName("Composite {0}")]
    public async Task Produce(CompositeJobSettings jobSettings, PerformContext? performContext, CancellationToken cancellationToken)
    {
        Action<string> log = performContext is null ? Console.WriteLine : performContext.WriteLine;

        var elements = await CreateQueueElements(jobSettings, log);
        
        if (elements.Length == 0)
        {
            log("No queue elements generated");
            return;
        }

        var publisher = _publisherBuilder
            .WithEvenlyDistribution(elements.Length)
            .WithMaxProcessingWindow(jobSettings.MaxProcessingWindow)
            .WithCron(jobSettings.Cron)
            .WithLogger(log)
            .WithJobName($"Composite[{jobSettings}]")
            .WithActivityPeriod(jobSettings.ActiveFrom, jobSettings.ActiveTo)
            .Build();
        await publisher.Publish(elements, cancellationToken);
        await publisher.Complete(cancellationToken);
    }
    
    public async Task<CompositeQueueElement[]> CreateQueueElements(CompositeJobSettings jobSettings, Action<string> log)
    {
        var routeDates = await _routeDatesProvider.GetRouteDates(jobSettings, log);
        return await Task.WhenAll(routeDates.Select(routeDate => CreateQueueElement(routeDate, jobSettings, log)));
    }
    
    private async Task<CompositeQueueElement> CreateQueueElement(RouteDate routeDate, CompositeJobSettings jobSettings, Action<string> log, int depth = 0)
    {
        var nextLayerElements = await GetNextLayerElements();

        return new CompositeQueueElement
        {
            ProceedNextOnSuccess = jobSettings.ProceedNextOnSuccess && nextLayerElements != null,
            NextQueueElements = nextLayerElements ?? [],
            
            DepartureCode = routeDate.Route.Departure,
            ArrivalCode = routeDate.Route.Arrival,
            
            DepartureDay = routeDate.DepartureDay,
            DeleteDepartureDayFrom = routeDate.DepartureDay.AddDays(-jobSettings.Flex),
            DeleteDepartureDayTo = routeDate.DepartureDay.AddDays(jobSettings.Flex),
            
            ReturnDepartureDay = routeDate.ReturnDepartureDay,
            IsRoundTrip = routeDate.ReturnDepartureDay is not null,
            DeleteReturnDepartureDayFrom = routeDate.ReturnDepartureDay?.AddDays(-jobSettings.Flex),
            DeleteReturnDepartureDayTo = routeDate.ReturnDepartureDay?.AddDays(jobSettings.Flex),
            
            Flex = jobSettings.Flex,
            
            ProviderCode = jobSettings.Provider,
            Supplier = jobSettings.Supplier,
            PartnerCode = jobSettings.PartnerCode,
            PaxConfigurations = jobSettings.PaxConfigurations,
            OverrideSettings = jobSettings.OverrideSettings,
            Depth = depth,
        };

        async Task<IReadOnlyCollection<CompositeQueueElement>?> GetNextLayerElements()
        {
            var nextLayer = jobSettings.Next;
            if (nextLayer == null)
            {
                return null;
            }

            var nextJobSettings = jobSettings with
            {
                ArrivalAirports = [],
                DepartureAirports = [],
                Routes = [ routeDate.Route ],
                RelativeDateRange = new RelativeDateRange(routeDate.DepartureDay.AddDays(-jobSettings.Flex), routeDate.DepartureDay.AddDays(jobSettings.Flex)),
                Provider = nextLayer.Provider ?? jobSettings.Provider,
                Supplier = nextLayer.Supplier ?? jobSettings.Supplier,
                PartnerCode = nextLayer.PartnerCode ?? jobSettings.PartnerCode,
                PaxConfigurations = nextLayer.PaxConfigurations ?? jobSettings.PaxConfigurations,
                Flex = nextLayer.Flex ?? jobSettings.Flex,
                StayLengths = nextLayer.StayLengths ?? jobSettings.StayLengths,
                SkipTheSameDepartureDateAsReturnDeparture = nextLayer.SkipTheSameDepartureDateAsReturnDeparture ??
                                                            jobSettings.SkipTheSameDepartureDateAsReturnDeparture,
                ProceedNextOnSuccess = nextLayer.ProceedNextOnSuccess,
                Next = nextLayer.Next
            };

            IEnumerable<RouteDate> nextRouteDates;
            if (nextJobSettings.Flex == jobSettings.Flex 
                && nextJobSettings.SkipTheSameDepartureDateAsReturnDeparture == jobSettings.SkipTheSameDepartureDateAsReturnDeparture
                && nextJobSettings.StayLengths.SequenceEqual(jobSettings.StayLengths))
            {
                nextRouteDates = [routeDate];
            }
            else
            {
                var routeDateRange = new DateRange(routeDate.DepartureDay.AddDays(-jobSettings.Flex),
                    routeDate.DepartureDay.AddDays(+jobSettings.Flex));
                nextRouteDates = (await _routeDatesProvider.GetRouteDates(nextJobSettings, log))
                    .Where(nextRouteDate => routeDateRange.Contains(nextRouteDate.DepartureDay));
            }

            return await Task.WhenAll(nextRouteDates.Select(nextRouteDate =>
                CreateQueueElement(nextRouteDate, nextJobSettings, log, depth + 1)));
        }
    }
}