using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;

public class TravelFusionSuppliersProducer
{
    private readonly IWeekDayAlgorithm _algorithm;
    private readonly IQueueElementPublisherBuilder<TravelFusionQueueElement> _builder;
    private readonly ITravelFusionSupplierRepository _supplierRepository;
    private readonly IPartnerSettingsService _partnerSettingsService;

    public static readonly string JobName = "TravelFusionSuppliersProducer";

    public TravelFusionSuppliersProducer(
        IWeekDayAlgorithm algorithm,
        IQueueElementPublisherBuilder<TravelFusionQueueElement> builder,
        ITravelFusionSupplierRepository supplierRepository,
        IPartnerSettingsService partnerSettingsService)
    {
        _algorithm = algorithm;
        _builder = builder;
        _supplierRepository = supplierRepository;
        _partnerSettingsService = partnerSettingsService;
    }

    [DisplayName("[TravelFusion] for all categorized suppliers by week day {0}")]
    public async Task Execute(Parameters parameters, PerformContext? performContext, CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var travelFusionSuppliers = await _supplierRepository.GetSuppliers(category: null, cancellationToken);
        var suppliers = travelFusionSuppliers
            .Select(s => s.Id.Supplier)
            .Order()
            .ToArray();
        var daysForward = (await _partnerSettingsService.GetPartnerSettingsAsync(RobotConstants.PARTNER_CODE))
            .SpecialOccasionsSettings
            .RobotsConfiguration
            .NumberOfDaysForward;
        
        var elements = await _algorithm
            .Generate(
                RobotConstants.PARTNER_CODE,
                suppliers,
                now,
                daysForward,
                ProviderCodeEnum.TravelFusion
            );

        var publisher = _builder
            .WithEvenlyDistribution(elements.Count())
            .WithJobName(nameof(TravelFusionSuppliersProducer))
            .WithCron(parameters.Cron)
            .WithLogger(performContext.WriteLine)
            .Build();

        await publisher.Publish(elements.Select(e => e.To<TravelFusionQueueElement>()), cancellationToken);
        await publisher.Complete(cancellationToken);
    }

    public record Parameters
    {
        public required string Cron { get; init; }
        public override string ToString() => string.Empty;
    }
}