using Esky.FlightsCache.RobotsProducers.Configuration;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public sealed class TravelFusionSupplierRepository : ITravelFusionSupplierRepository
    {
        private readonly IMongoCollection<TravelFusionSupplier> _suppliers;

        public TravelFusionSupplierRepository(DatabaseSettings databaseSettings)
        {
            var mongoClient = new MongoClient(databaseSettings.ConnectionString);
            _suppliers = mongoClient.GetDatabase(databaseSettings.DatabaseName).GetCollection<TravelFusionSupplier>("TravelFusionSuppliers");
        }

        public Task UpsertSupplier(string supplier, TravelFusionSupplierRequest supplierRequest)
        {
            var newSupplier = new TravelFusionSupplier
            {
                Id = new TravelFusionSupplier.TravelFusionSupplierId { Supplier = supplier },
                Category = supplierRequest.Category,
                PaxConfiguration = supplierRequest.PaxConfiguration,
            };

            return _suppliers.ReplaceOneAsync(s => s.Id.Equals(newSupplier.Id), newSupplier, new ReplaceOptions { IsUpsert = true });
        }

        public Task<List<TravelFusionSupplier>> GetSuppliers(TravelFusionSupplierCategory? category, CancellationToken cancellationToken)
        {
            var query = _suppliers.Aggregate();

            if (category.HasValue)
            {
                query = query.Match(s => s.Category == category.Value);
            }

            return query.ToListAsync(cancellationToken);
        }

        public Task RemoveSuppliers(string supplier)
        {
            return _suppliers.DeleteOneAsync(x => x.Id.Supplier == supplier);
        }
    }
}
