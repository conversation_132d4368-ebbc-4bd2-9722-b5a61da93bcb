using Esky.FlightsCache.Robots;
using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public class TravelFusionProducer
    {
        private readonly IDayByDayAlgorithm _dayByDayAlgorithm;
        private readonly IQueueElementPublisherBuilder<TravelFusionQueueElement> _publisherBuilder;

        public TravelFusionProducer(
            IDayByDayAlgorithm dayByDayAlgorithm,
            IQueueElementPublisherBuilder<TravelFusionQueueElement> publisherBuilder)
        {
            _dayByDayAlgorithm = dayByDayAlgorithm;
            _publisherBuilder = publisherBuilder;
        }

        internal static string GetJobName(string[] suppliers, int daysToDepartureFrom, int daysToDepartureTo) =>
            $"TravelFusion {string.Join(",", suppliers)} producer, from now + {daysToDepartureFrom} to {daysToDepartureTo} days";


        [DisplayName("{0}")]
        public async Task GenerateForSuppliersEvenlyDistributed(GenerateForSuppliersRequest request,
            PerformContext? context, CancellationToken cancellationToken)
        {
            var departureFrom = DateTime.UtcNow.AddDays(request.DaysToDepartureFrom).Date;
            var departureTo = DateTime.UtcNow.AddDays(request.DaysToDepartureTo).Date;

            var (elements, count) = await _dayByDayAlgorithm.GenerateTravelFusionElements(
                request.Suppliers,
                [],
                request.PartnerCode,
                departureFrom,
                departureTo,
                GetMinAge(),
                $"{ProviderCodeEnum.TravelFusion}Consumer_Suppliers_{string.Join(",", request.Suppliers)}",
                PaxConfigurations.AdultChildInfant);

            var publisher = _publisherBuilder
                .WithEvenlyDistribution(count)
                .WithCron(request.CronExpression)
                .WithJobName(request.JobName)
                .WithLogger(context.WriteLine)
                .Build();
            await publisher.Publish(elements, cancellationToken);
            await publisher.Complete(cancellationToken);

            TimeSpan GetMinAge()
            {
                try
                {
                    var cron = Cronos.CronExpression.Parse(request.CronExpression);
                    var firstOccurence = cron.GetNextOccurrence(DateTime.UtcNow);
                    var secondOccurence = cron.GetNextOccurrence(firstOccurence!.Value);
                    return (secondOccurence - firstOccurence)!.Value / 2;
                }
                catch (Exception)
                {
                    return TimeSpan.Zero;
                }
            }
        }
    }

    public record GenerateForSuppliersRequest
    {
        public required string[] Suppliers;
        public int DaysToDepartureFrom;
        public int DaysToDepartureTo;
        public required string PartnerCode;
        public string? CronExpression;
        public string? JobName;

        public override string ToString() =>
            $"[TravelFusion] GenerateForSuppliers: {string.Join(",", Suppliers)}, from now + {DaysToDepartureFrom} to {DaysToDepartureTo} days ({PartnerCode})";
    }
}