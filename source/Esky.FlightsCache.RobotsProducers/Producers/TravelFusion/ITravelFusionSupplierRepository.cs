using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public interface ITravelFusionSupplierRepository
    {
        Task UpsertSupplier(string supplier, TravelFusionSupplierRequest supplierRequest);
        Task<List<TravelFusionSupplier>> GetSuppliers(TravelFusionSupplierCategory? category, CancellationToken cancellationToken);
        Task RemoveSuppliers(string supplier);
    }
}
