using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Server;
using Hangfire.Console;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public class DiscoverRoutesProducer
    {
        private readonly IDayByDayAlgorithm _dayByDayAlgorithm;
        private readonly IPartnerSettingsService _partnerSettings;
        private readonly ITravelFusionSupplierRepository _supplierRepository;
        private readonly IQueueElementPublisherBuilder<TravelFusionQueueElement> _builder;

        public DiscoverRoutesProducer(
            IDayByDayAlgorithm dayByDayAlgorithm,
            IPartnerSettingsService partnerSettings,
            ITravelFusionSupplierRepository supplierRepository,
            IQueueElementPublisherBuilder<TravelFusionQueueElement> builder)
        {
            _dayByDayAlgorithm = dayByDayAlgorithm;
            _partnerSettings = partnerSettings;
            _supplierRepository = supplierRepository;
            _builder = builder;
        }

        public static string GetJobName() => $"TravelFusion Discover routes producer";

        [DisplayName("[TravelFusion] GenerateForAllSuppliers for all new routes")]
        public async Task GenerateForAllSuppliers(Parameters parameters, PerformContext? performContext, CancellationToken cancellationToken)
        {
            var settings = await _partnerSettings.GetPartnerSettingsAsync(RobotConstants.PARTNER_CODE);
            var nDaysForward = settings.SpecialOccasionsSettings.RobotsConfiguration.NumberOfDaysForward;
            var startDate = DateTime.Now.AddDays(1).Date;
            var endDate = startDate.AddDays(nDaysForward);
            var suppliers = (await _supplierRepository.GetSuppliers(null, CancellationToken.None))
                .Select(x => x.Id.Supplier).Order().ToArray();

            var elements = await _dayByDayAlgorithm.Generate(
                RobotConstants.PARTNER_CODE,
                ProviderCodeEnum.TravelFusion,
                startDate,
                endDate,
                suppliers);
            
            var count = elements.Count();

            if (count == 0)
            {
                performContext?.WriteLine("No elements to process");
                return;
            }

            var publisher = _builder
                .WithEvenlyDistribution(count)
                .WithJobName(nameof(DiscoverRoutesProducer))
                .WithCron(parameters.Cron)
                .WithLogger(performContext.WriteLine)
                .Build();

            await publisher.Publish(elements.Select(e => e.To<TravelFusionQueueElement>()), cancellationToken);
            await publisher.Complete(cancellationToken);
        }

        public record Parameters
        {
            public string? Cron { get; init; }
            public override string ToString() => string.Empty;
        }
    }
}