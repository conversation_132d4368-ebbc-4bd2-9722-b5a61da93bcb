using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots.Algorithms;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;

public sealed class TravelFusionCategorizedProducer
{
    private readonly IQueueElementPublisherBuilder<TravelFusionQueueElement> _publisherBuilder;
    private readonly IPartnerSettingsService _partnerSettings;
    private readonly ITravelFusionSupplierRepository _supplierRepository;
    private readonly IDayByDayAlgorithm _dayByDayAlgorithm;

    public TravelFusionCategorizedProducer(
        IQueueElementPublisherBuilder<TravelFusionQueueElement> publisherBuilder,
        IPartnerSettingsService partnerSettings,
        ITravelFusionSupplierRepository supplierRepository,
        IDayByDayAlgorithm dayByDayAlgorithm)
    {
        _publisherBuilder = publisherBuilder;
        _partnerSettings = partnerSettings;
        _supplierRepository = supplierRepository;
        _dayByDayAlgorithm = dayByDayAlgorithm;
    }

    internal static string GetJobName(TravelFusionSupplierCategory suppliersCategory) => $"TravelFusion producer for category: {suppliersCategory}";

    [DisplayName("[TravelFusion] GenerateForSuppliersCategory: {0}")]
    public async Task GenerateForCategoryEvenlyDistributed(
        TravelFusionSupplierCategory suppliersCategory,
        string cronExpression,
        string jobName,
        PerformContext? context,
        CancellationToken cancellationToken)
    {
        var allSuppliers = await _supplierRepository.GetSuppliers(category: null, cancellationToken);

        var categorySuppliers = allSuppliers.Where(s => s.Category == suppliersCategory).ToList();

        if (!categorySuppliers.Any())
        {
            context?.WriteLine($"No suppliers defined for category: {suppliersCategory}.");
            return;
        }

        var partnerSettings = await _partnerSettings.GetPartnerSettingsAsync(RobotConstants.PARTNER_CODE);
        var nDaysForward = partnerSettings.SpecialOccasionsSettings.RobotsConfiguration.NumberOfDaysForward;

        var distinctPaxConfiguration = categorySuppliers
            .Select(x => x.PaxConfiguration)
            .Distinct().ToList();
        context?.WriteLine($"Distinct PaxConfigurations: {string.Join(",", distinctPaxConfiguration)}.");

        foreach (var paxConfiguration in distinctPaxConfiguration)
        {
            var suppliers = ToSortedSupplierSet(categorySuppliers, s => s.PaxConfiguration == paxConfiguration);
            var exceptSuppliers = ToSortedSupplierSet(allSuppliers, s => s.Category < suppliersCategory && s.PaxConfiguration == paxConfiguration);

            context?.WriteLine($"PaxConfiguration: {paxConfiguration}.");
            context?.WriteLine($"Suppliers: {string.Join(",", suppliers)}.");
            context?.WriteLine($"Except suppliers: {string.Join(",", exceptSuppliers)}.");

            var (elements, count) = await _dayByDayAlgorithm.GenerateTravelFusionElements(
                suppliers,
                exceptSuppliers,
                RobotConstants.PARTNER_CODE,
                DateTime.Today.AddDays(1),
                DateTime.Today.AddDays(nDaysForward),
                suppliersCategory.GetPeriod() / 2,
                paxConfiguration: paxConfiguration,
                sourceName: $"{ProviderCodeEnum.TravelFusion}Consumer_Categorized_{suppliersCategory}_{paxConfiguration}");

            var publisher = _publisherBuilder
                .WithEvenlyDistribution(count)
                .WithCron(cronExpression)
                .WithJobName($"{jobName}, paxConfiguration:{paxConfiguration}")
                .WithLogger(context.WriteLine)
                .Build();

            await publisher.Publish(elements, cancellationToken);
            await publisher.Complete(cancellationToken);
        }
    }

    private static IReadOnlySet<string> ToSortedSupplierSet(
        IEnumerable<TravelFusionSupplier> suppliers,
        Func<TravelFusionSupplier, bool> filter)
    {
        return suppliers
            .Where(filter)
            .Select(s => s.Id.Supplier)
            .OrderBy(s => s)
            .ToHashSet();
    }
}