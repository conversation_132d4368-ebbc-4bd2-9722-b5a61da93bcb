using Esky.FlightsCache.Robots;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public class TravelFusionSupplierRequest
    {
        public TravelFusionSupplierCategory Category { get; set; }

        [RegularExpression(@"^\d\.\d\.\d\.\d$", ErrorMessage = "Not valid paxConfiguration value")]
        [DefaultValue(PaxConfigurations.AdultChildInfant)]
        public string PaxConfiguration { get; set; } = PaxConfigurations.AdultChildInfant;
    }
}
