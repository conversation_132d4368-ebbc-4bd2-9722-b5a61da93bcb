using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.TravelFusion
{
    public enum TravelFusionSupplierCategory
    {
        Every60Minutes = 1,
        Every3Hours = 2,
        Every6Hours = 3,
        Every12Hours = 4,
    }

    public static class TravelFusionSupplierCategoryExtensions
    {
        public static TimeSpan GetPeriod(this TravelFusionSupplierCategory category) =>
            category switch
            {
                TravelFusionSupplierCategory.Every60Minutes => TimeSpan.FromMinutes(60),
                TravelFusionSupplierCategory.Every3Hours => TimeSpan.FromHours(3),
                TravelFusionSupplierCategory.Every6Hours => TimeSpan.FromHours(6),
                TravelFusionSupplierCategory.Every12Hours => TimeSpan.FromHours(12),
                _ => TimeSpan.Zero,
            };
    }

}
