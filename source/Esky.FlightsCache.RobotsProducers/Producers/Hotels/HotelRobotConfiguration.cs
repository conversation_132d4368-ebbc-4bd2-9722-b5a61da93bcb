using Esky.Framework.PartnerSettings.Enums;
using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels;

public record HotelRobotConfiguration
{
    public ProviderCodeEnum ProviderCode { get; set; }
    public string? ProviderConfigurationId { get; set; }
    public required int[] HotelMetaCodes { get; set; }
    public DateOnly CheckInDateFrom { get; set; }
    public DateOnly CheckInDateTo { get; set; }
    public int StayLengthFrom { get; set; }
    public int StayLengthTo { get; set; }
    public RoomConfiguration[] Occupancy { get; set; }
    public required string PartnerCode { get; set; }
}

public record RoomConfiguration
{
    public int Adults { get; set; }
    public int[] ChildAges { get; set; } = [];
}