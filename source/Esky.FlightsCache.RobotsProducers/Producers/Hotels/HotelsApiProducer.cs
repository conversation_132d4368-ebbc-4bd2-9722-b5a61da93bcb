using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Hangfire.Console;
using Hangfire.Server;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels;

public interface IHotelsRobotsProducer
{
    Task Publish(string jobName, HotelRobotConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct);
}

public class HotelsRobotsProducer : IHotelsRobotsProducer
{
    private readonly IQueueElementPublisherBuilder<HotelsApiQueueElement> _publisherBuilder;
    private readonly IHotelsApiQueueElementsFactory _queueElementsFactory;

    public HotelsRobotsProducer(IQueueElementPublisherBuilder<HotelsApiQueueElement> publisherBuilder, IHotelsApiQueueElementsFactory queueElementsFactory)
    {
        _publisherBuilder = publisherBuilder;
        _queueElementsFactory = queueElementsFactory;
    }

    [DisplayName("[HotelsApiRobot] {0}")]
    public async Task Publish(string jobName, HotelRobotConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct)
    {
        var elements = _queueElementsFactory.CreateFromConfiguration(config);
        var elementsCount = elements.Count();

        var publisher = _publisherBuilder
            .WithEvenlyDistribution(elementsCount)
            .WithCron(cronExpression)
            .WithJobName($"[HotelsApiRobot] {jobName}")
            .WithLogger(context.WriteLine)
            .Build();
        await publisher.Publish(elements, ct);
        await publisher.Complete(ct);
    }
}
