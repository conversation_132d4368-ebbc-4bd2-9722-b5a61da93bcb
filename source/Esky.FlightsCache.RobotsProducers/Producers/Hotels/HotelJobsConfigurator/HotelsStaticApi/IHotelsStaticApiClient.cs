using Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator.HotelsStaticApi.Contract;
using Refit;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator.HotelsStaticApi;

public interface IHotelsStaticApiClient
{
    [Get("/api/v2.0/Hotels/Providers")]
    Task<HotelsMappingDataResponse> GetHotelsMappingData([Query] string tag, CancellationToken ct);
}