using Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Hangfire.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

public interface IHotelJobsConfigurator
{
    Task ConfigureRobots(PerformContext context, CancellationToken ct);
}

public class HotelJobsConfigurator(
    IHotelInventoryRobotsConfigFeedRepository repository,
    IRecurringJobManager recurringJobManager) : IHotelJobsConfigurator
{
    private const string RobotsJobPrefix = "HCACHE";

    public async Task ConfigureRobots(PerformContext context, CancellationToken ct)
    {
        context.WriteLine("Starting hotel robots configuration from BigQuery data...");
        var parsed = await repository.GetData(context, ct);
        if (parsed.Count is 0)
        {
            context.WriteLine("No data found. Exiting configuration.");
            throw new InvalidOperationException("No data found for hotel robots configuration.");
        }

        await RemoveAllHCacheJobs(context);
        await ProcessAndCreateJobs(parsed, context, ct);
    }

    private Task RemoveAllHCacheJobs(PerformContext context)
    {
        context.WriteLine("Removing all existing HCACHE jobs...");

        // Get all recurring jobs
        using var connection = JobStorage.Current.GetConnection();
        var recurringJobs = connection.GetRecurringJobs();

        // Filter jobs with HCACHE prefix
        var hcacheJobs = recurringJobs.Where(job => job.Id.StartsWith(RobotsJobPrefix)).ToList();

        context.WriteLine($"Found {hcacheJobs.Count} HCACHE jobs to remove.");

        // Remove each job with progress bar
        var progressBar = context.WriteProgressBar("Remove old jobs...");

        foreach (var job in hcacheJobs.WithProgress(progressBar))
        {
            recurringJobManager.RemoveIfExists(job.Id);
            context.WriteLine($"Removed job: {job.Id}");
        }

        return Task.CompletedTask;
    }

    private Task ProcessAndCreateJobs(List<HotelInventoryRobotsConfigFeedBigRecord> parsed, PerformContext context, CancellationToken _)
    {
        // Configuration values
        context.WriteLine("Setting up configuration values...");
        var minCheckinDaysFromGeneration = 2;
        var maxCheckinDaysFromGeneration = 365;
        var stayLengths = new int[] { 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14 };
        var occupancy = new RoomConfiguration[]
        {
            new(){Adults = 1, ChildAges = [] },
            new(){Adults = 1, ChildAges = [11] },
            new(){Adults = 1, ChildAges = [17] },
            new(){Adults = 2, ChildAges = [] },
            new(){Adults = 2, ChildAges = [1] },
            new(){Adults = 2, ChildAges = [11] },
            new(){Adults = 2, ChildAges = [17] },
            new(){Adults = 2, ChildAges = [11,11] },
            new(){Adults = 2, ChildAges = [11,17] },
            new(){Adults = 2, ChildAges = [17,17] },
            new(){Adults = 3, ChildAges = [] },
            new(){Adults = 4, ChildAges = [] },
        };

        // Process records
        context.WriteLine("Processing records...");
        var records = parsed
            .Where(x => x.TC_Tag || x.Portfolio)
            .SelectMany(x => x.FlatByMarket())
            .SelectMany(x => x.FlatByProviders())
            .GroupBy(RobotGroup.From)
            .ToList();

        context.WriteLine($"Grouped into {records.Count} robot configurations.");

        // Create progress bar for job configuration
        var progressBar = context.WriteProgressBar("Producing robots");

        foreach (var robot in records.WithProgress(progressBar))
        {
            if (!robot.Key.IsValid)
            {
                context.WriteLine(ConsoleTextColor.Red, $"Skipping invalid robot configuration: {robot.Key}");
                continue;
            }

            var freq = robot.Key.Frequency;
            var provider = robot.Key.Provider;
            var partner = robot.Key.Partner;
            var configString = GetProviderConfigurationId(partner, provider, freq);

            if (string.IsNullOrEmpty(configString))
            {
                context.WriteLine($"Skipping provider {provider} for partner {partner} due to missing configuration.");
                continue;
            }

            if (ShouldExclude(provider, freq, partner))
            {
                context.WriteLine($"Excluding provider {provider} with frequency {freq} for partner {partner}.");
                continue;
            }

            var jobName = GetJobName(provider, configString, freq);
            var cron = GetCron(freq);
            var hotelMetaCodes = robot.Select(x => x.HotelMetaCode).Distinct().ToArray();

            context.WriteLine($"Processing job: {jobName} with cron: {cron} and {hotelMetaCodes.Length} hotel meta codes.");

            var config = new HotelRobotFlexConfiguration()
            {
                ProviderCode = (ProviderCodeEnum)provider,
                HotelMetaCodes = hotelMetaCodes,
                MinCheckinDaysFromGeneration = minCheckinDaysFromGeneration,
                MaxCheckinDaysFromGeneration = GetMaxDaysForward(partner, provider, freq) ?? maxCheckinDaysFromGeneration,
                StayLengths = stayLengths,
                Occupancy = occupancy,
                PartnerCode = partner,
                ProviderConfigurationId = configString
            };

            try
            {
                context.WriteLine($"Adding new job: {jobName} with cron: {cron}");
                recurringJobManager.AddOrUpdate<IHotelsRobotsFlexProducer>(
                    jobName,
                    producer => producer.Publish(jobName, config, cron, null, CancellationToken.None),
                    cron,
                    new RecurringJobOptions { TimeZone = TimeZoneInfo.Utc }
                );
                context.WriteLine($"Successfully configured job: {jobName}");
            }
            catch (Exception ex)
            {
                context.WriteLine($"Failed to configure job {jobName}: {ex}");
            }
        }

        return Task.CompletedTask;
    }

    private static string GetJobName(int provider, string configString, int freq) => $"{RobotsJobPrefix}-{provider}-{configString}-{freq}";

    private static bool ShouldExclude(int provider, int freq, string partnerCode) => (provider, freq, partnerCode) switch
    {
        (12, 3, "THOMASCOOKUKPACKAGES") => true,  // Exclude Expedia low sales robots for TC only
        (12, 7, _) => true,  // Expedia
        (122, 7, _) => true, // WebBeds
        (123, 3, "ESKYPLPACKAGES") => true, // Exlude OTS low sales robots for eSky only
        (123, 7, "ESKYPLPACKAGES") => true, // Exlude OTS no sales robots for eSky only
        (124, 3, _) => true, // Alturabeds
        (124, 7, _) => true, // Alturabeds
        (126, 3, _) => true, // Yalago
        (126, 7, _) => true, // Yalago
        (171, 3, _) => true, // TravelgateW2M
        (171, 7, _) => true, // TravelgateW2M
        _ => false
    };

    private static string GetCron(int frequency) => frequency switch
    {
        1 => "0 8 * * *",  // At 8:00 every day
        3 => "0 9 */3 * *",  // At 9:00 every 3 days
        7 => "0 10 * * 3",  // At 10:00 on Wednesday
        _ => throw new ArgumentOutOfRangeException(nameof(frequency), frequency, "Freq must be 1, 3 or 7")
    };

    private static string? GetProviderConfigurationId(string partner, int provider, int frequency) => (partner, provider, frequency) switch
    {
        ("ESKYPLPACKAGES", 12, _) => "epaeac", //Expedia
        ("ESKYPLPACKAGES", 36, _) => "epa", //HotelBeds
        ("ESKYPLPACKAGES", 119, 1) => "plp", //RateHawk
        ("ESKYPLPACKAGES", 119, 3) => "hup", //RateHawk
        ("ESKYPLPACKAGES", 119, 7) => "skp", //RateHawk
        ("ESKYPLPACKAGES", 122, _) => "epa", //WebBeds
        ("ESKYPLPACKAGES", 123, _) => "epa", //OTS
        ("ESKYPLPACKAGES", 124, _) => "esky-package", //Alturabeds
        //("ESKYPLPACKAGES", 126, _) => "TH0", //Yalago
        ("THOMASCOOKUKPACKAGES", 12, _) => "tcpaeac", //Expedia
        ("THOMASCOOKUKPACKAGES", 36, _) => "tcp", //HotelBeds
        //("THOMASCOOKUKPACKAGES", 119, _) => "ukp", //RateHawk
        ("THOMASCOOKUKPACKAGES", 122, _) => "def", //WebBeds
        ("THOMASCOOKUKPACKAGES", 123, _) => "packages", //OTS
        ("THOMASCOOKUKPACKAGES", 124, _) => "packages", //Alturabeds
        ("THOMASCOOKUKPACKAGES", 126, _) => "TPA", //Yalago
        ("THOMASCOOKUKPACKAGES", 171, _) => "w2m", //TravelgateW2M
        _ => null
    };

    // TODO: move configurations to mongo or other storage
    private static int? GetMaxDaysForward(string partner, int provider, int frequency) => (partner, provider, frequency) switch
    {
        ("ESKYPLPACKAGES", 36, 1) => 450, // HotelBeds
        ("THOMASCOOKUKPACKAGES", 36, 1) => 450, // HotelBeds
        _ => null
    };
}