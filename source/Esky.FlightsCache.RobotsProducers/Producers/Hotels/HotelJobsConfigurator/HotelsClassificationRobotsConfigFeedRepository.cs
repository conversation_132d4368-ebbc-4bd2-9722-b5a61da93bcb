using Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator.HotelsStaticApi;
using Esky.Hotels.Contract.Common;
using Hangfire.Console;
using Hangfire.Server;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

public class HotelsClassificationRobotsConfigFeedRepository(IHotelsStaticApiClient client) : IHotelInventoryRobotsConfigFeedRepository
{
    private readonly static Dictionary<string, string> MarketTags = new Dictionary<string, string>()
    {
        ["hc-tc"] = "TC",
        ["hc-esky"] = "ESKY"
    };

    public async Task<List<HotelInventoryRobotsConfigFeedBigRecord>> GetData(PerformContext context, CancellationToken ct)
    {
        var results = new List<HotelInventoryRobotsConfigFeedBigRecord>();
        context.WriteLine("Retrieving data from Hotels Static API...");
        foreach (var marketTag in MarketTags)
        {
            context.WriteLine($"Retrieving data for market tag: {marketTag.Key} ({marketTag.Value})");
            var mappings = await client.GetHotelsMappingData(marketTag.Key, ct);
            if (mappings.HotelsMappingData.Count == 0)
            {
                context.WriteLine($"No data found for market tag: {marketTag.Key}");
                continue;
            }
            context.WriteLine($"Retrieved {mappings.HotelsMappingData.Count} records for market tag: {marketTag.Key}");
            results.AddRange(mappings.HotelsMappingData.Select(x => new HotelInventoryRobotsConfigFeedBigRecord()
            {
                HotelMetaCode = x.HotelMetaCode,
                Providers = string.Join(",", x.Providers.Select(x => ((ProviderCode)x).ToString())),
                Sales = int.MaxValue,
                Market = marketTag.Value,
                TC_Tag = marketTag.Value == "TC",
                Portfolio = marketTag.Value == "ESKY"
            }));
        }
        context.WriteLine($"Retrieved {results.Count} rows from Hotels Static API.");
        return results;
    }
}
