using Esky.FlightsCache.Robots.BigQuery;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.BigQuery.V2;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

public interface IHotelInventoryRobotsConfigFeedRepository
{
    Task<List<HotelInventoryRobotsConfigFeedBigRecord>> GetData(PerformContext context, CancellationToken ct);
}

public class HotelInventoryRobotsConfigFeedRepository : IHotelInventoryRobotsConfigFeedRepository
{
    private const string Query = @"
        SELECT hotel_meta_code, Providers, Sales, Market, TC_Tag, Portfolio   
        FROM `esky-datac-hotels.hotel_mapping.hotel_inventory_robots_config_feed`";

    private readonly Lazy<BigQueryClient> _client;

    public HotelInventoryRobotsConfigFeedRepository(BigQuerySettings settings)
    {
        _client = new Lazy<BigQueryClient>(() =>
        {
            var credentials = File.ReadAllText(settings.GoogleCredentialsPath);
            return BigQueryClient.Create(settings.EskyDatacHotelsProjectId, GoogleCredential.FromJson(credentials));
        });
    }

    public async Task<List<HotelInventoryRobotsConfigFeedBigRecord>> GetData(PerformContext context, CancellationToken ct)
    {
        var results = await GetBigQueryData(context, ct);
        var parsed = ParseRecords(context, results);
        return parsed;
    }

    private async Task<BigQueryResults> GetBigQueryData(PerformContext context, CancellationToken ct)
    {
        var job = await _client.Value.CreateQueryJobAsync(Query, [], cancellationToken: ct);
        context.WriteLine("Retrieving data from BigQuery...");
        var results = await job.GetQueryResultsAsync(cancellationToken: ct);
        return results;
    }

    private static List<HotelInventoryRobotsConfigFeedBigRecord> ParseRecords(PerformContext context, BigQueryResults results)
    {
        var resultsList = results.ToList();
        context.WriteLine($"Retrieved {resultsList.Count} rows from BigQuery.");
        context.WriteLine("Parsing BigQuery results to RobotsConfigFeedRecord model...");
        var parsed = new List<HotelInventoryRobotsConfigFeedBigRecord>();
        var progressBar = context.WriteProgressBar();
        var totalRows = resultsList.Count;
        var currentRow = 0;
        foreach (var row in resultsList)
        {
            parsed.Add(new HotelInventoryRobotsConfigFeedBigRecord
            {
                HotelMetaCode = Convert.ToInt32(row["hotel_meta_code"]),
                Providers = row["Providers"]?.ToString() ?? string.Empty,
                Sales = row["Sales"] == null ? null : Convert.ToInt32(row["Sales"]),
                Market = row["Market"]?.ToString() ?? string.Empty,
                TC_Tag = Convert.ToBoolean(row["TC_Tag"]),
                Portfolio = Convert.ToBoolean(row["Portfolio"])
            });

            currentRow++;
            progressBar?.SetValue((int)((double)currentRow / totalRows * 100));
        }
        context.WriteLine($"Parsed {parsed.Count} records from BigQuery.");
        return parsed;
    }
}
