using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

public record HotelInventoryRobotsConfigFeedBigRecord
{
    public required int HotelMetaCode { get; set; }
    public required string Providers { get; set; }
    public required int? Sales { get; set; }
    public required string Market { get; set; }
    public required bool TC_Tag { get; set; }
    public required bool Portfolio { get; set; }

    public IEnumerable<HotelInventoryRobotsConfigFeedBigRecord> FlatByMarket()
    {
        if (TC_Tag)
            yield return this with { Market = "TC" };
        if (Portfolio)
            yield return this with { Market = "ESKY" };
    }

    public IEnumerable<HotelInventoryRobotsConfigFeedBigRecord> FlatByProviders()
    {
        return Providers.Split(',').Select(x => this with { Providers = x.Trim() });
    }

    public string? GetPartner() => Market.ToUpper() switch
    {
        "ESKY" => "ESKYPLPACKAGES",
        "TC" => "THOMASCOOKUKPACKAGES",
        _ => null
    };

    public int? GetProviderCode() => Providers?.Replace(" ", "").ToUpper() switch
    {
        "BOOKINGCOM" => 3,
        "EXPEDIA" => 12,
        "HOTELBEDS" => 36,
        "TRAVOLUTIONARY" => 63,
        "RATEHAWK" => 119,
        "WEBBEDS" => 122,
        "OTS" => 123,
        "ALTURABEDS" => 124,
        "YALAGO" => 126,
        "TRAVELGATEDODO" => 170,
        "TRAVELGATEW2M" => 171,
        _ => null
    };

    public int? GetFrequency() => Sales switch
    {
        >= 7 => 1,
        >= 1 => 3,
        null or 0 => 7,
        _ => null
    };
}