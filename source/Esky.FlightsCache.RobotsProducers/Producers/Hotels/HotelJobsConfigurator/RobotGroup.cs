using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.HotelJobsConfigurator;

public record RobotGroup
{
    private RobotGroup(HotelInventoryRobotsConfigFeedBigRecord record)
    {
        Partner = record.GetPartner() ?? string.Empty;
        Provider = record.GetProviderCode() ?? 0;
        Frequency = record.GetFrequency() ?? 0;
        IsValid = !string.IsNullOrWhiteSpace(Partner) && Provider > 0 && Frequency > 0;
    }

    public bool IsValid { get; }

    public string Partner { get; }
    public int Provider { get; }
    public int Frequency { get; }

    public static RobotGroup From(HotelInventoryRobotsConfigFeedBigRecord record)
    {
        return new RobotGroup(record);
    }

    public virtual bool Equals(RobotGroup? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return Partner == other.Partner
               && Provider == other.Provider
               && Frequency == other.Frequency;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Partner, Provider, Frequency);
    }
}