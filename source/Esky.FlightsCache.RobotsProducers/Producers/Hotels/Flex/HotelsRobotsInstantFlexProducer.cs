using Esky.FlightsCache.Robots.Messages;
using Hangfire.Console;
using Hangfire.Server;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;

public interface IHotelsRobotsInstantFlexProducer
{
    Task Publish(string jobName, HotelRobotFlexConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct);
}

public class HotelsRobotsInstantFlexProducer(
    IQueuePublisher queuePublisher,
    IHotelsApiQueueElementsFactory queueElementsFactory) : IHotelsRobotsInstantFlexProducer
{
    [DisplayName("[HotelsApiInstantFlexRobot] {0}")]
    public async Task Publish(string jobName, HotelRobotFlexConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct)
    {
        var elements = queueElementsFactory.CreateFromConfiguration(config);
        var elementsCount = elements.Count();
        context?.WriteLine($"Publishing {elementsCount} elements to Consumer's queue started for type {nameof(HotelsApiQueueElement)}");
        await queuePublisher.Publish(elements, ct);
        context?.WriteLine($"Publishing {elementsCount} elements to Consumer's queue done");
    }
}
