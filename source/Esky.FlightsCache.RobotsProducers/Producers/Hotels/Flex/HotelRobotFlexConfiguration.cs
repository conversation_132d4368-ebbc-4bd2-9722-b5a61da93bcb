using Esky.Framework.PartnerSettings.Enums;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;

public record HotelRobotFlexConfiguration
{
    public ProviderCodeEnum ProviderCode { get; set; }
    public string? ProviderConfigurationId { get; set; }
    public required int[] HotelMetaCodes { get; set; }
    public required int MinCheckinDaysFromGeneration { get; set; }
    public required int MaxCheckinDaysFromGeneration { get; set; }
    public required int[] StayLengths { get; set; }
    public required RoomConfiguration[] Occupancy { get; set; }
    public required string PartnerCode { get; set; }
}