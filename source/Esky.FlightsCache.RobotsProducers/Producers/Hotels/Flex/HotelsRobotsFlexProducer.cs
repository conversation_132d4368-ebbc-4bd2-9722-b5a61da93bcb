using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Hangfire.Console;
using Hangfire.Server;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;

public interface IHotelsRobotsFlexProducer
{
    Task Publish(string jobName, HotelRobotFlexConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct);
}

public class HotelsRobotsFlexProducer(
    IQueueElementPublisherBuilder<HotelsApiQueueElement> publisherBuilder,
    IHotelsApiQueueElementsFactory queueElementsFactory) : IHotelsRobotsFlexProducer
{

    [DisplayName("[HotelsApiFlexRobot] {0}")]
    public async Task Publish(string jobName, HotelRobotFlexConfiguration config, string cronExpression, PerformContext? context, CancellationToken ct)
    {
        var elements = queueElementsFactory.CreateFromConfiguration(config);
        var elementsCount = elements.Count();
        var publisher = publisherBuilder
            .WithEvenlyDistribution(elementsCount)
            .WithCron(cronExpression)
            .WithJobName($"[HotelsApiFlexRobot] {jobName}")
            .WithLogger(context.WriteLine)
            .Build();
        await publisher.Publish(elements, ct);
        await publisher.Complete(ct);

        Metrics.SetHotelJobElementsCount(jobName, elementsCount);
    }
}