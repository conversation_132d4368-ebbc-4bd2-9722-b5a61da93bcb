using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.Hotels.Flex;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.Hotels;

public interface IHotelsApiQueueElementsFactory
{
    IEnumerable<HotelsApiQueueElement> CreateFromConfiguration(HotelRobotFlexConfiguration config);
    IEnumerable<HotelsApiQueueElement> CreateFromConfiguration(HotelRobotConfiguration config);
}

public class HotelsApiQueueElementsFactory : IHotelsApiQueueElementsFactory
{
    private const string SourceName = "HotelsApiRobots";
    private const int MaxHotelsInRequest = 1000;
    public IEnumerable<HotelsApiQueueElement> CreateFromConfiguration(HotelRobotConfiguration config)
    {
        var checkInDateRange = Enumerable.Range(0, config.CheckInDateTo.DayNumber - config.CheckInDateFrom.DayNumber + 1).Select(i => config.CheckInDateFrom.AddDays(i));
        var stayLengthRange = Enumerable.Range(config.StayLengthFrom, config.StayLengthTo - config.StayLengthFrom + 1);
        var numberOfChunks = (int)Math.Ceiling((double)config.HotelMetaCodes.Length / MaxHotelsInRequest);
        var sizeOfChunk = (int)Math.Ceiling((double)config.HotelMetaCodes.Length / numberOfChunks);
        var metaCodesChunks = config.HotelMetaCodes.Chunk(sizeOfChunk);
        var elements = from checkInDate in checkInDateRange
                       from stayLength in stayLengthRange
                       from roomConfiguration in config.Occupancy
                       from metaCodesChunk in metaCodesChunks
                       select new HotelsApiQueueElement
                       {
                           ProviderCode = config.ProviderCode,
                           HotelMetaCodes = metaCodesChunk.ToArray(),
                           CheckInDate = checkInDate,
                           CheckOutDate = checkInDate.AddDays(stayLength),
                           Adults = roomConfiguration.Adults,
                           ChildrenAges = roomConfiguration.ChildAges,
                           PartnerCode = config.PartnerCode,
                           ProviderConfigurationId = config.ProviderConfigurationId,
                           SourceName = SourceName
                       };
        return elements;
    }

    public IEnumerable<HotelsApiQueueElement> CreateFromConfiguration(HotelRobotFlexConfiguration config)
    {
        var now = DateTime.UtcNow;
        var checkInDateFrom = DateOnly.FromDateTime(now.AddDays(config.MinCheckinDaysFromGeneration));
        var checkInDateTo = DateOnly.FromDateTime(now.AddDays(config.MaxCheckinDaysFromGeneration));

        var checkInRange = Enumerable
            .Range(0, checkInDateTo.DayNumber - checkInDateFrom.DayNumber + 1)
            .Select(i => checkInDateFrom.AddDays(i));

        var byCheckIn =
            from checkIn in checkInRange
            from stayLength in config.StayLengths
            select new HotelRobotConfiguration{
                ProviderCode = config.ProviderCode,
                HotelMetaCodes = config.HotelMetaCodes,
                CheckInDateFrom = checkIn,
                CheckInDateTo = checkIn,
                StayLengthFrom = stayLength,
                StayLengthTo = stayLength,
                Occupancy = config.Occupancy,
                PartnerCode = config.PartnerCode,
                ProviderConfigurationId = config.ProviderConfigurationId
            };

        return byCheckIn.SelectMany(CreateFromConfiguration);
    }
}
