using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public static class CacheDemandJobValidationExtensions
    {
        public static void ValidateLimits(this CacheDemandJob job, CacheDemandSettings cacheDemandSettings)
        {
            bool isValid = true;

            foreach (var item in job.Items)
            {
                if (item.RequestsGenerated > cacheDemandSettings.SingleLineRequestsLimit)
                {
                    item.HasErrors = true;
                    item.ErrorMessage = $"Single line requests limit exceeded {item.RequestsGenerated} / {cacheDemandSettings.SingleLineRequestsLimit}";
                }
            }

            if (job.TotalRequestsGenerated > cacheDemandSettings.TotalRequestsLimit)
            {
                job.ErrorMessage = $"Operation aborted. Total number of generated requests: {job.TotalRequestsGenerated} exceed total request limit: {cacheDemandSettings.TotalRequestsLimit}.";
                isValid = false;
            }

            if (isValid)
            {
                var lines = job.Items.Count(o => o.HasErrors);

                if (lines > 0)
                {
                    job.ErrorMessage = $"Operation aborted. Number of generated requests in {lines} lines exceed single line request limit: {cacheDemandSettings.SingleLineRequestsLimit}.";
                    isValid = false;
                }
            }

            job.IsValid = isValid;
        }

        public static async Task ValidatePartnerCodes(this CacheDemandJob job, IPartnerSettingsService partnerSettingsService)
        {
            var partnerCodes = job.Items.Select(x => x.InputModel.PartnerCode).ToHashSet();

            foreach (var partnerCode in partnerCodes)
            {
                var ps = await partnerSettingsService.GetPartnerSettingsAsync(partnerCode);
                if (ps is not null) continue;

                job.ErrorMessage = string.Join(Environment.NewLine, job.ErrorMessage, $"Invalid partnerCode: {partnerCode}").Trim();
                job.IsValid = false;
                break;
            }
        }

        public static async Task ValidateAmadeusLiveCheck(this CacheDemandJob job, IPartnerSettingsService partnerSettingsService)
        {
            var hasAlcJobsWithoutOfficeId = job.Items.Any(x => x.InputModel.ProviderCode is null && string.IsNullOrEmpty(x.InputModel.OfficeId));

            if (hasAlcJobsWithoutOfficeId)
            {
                job.ErrorMessage = string.Join(Environment.NewLine, job.ErrorMessage, "Missing office id for rows Amadeus Live Check").Trim();
                job.IsValid = false;
            }

            var officeIds = job.Items
                .Where(e => e.InputModel.ProviderCode is null && !string.IsNullOrWhiteSpace(e.InputModel.OfficeId))
                .Select(e => e.InputModel.OfficeId)
                .Distinct();

            foreach (var officeId in officeIds)
            {
                var officeSettings = await partnerSettingsService.GetOfficeSettingsAsync(officeId);
                if (officeSettings is null)
                {
                    job.ErrorMessage = string.Join(Environment.NewLine, job.ErrorMessage, $"Unknown office id {officeId} for rows Amadeus Live Check").Trim();
                    job.IsValid = false;
                }
            }
        }
    }
}
