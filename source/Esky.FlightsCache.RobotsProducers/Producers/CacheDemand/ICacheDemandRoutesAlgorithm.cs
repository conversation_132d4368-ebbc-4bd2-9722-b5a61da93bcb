using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public interface ICacheDemandRoutesAlgorithm
    {
        IEnumerable<T> GenerateQueueElements<T>(CacheDemandJob job) where T : QueueElement, new();
        IEnumerable<T> GenerateQueueElements<T>(CacheDemandModelInput input, string sourceName) where T : QueueElement, new();
    }
}