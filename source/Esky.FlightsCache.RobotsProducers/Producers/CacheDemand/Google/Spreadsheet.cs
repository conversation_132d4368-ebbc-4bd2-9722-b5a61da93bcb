using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google
{
    public record Spreadsheet
    {
        public required string Id { get; init; }
        public required string Name { get; init; }
        public required string Directory { get; init; }
        public DateTime? ModifiedTime { get; init; }
        public string? LastModifyingUser { get; init; }
        public IEnumerable<Sheet> Sheets { get; set; } = [];
    }

    public class Sheet
    {
        public int? Id { get; init; }
        public string? Name { get; init; }
        public DateTime? LastSuccessfulValidationDate { get; init; }
    }
}