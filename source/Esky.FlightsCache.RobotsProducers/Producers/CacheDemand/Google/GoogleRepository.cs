using Esky.FlightsCache.RobotsProducers.Configuration;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using File = Google.Apis.Drive.v3.Data.File;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google
{
    public interface IGoogleRepository
    {
        Task<IEnumerable<Spreadsheet>> GetSpreadsheets();
        Task<IList<IList<object>>> GetValues(string spreadsheetId, string sheetName);
        Task SetLastSuccessfulValidationDate(string spreadsheetId, string sheetName, string value);
        Task SetTotalRequestsGenerated(string spreadsheetId, string sheetName, int value);
        Task SetRowRequestsGenerated(string spreadsheetId, string sheetName, IEnumerable<int> values, int? rowRequestsGeneratedColumn = null);
        Task SetValues(string spreadsheetId, string sheetName, int column, int rowNumber, IEnumerable<int> values);
    }

    public class GoogleRepository : IGoogleRepository
    {
        private const string _lastSuccessfulValidationDateCell = "H1";
        private const string _totalRequestsGeneratedCell = "J1";
        private const char _rowRequestsGeneratedDefaultColumn = 'K';
        private const int _rowRequestsGeneratedStartRow = 3;
        private const string _allCellsRange = "A:Z";
        private const string _applicationName = "Esky.FlightsCache.RobotsProducers";

        private readonly DriveService _driveService;
        private readonly SheetsService _sheetsService;
        private readonly CacheDemandSettings _settings;

        public GoogleRepository(CacheDemandSettings settings)
        {
            var stream = System.IO.File.ReadAllText(settings.GoogleCredentialsPath);
            var credentials = GoogleCredential.FromJson(stream).CreateScoped(DriveService.ScopeConstants.Drive);
            
            _settings = settings;
            
            _driveService = new DriveService(new BaseClientService.Initializer
            {
                HttpClientInitializer = credentials, ApplicationName = _applicationName
            });
            
            _sheetsService = new SheetsService(new BaseClientService.Initializer
            {
                HttpClientInitializer = credentials, ApplicationName = _applicationName
            });
        }

        public async Task<IEnumerable<Spreadsheet>> GetSpreadsheets()
        {
            var driveDirectories = await GetDirectories();
            var spreadsheetDetailsTasks = driveDirectories.Select(async directory =>
            {
                var result =
                    await CreateFilesListRequest(
                            $"parents in '{directory.Id}' and mimeType = 'application/vnd.google-apps.spreadsheet'")
                        .ExecuteAsync();

                var spreadsheetTasks = result.Files
                    .Select(async x =>
                        new Spreadsheet
                        {
                            Id = x.Id,
                            Name = x.Name,
                            Directory = directory.Name,
                            ModifiedTime = x.ModifiedTimeDateTimeOffset?.DateTime,
                            LastModifyingUser = x.LastModifyingUser?.DisplayName,
                            Sheets = await GetSheets(x.Id)
                        });
                
                return await Task.WhenAll(spreadsheetTasks);
            });

            return (await Task.WhenAll(spreadsheetDetailsTasks)).SelectMany(x => x);
        }

        public async Task<IList<IList<object>>> GetValues(string spreadsheetId, string sheetName)
        {
            var valueRange = await _sheetsService.Spreadsheets.Values.Get(spreadsheetId, $"{sheetName}!{_allCellsRange}")
                .ExecuteAsync();
            return valueRange.Values;
        }

        public async Task SetLastSuccessfulValidationDate(string spreadsheetId, string sheetName, string value)
        {
            await SetValueInCell(spreadsheetId, sheetName, _lastSuccessfulValidationDateCell, new List<IList<object>> { new List<object> { value } });
        }

        public async Task SetTotalRequestsGenerated(string spreadsheetId, string sheetName, int value)
        {
            await SetValueInCell(spreadsheetId, sheetName, _totalRequestsGeneratedCell, new List<IList<object>> { new List<object> { value } });
        }

        public async Task SetRowRequestsGenerated(string spreadsheetId, string sheetName, IEnumerable<int> values, int? rowRequestsGeneratedColumn = null)
        {
            var rowRequestsGeneratedCell = $"{(char?)('A' + rowRequestsGeneratedColumn) ?? _rowRequestsGeneratedDefaultColumn}{_rowRequestsGeneratedStartRow}";
            await SetValueInCell(spreadsheetId, sheetName, rowRequestsGeneratedCell,
                values.Select(x => (IList<object>)new List<object> { x }).ToList());
        }
        
        public async Task SetValues(string spreadsheetId, string sheetName, int column, int rowNumber, IEnumerable<int> values)
        {
            var cell = $"{(char?)('A' + column)}{rowNumber}";
            await SetValueInCell(spreadsheetId, sheetName, cell, values.Select(x => (IList<object>)new List<object> { x }).ToList());
        }

        private async Task<IEnumerable<Sheet>> GetSheets(string spreadsheetId)
        {
            var getSpreadsheetRequest = _sheetsService.Spreadsheets.Get(spreadsheetId);
            getSpreadsheetRequest.Fields = "sheets.properties(title,sheetId)";
            var spreadsheet = await getSpreadsheetRequest.ExecuteAsync();

            var getValuesRequest = _sheetsService.Spreadsheets.Values.BatchGet(spreadsheetId);
            getValuesRequest.Ranges = spreadsheet.Sheets.Select(x => $"{x.Properties.Title}!{_lastSuccessfulValidationDateCell}").ToList();
            var response = await getValuesRequest.ExecuteAsync();

            var values = response.ValueRanges.Select(valueRange =>
            {
                DateTime? lastSuccessfulValidationDate = null;
                if (valueRange.Values != null && DateTime.TryParse(valueRange.Values[0][0].ToString(), out var dt))
                {
                    lastSuccessfulValidationDate = dt;
                }
                return lastSuccessfulValidationDate;
            }).ToList();

            return spreadsheet.Sheets.Zip(values,
                (sheet, cellValue) => new Sheet
                {
                    Id = sheet.Properties.SheetId,
                    Name = sheet.Properties.Title,
                    LastSuccessfulValidationDate = cellValue
                });
        }

        private async Task SetValueInCell(string spreadsheetId, string sheetName, string cell,
            IList<IList<object>> values)
        {
            var update = _sheetsService.Spreadsheets.Values.Update(
                new ValueRange { Values = values },
                spreadsheetId, $"{sheetName}!{cell}");

            update.ValueInputOption =
                SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.USERENTERED;

            await update.ExecuteAsync();
        }

        private async Task<IEnumerable<File>> GetDirectories()
        {
            var result =
                await CreateFilesListRequest("mimeType = 'application/vnd.google-apps.folder'").ExecuteAsync();
            return result.Files;
        }

        private FilesResource.ListRequest CreateFilesListRequest(string q)
        {
            var request = _driveService.Files.List();
            request.IncludeItemsFromAllDrives = true;
            request.SupportsAllDrives = true;
            request.DriveId = _settings.GoogleDirectoryId;
            request.Corpora = "drive";
            request.Fields = "*";
            request.Q = q + " and trashed = false";

            return request;
        }
    }
}