using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{

    public class CacheDemandJobRepository : ICacheDemandJobRepository
    {
        private readonly IMongoCollection<CacheDemandJob> _jobs;
        
        public CacheDemandJobRepository(DatabaseSettings databaseSettings)
        {
            var mongoClient = new MongoClient(databaseSettings.ConnectionString);
            _jobs = mongoClient.GetDatabase(databaseSettings.DatabaseName).GetCollection<CacheDemandJob>("CacheDemandJobs");

            _jobs.Indexes.CreateOne(
                new CreateIndexModel<CacheDemandJob>(
                    Builders<CacheDemandJob>.IndexKeys.Ascending(x => x.JobId),
                    new CreateIndexOptions { Unique = true, Name = "JobId_IX" }
            ));

            _jobs.Indexes.CreateOne(
                new CreateIndexModel<CacheDemandJob>(
                    Builders<CacheDemandJob>.IndexKeys.Descending(x => x.CreatedUtc),
                    new CreateIndexOptions { ExpireAfter = TimeSpan.FromDays(30), Name = "TTL" }
            ));
        }

        public async Task Save(CacheDemandJob job)
        {
            await _jobs.InsertOneAsync(job);
        }

        public async Task<IList<CacheDemandJob>> GetList(int limit)
        {
            return await _jobs.AsQueryable()
                .OrderByDescending(x => x.CreatedUtc)
                .Take(limit)
                .ToListAsync();
        }
    }
}
