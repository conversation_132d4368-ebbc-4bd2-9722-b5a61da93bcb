using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public static class CacheDemandHelpers
{
    public static string CreateSourceName(CacheDemandJob job)
    {
        return CreateSourceName(job.Name ?? "");
    }

    public static string CreateSourceName(Spreadsheet spreadsheet, string sheetName)
    {
        return CreateSourceName(CreateJobName(spreadsheet, sheetName));
    }

    public static string CreateJobName(Spreadsheet spreadsheet, string sheetName)
    {
        return $"{spreadsheet.Directory}|{spreadsheet.Name}|{sheetName}";
    }
    
    private static string CreateSourceName(string jobName)
    {
        return $"CacheDemand_{jobName.Replace(" ", "_")}";
    }

    public static IList<IList<object>> GetContent(this IList<IList<object>> rows)
    {
        return rows.Skip(2).ToList();
    }
    
    public static IList<string> GetHeader(this IList<IList<object>> rows)
    {
        return rows[1].Cast<string>().ToList();
    }
}