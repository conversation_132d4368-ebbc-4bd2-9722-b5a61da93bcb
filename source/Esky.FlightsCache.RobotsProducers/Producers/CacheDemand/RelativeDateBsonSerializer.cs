using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public class RelativeDateBsonSerializer : SerializerBase<RelativeDate>
{
    public static readonly RelativeDateBsonSerializer Instance = new();
    private RelativeDateBsonSerializer() { }
    public override RelativeDate Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        switch (context.Reader.CurrentBsonType)
        {
            case BsonType.DateTime:
                var unixTimeMs = context.Reader.ReadDateTime();
                var dto = DateTimeOffset.FromUnixTimeMilliseconds(unixTimeMs);
                return new RelativeDate(dto.DateTime);
            case BsonType.Int32:
                var relative = context.Reader.ReadInt32();
                return new RelativeDate(relative);
            default:
                var str = context.Reader.ReadString();
                return RelativeDate.Parse(str);
        }
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, RelativeDate value)
    {
        if (value.IsAbsolute(out var date))
        {
            var dto = new DateTimeOffset(date.Value, TimeSpan.Zero);
            var unixTimeMs = dto.ToUnixTimeMilliseconds();
            context.Writer.WriteDateTime(unixTimeMs);
        }
        else
        {
            context.Writer.WriteInt32(value.RelativeDays);
        }
    }
}