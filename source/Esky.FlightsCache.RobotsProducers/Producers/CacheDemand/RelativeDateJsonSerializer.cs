using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Newtonsoft.Json;
using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public class RelativeDateJsonSerializer : JsonConverter<RelativeDate>
{
    public override void Write<PERSON><PERSON>(JsonWriter writer, RelativeDate value, JsonSerializer serializer)
    {
        if (value.IsAbsolute(out var absoluteDate))
        {
            writer.WriteValue(absoluteDate.Value);
        }
        else
        {
            writer.WriteValue(value.RelativeDays);
        }
    }

    public override RelativeDate ReadJson(
        JsonReader reader,
        Type objectType,
        RelativeDate existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        var text = reader.Value?.ToString();
        return RelativeDate.Parse(text);
    }
}