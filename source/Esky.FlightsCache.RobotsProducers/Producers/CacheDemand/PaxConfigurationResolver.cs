using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public interface IPaxConfigurationResolver
{
    string GetPaxConfiguration(CacheDemandModelInput input);
}

public class DefaultPaxConfigurationResolver : IPaxConfigurationResolver
{
    public string GetPaxConfiguration(CacheDemandModelInput input)
    {
        return PaxConfigurations.AdultChildInfant;
    }
}

public class LatamNdcPaxConfigurationResolver : IPaxConfigurationResolver
{
    private readonly IPaxConfigurationResolver _next;

    public LatamNdcPaxConfigurationResolver(IPaxConfigurationResolver next)
    {
        _next = next;
    }

    public string GetPaxConfiguration(CacheDemandModelInput input)
    {
        return input.ProviderCode == Esky.Framework.PartnerSettings.Enums.ProviderCodeEnum.LatamNdc
            ? PaxConfigurations.TwoAdults
            : _next.GetPaxConfiguration(input);        
    }
}

public class MESKYPaxConfigurationResolver : IPaxConfigurationResolver
{
    private readonly IPaxConfigurationResolver _next;

    public MESKYPaxConfigurationResolver(IPaxConfigurationResolver next)
    {
        _next = next;
    }

    public string GetPaxConfiguration(CacheDemandModelInput input)
    {
        return input.PartnerCode == "M-ESKY"
            ? PaxConfigurations.AdultChild
            : _next.GetPaxConfiguration(input);        
    }
}

public class ESKYRO1LAYOUTPaxConfigurationResolver : IPaxConfigurationResolver
{
    private readonly IPaxConfigurationResolver _next;

    public ESKYRO1LAYOUTPaxConfigurationResolver(IPaxConfigurationResolver next)
    {
        _next = next;
    }

    public string GetPaxConfiguration(CacheDemandModelInput input)
    {
        return input.PartnerCode == "ESKYRO1LAYOUT"
            ? PaxConfigurations.TwoAdults
            : _next.GetPaxConfiguration(input);        
    }
}