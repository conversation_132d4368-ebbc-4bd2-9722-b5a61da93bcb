using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Configuration;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Hangfire.Console;
using Hangfire.Server;
using Microsoft.Extensions.Logging;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public interface ICacheDemandProducer
{
    Task<CacheDemandJob> CreateAndValidateJob(string content, string? header, string? name = null);
    Task<CacheDemandJob> DryRun(string content, string? header, bool validateLimits = true);
}

public class CacheDemandProducer : ICacheDemandProducer
{
    private readonly IQueueElementPublisherBuilder<CacheDemandQueueElement> _cacheDemandPublisherBuilder;
    private readonly IQueueElementPublisherBuilder<AmadeusLiveCheckQueueElement> _amadeusLiveCheckPublisherBuilder;
    private readonly ICacheDemandRoutesAlgorithm _algorithm;
    private readonly ICacheDemandInputMapper _cacheDemandInputMapper;
    private readonly CacheDemandSettings _cacheDemandSettings;
    private readonly ICacheDemandJobRepository _jobRepository;
    private readonly IPartnerSettingsService _partnerSettingsService;
    private readonly ILogger<CacheDemandProducer> _logger;

    public CacheDemandProducer(
        IQueueElementPublisherBuilder<CacheDemandQueueElement> cacheDemandPublisherBuilder,
        IQueueElementPublisherBuilder<AmadeusLiveCheckQueueElement> amadeusLiveCheckPublisherBuilder,
        ICacheDemandRoutesAlgorithm algorithm,
        ICacheDemandInputMapper cacheDemandInputMapper,
        CacheDemandSettings cacheDemandSettings,
        ICacheDemandJobRepository jobRepository,
        IPartnerSettingsService partnerSettingsService,
        ILogger<CacheDemandProducer> logger)
    {
        _cacheDemandPublisherBuilder = cacheDemandPublisherBuilder;
        _amadeusLiveCheckPublisherBuilder = amadeusLiveCheckPublisherBuilder;
        _algorithm = algorithm;
        _cacheDemandInputMapper = cacheDemandInputMapper;
        _cacheDemandSettings = cacheDemandSettings;
        _jobRepository = jobRepository;
        _partnerSettingsService = partnerSettingsService;
        _logger = logger;
    }

    [DisplayName("[CacheDemand] {0}")]
    public async Task<CacheDemandJob> Generate(CacheDemandJobConfiguration config, PerformContext? context)
    {
        var now = DateTimeOffset.UtcNow;

        if (!config.IsTimeToExecute(now))
        {
            return new CacheDemandJob { IsValid = false, ErrorMessage = "It is not the time to run job" };
        }

        var job = await CreateAndValidateJob(config.Content, config.Header, config.Name);

        if (job.IsValid)
        {
            await _jobRepository.Save(job);

            var cacheDemand = job.Get(JobType.CacheDemand);
            var cacheDemandElementsCount = cacheDemand.TotalElementsGenerated;
            if (cacheDemandElementsCount > 0)
            {
                var cacheDemandPublisher = _cacheDemandPublisherBuilder
                    .WithEvenlyDistribution(cacheDemandElementsCount)
                    .WithCron(config.CronExpression)
                    .WithJobName($"[CacheDemand] {config.Name}")
                    .WithLogger(context.WriteLine)
                    .Build();
                await cacheDemandPublisher.Publish(_algorithm.GenerateQueueElements<CacheDemandQueueElement>(cacheDemand), CancellationToken.None);
                await cacheDemandPublisher.Complete(CancellationToken.None);
            }

            var alc = job.Get(JobType.AmadeusLiveCheck);
            var amadeusLiveCheckElementsCount = alc.TotalElementsGenerated;
            if (amadeusLiveCheckElementsCount > 0)
            {
                var amadeusLiveCheckPublisher = _amadeusLiveCheckPublisherBuilder
                    .WithEvenlyDistribution(amadeusLiveCheckElementsCount)
                    .WithCron(config.CronExpression)
                    .WithJobName($"[CacheDemand] {config.Name}")
                    .WithLogger(context.WriteLine)
                    .Build();

                await amadeusLiveCheckPublisher.Publish(_algorithm.GenerateQueueElements<AmadeusLiveCheckQueueElement>(alc), CancellationToken.None);
                await amadeusLiveCheckPublisher.Complete(CancellationToken.None);
            }
        }

        return job;
    }

    public async Task<CacheDemandJob> CreateAndValidateJob(string content, string? header, string? name = null)
    {
        try
        {
            var columnMapping = ColumnMapping.Parse(header);
            var inputItems = _cacheDemandInputMapper.Parse(content, columnMapping, RobotConstants.PARTNER_CODE) ?? throw new ArgumentException("No content provided.");

            var job = new CacheDemandJob
            {
                Items = inputItems.ToJobItems(_algorithm).ToList(),
                Name = name,
                RequestsGeneratedColumnIndex = columnMapping.Requests
            };

            await job.ValidatePartnerCodes(_partnerSettingsService);
            await job.ValidateAmadeusLiveCheck(_partnerSettingsService);

            return job;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "CacheDemand ValidateJob has failed");
            return CacheDemandJob.FailedJob(name, ex);
        }
    }

    public async Task<CacheDemandJob> DryRun(string content, string? header, bool validateLimits = true)
    {
        var job = await CreateAndValidateJob(content, header);
        if (validateLimits) job.ValidateLimits(_cacheDemandSettings);
        return job;
    }
}