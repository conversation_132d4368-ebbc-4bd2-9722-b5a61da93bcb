using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    public class CacheDemandSheetJob
    {
        public string? DirectoryName { get; set; }
        public string? SpreadsheetName { get; set; }
        public string? SheetName { get; set; }
        public string? SpreadsheetId { get; set; }
        public string? LastModifyingUser { get; set; }
        public DateTime? ModifiedTime { get; set; }
        public string? JobId { get; set; }
        public string? JobName { get; set; }
        public DateTime? JobCreatedAt { get; set; }
        public DateTimeOffset? JobStartDate { get; set; }
        public DateTimeOffset? JobEndDate { get; set; }
        public string? JobCronExpression { get; set; }
        public int? SheetId { get; set; }
        public DateTime? LastSuccessfulValidationDate { get; set; }
        public string? RemovalJobId { get; set; }
    }
}