using MongoDB.Bson.Serialization.Attributes;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    [BsonIgnoreExtraElements]
    public class CacheDemandJobItem
    {
        public required CacheDemandModelInput InputModel { get; set; }
        public int RequestsGenerated { get; set; }
        public int ElementsGenerated { get; set; }
        public bool HasErrors { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
