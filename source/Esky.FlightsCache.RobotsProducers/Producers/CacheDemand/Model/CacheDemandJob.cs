using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    [BsonIgnoreExtraElements]
    public record CacheDemandJob
    {
        public IList<CacheDemandJobItem> Items { get; set; } = new List<CacheDemandJobItem>();

        public Guid JobId { get; set; } = Guid.NewGuid();

        public DateTime CreatedUtc { get; set; } = DateTime.UtcNow;

        public int TotalRequestsGenerated => Items.Sum(o => o.RequestsGenerated);
        public int TotalElementsGenerated => Items.Sum(o => o.ElementsGenerated);

        public bool IsValid { get; set; } = true;

        public string? ErrorMessage { get; set; }

        public string? Name { get; set; }

        public int RequestsGeneratedColumnIndex { get; set; }

        internal static CacheDemandJob FailedJob(string? name, Exception ex) => new()
        {
            Name = name,
            IsValid = false,
            ErrorMessage = ex.Message
        };
    }
}
