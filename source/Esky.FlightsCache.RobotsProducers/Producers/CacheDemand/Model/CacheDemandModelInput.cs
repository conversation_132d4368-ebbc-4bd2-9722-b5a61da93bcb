using Esky.Framework.PartnerSettings.Enums;
using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    [BsonIgnoreExtraElements]
    public record CacheDemandModelInput
    {
        public IList<string> AirlinesFilter { get; set; } = [];
        public ProviderCodeEnum? ProviderCode { get; set; }
        public required string PartnerCode { get; set; }
        public required string DepartureAirportCode { get; set; }
        public required string ArrivalAirportCode { get; set; }
        public required RelativeDate DepartureDateFrom { get; set; }
        public required RelativeDate DepartureDateTo { get; set; }
        public bool IsRoundTrip { get; set; }
        public int? MinStayLength { get; set; }
        public int? MaxStayLength { get; set; }
        public string? OfficeId { get; set; }
        public string[]? AlcExcludedAirlines { get; set; }
        public JobType? Type { get; set; } = JobType.CacheDemand;
    }

    public enum JobType
    {
        CacheDemand = 0,
        AmadeusLiveCheck = 1
    }
}
