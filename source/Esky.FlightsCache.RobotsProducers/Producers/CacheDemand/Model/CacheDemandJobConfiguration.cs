using System;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    public class CacheDemandJobConfiguration
    {
        public required string Name { get; set; }
        public required string Header { get; set; }
        public required string Content { get; set; }
        public required string CronExpression { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? EndDate { get; set; }

        public string? RemovalJobId { get; set; }

        public bool IsTimeToExecute(DateTimeOffset? now = null)
        {
            now ??= DateTimeOffset.UtcNow;

            return (!StartDate.HasValue || StartDate < now)
                   && (!EndDate.HasValue || now < EndDate);
        }

        public override string ToString() => Name;
    }
}
