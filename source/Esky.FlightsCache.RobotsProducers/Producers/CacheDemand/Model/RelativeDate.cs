using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;

[DebuggerDisplay("""{GetDate().ToString("yyyy-MM-dd")}""")]
public readonly record struct RelativeDate : IParsable<RelativeDate>
{
    private DateTime? RawDate { get; }
    public int RelativeDays { get; }
    public bool IsRelative => !RawDate.HasValue;

    public RelativeDate(DateTime date) => RawDate = date;
    public RelativeDate(int relativeDays) => RelativeDays = relativeDays;

    public bool IsAbsolute([NotNullWhen(true)] out DateTime? date)
    {
        date = RawDate;
        return !IsRelative;
    }

    /// <summary>
    /// Returns date from relative date
    /// </summary>
    /// <param name="relativeTo">if null resolves to current utc date</param>
    /// <returns>calculated date</returns>
    public DateTime GetDate(DateTime? relativeTo = null)
    {
        return RawDate ?? relativeTo?.AddDays(RelativeDays) ?? DateTime.UtcNow.Date.AddDays(RelativeDays);
    }
    
    public static RelativeDate Parse(string? s, IFormatProvider? provider = null)
    {
        if (TryParse(s, provider, out var date))
        {
            return date;
        }
        
        throw new ArgumentException($"Cannot parse value '{s}' as relative date. Provide date or relative days.", nameof(s));
    }

    public static bool TryParse(string? s, IFormatProvider? provider, out RelativeDate result)
    {
        if (int.TryParse(s, provider, out var integer))
        {
            result = new RelativeDate(integer);
            return true;
        }

        if (DateTime.TryParse(s, provider, out var dateTime))
        {
            result = new RelativeDate(dateTime);
            return true;
        }

        result = default;
        return false;
    }

    public override string ToString() => RawDate?.ToString() ?? RelativeDays.ToString();

    public static implicit operator RelativeDate(int days) => new(days);
    public static implicit operator RelativeDate(DateTime date) => new(date);
}