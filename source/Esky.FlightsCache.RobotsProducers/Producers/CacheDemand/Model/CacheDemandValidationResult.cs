using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model
{
    public class CacheDemandValidationResult
    {
        public bool IsValid { get; set; }
        public IEnumerable<string>? Errors { get; set; }
        public IEnumerable<string>? Warnings { get; set; }
        public IEnumerable<DateTime>? NextOccurrences { get; set; }
    }
}