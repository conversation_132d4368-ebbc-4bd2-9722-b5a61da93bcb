using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public class CacheDemandInputMapper : ICacheDemandInputMapper
    {
        public IList<CacheDemandModelInput> Parse(string content, ColumnMapping columnMapping, string defaultPartnerCode)
        {
            var lines = content.Split(["\r\n", "\r", "\n"], StringSplitOptions.RemoveEmptyEntries);

            if (lines.Length == 1 && lines[0].Split('\t').Length == 1)
                throw new ArgumentException("Invalid input data. Did you forget about quotes?");

            var result = lines
                .SelectMany(x => ParseLine(x, columnMapping, defaultPartnerCode))
                .ToList();

            return result;
        }

        private static IEnumerable<CacheDemandModelInput> ParseLine(string line, ColumnMapping columnMapping, string defaultPartnerCode)
        {
            var parts = line.ToUpper().Split('\t');
            var isRoundTrip = parts[columnMapping.TripType].Trim() == "RT";

            var input = new CacheDemandModelInput
            {
                AirlinesFilter = !string.IsNullOrWhiteSpace(parts[columnMapping.AirlineFilter])
                    ? parts[columnMapping.AirlineFilter].Split(",").Select(o => o.Trim()).ToArray()
                    : [""],
                ProviderCode = null,
                PartnerCode = !string.IsNullOrWhiteSpace(parts[columnMapping.PartnerCode])
                    ? parts[columnMapping.PartnerCode].Trim()
                    : defaultPartnerCode,
                DepartureAirportCode = parts[columnMapping.Departure].Trim(),
                ArrivalAirportCode = parts[columnMapping.Arrival].Trim(),
                DepartureDateFrom = Parse<RelativeDate>(columnMapping.DepartureFrom),
                DepartureDateTo = Parse<RelativeDate>(columnMapping.DepartureTo),
                IsRoundTrip = isRoundTrip,
                MinStayLength = isRoundTrip ? Parse<int>(columnMapping.MinStayLength) : null,
                MaxStayLength = isRoundTrip ? Parse<int>(columnMapping.MaxStayLength) : null,
                OfficeId = columnMapping.OfficeId >= 0 && !string.IsNullOrWhiteSpace(parts[columnMapping.OfficeId])
                    ? parts[columnMapping.OfficeId].Trim()
                    : null,
                AlcExcludedAirlines = columnMapping.AlcExcludedAirlines >= 0 && !string.IsNullOrWhiteSpace(parts[columnMapping.AlcExcludedAirlines])
                    ? parts[columnMapping.AlcExcludedAirlines].Split(",").Select(o => o.Trim()).ToArray()
                    : null
            };

            return parts[columnMapping.ProviderCode]
                .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .Select(provider => input with
                {
                    ProviderCode = int.TryParse(provider, out var code)
                        ? (ProviderCodeEnum?)code
                        : null,
                    Type = provider is "ALC"
                        ? JobType.AmadeusLiveCheck
                        : JobType.CacheDemand
                });

            T Parse<T>(int columnIndex, [CallerArgumentExpression(nameof(columnIndex))] string? columnName = null) where T : IParsable<T>
            {
                return T.TryParse(parts[columnIndex].Trim(), null, out var result)
                    ? result
                    : throw new ArgumentException("Failed to parse cell", columnName);
            }
        }

    }
}
