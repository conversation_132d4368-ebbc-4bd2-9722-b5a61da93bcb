using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public class CacheDemandRoutesAlgorithm : ICacheDemandRoutesAlgorithm
    {
        private readonly IQueueElementCreator _queueElementCreator;

        public CacheDemandRoutesAlgorithm(IQueueElementCreator queueElementCreator)
        {
            _queueElementCreator = queueElementCreator;
        }

        public IEnumerable<T> GenerateQueueElements<T>(CacheDemandJob job) where T : QueueElement, new()
        {
            var sourceName = CacheDemandHelpers.CreateSourceName(job);
            return job.Items.SelectMany(e => GenerateQueueElements<T>(e.InputModel, sourceName));
        }

        public IEnumerable<T> GenerateQueueElements<T>(CacheDemandModelInput input, string sourceName) where T : QueueElement, new()
        {
            var today = DateTime.UtcNow.Date;
            var departureDateRange = input.GetDepartureDateRange().Where(date => today <= date).ToList();
            var returnDaysRange = input.GetReturnDaysRange();
            var firstDepartureDate = departureDateRange.First();
            var lastDepartureDate = departureDateRange.Last();

            var paxConfiguration = input.PartnerCode switch
            {
                "M-ESKY" => PaxConfigurations.AdultChild, // TODO: remove when problems with 1.0.1.1 content from AmadeusNDC are fixed
                "ESKYRO1LAYOUT" => PaxConfigurations.TwoAdults, // TODO: remove when proper fix implemented (for example configurable in sheet per sheet/row)
                _ => PaxConfigurations.AdultChildInfant
            };

            foreach (var airline in input.AirlinesFilter)
            {
                foreach (var departureDate in departureDateRange)
                {
                    IEnumerable<IQueueElementCreator> queueElementCreators;

                    if (!input.IsRoundTrip) // isOw
                    {
                        queueElementCreators = GetOw(input, departureDate);
                    }
                    else // is Rt non-separable or separable
                    {
                        var isFirstDepartureDate = firstDepartureDate == departureDate;
                        var isLastDepartureDate = lastDepartureDate == departureDate;
                        queueElementCreators = GetRt(
                            input,
                            departureDate,
                            returnDaysRange,
                            isFirstDepartureDate,
                            isLastDepartureDate
                        );
                    }
                    foreach (var queueElementCreator in queueElementCreators ?? [])
                    {
                        var queueElement = queueElementCreator
                            .ConfigureAirlineCode(airline)
                            .ConfigureSourceName(sourceName)
                            .ConfigurePaxConfiguration(paxConfiguration)
                            .Resolve()
                            .To<T>();

                        if (queueElement.IsValid())
                        {
                            yield return queueElement;
                        }
                    }
                }
            }

            yield break;

            IEnumerable<IQueueElementCreator> GetOw(CacheDemandModelInput cacheDemandInput, DateTime departureDateTime)
            {
                yield return _queueElementCreator
                    .CreateOneWayElement(
                        cacheDemandInput.ProviderCode,
                        cacheDemandInput.PartnerCode,
                        cacheDemandInput.DepartureAirportCode,
                        cacheDemandInput.ArrivalAirportCode,
                        departureDateTime,
                        officeId: cacheDemandInput.OfficeId,
                        alcExcludedAirlines: cacheDemandInput.AlcExcludedAirlines
                    );
            }

            IEnumerable<IQueueElementCreator> GetRt(
                CacheDemandModelInput cacheDemandInput,
                DateTime departureDateTime,
                IReadOnlyCollection<int> returnDaysRange,
                bool isFirst = false,
                bool isLast = false)
            {
                var dates = GetRtSeparableDates(departureDateTime, returnDaysRange.Order(), isFirst, isLast);

                foreach (var date in dates)
                {
                    yield return _queueElementCreator
                        .CreateSeparableRoundTripElement(
                            cacheDemandInput.ProviderCode,
                            cacheDemandInput.PartnerCode,
                            cacheDemandInput.DepartureAirportCode,
                            cacheDemandInput.ArrivalAirportCode,
                            departureDateTime,
                            date.Returns,
                            officeId: cacheDemandInput.OfficeId,
                            alcExcludedAirlines: cacheDemandInput.AlcExcludedAirlines
                        );
                }
            }
        }

        public static IEnumerable<Dates> GetRtSeparableDates(DateTime date, IOrderedEnumerable<int> returnDays, bool isFirst = false, bool isLast = false)
        {
            var requiredDay = returnDays.Contains(7) ? 7 : returnDays.First();
            var returnDepartureDaysBuffer = new Collection<ReturnDepartureDate>();
            var containsRequired = false;

            foreach (var returnDay in returnDays)
            {
                var isRequired = returnDay == requiredDay
                                 || isFirst && returnDay < requiredDay
                                 || isLast && returnDay > requiredDay;

                if (containsRequired && isRequired)
                {
                    yield return new Dates(date, returnDepartureDaysBuffer.ToArray());
                    returnDepartureDaysBuffer.Clear();
                }
                returnDepartureDaysBuffer.Add(new ReturnDepartureDate(date.AddDays(returnDay), isRequired));
                containsRequired |= isRequired;
            }

            if (returnDepartureDaysBuffer.Count != 0)
            {
                yield return new Dates(date, returnDepartureDaysBuffer.ToArray());
            }
        }

        public record Dates(DateTime Departure, ReturnDepartureDate[] Returns)
        {
            public override string ToString() => $"{Departure:yy-MM-dd} [{string.Join(", ", Returns.Select(r =>($"{r.Return:yy-MM-dd}:{r.IsRequired}")  ))}]";
        }
    }
}
