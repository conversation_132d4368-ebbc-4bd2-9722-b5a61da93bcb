using Cronos;
using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Hangfire;
using Hangfire.Common;
using Hangfire.Storage;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;

public class CacheDemandService
{
    private readonly IGoogleRepository _googleRepository;
    private readonly IFlightOffersRepository _flightOffersRepository;
    private readonly IAirportsRepository _airportsRepository;
    private readonly IRecurringJobManager _recurringJobManager;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly JobStorage _storage;
    private readonly ICacheDemandProducer _producer;
    private readonly ILogger<CacheDemandService> _logger;

    public CacheDemandService(
        IRecurringJobManager recurringJobManager,
        IBackgroundJobClient backgroundJobClient,
        JobStorage storage,
        ICacheDemandProducer producer,
        IGoogleRepository googleRepository,
        IFlightOffersRepository flightOffersRepository,
        IAirportsRepository airportsRepository,
        ILogger<CacheDemandService> logger)
    {
        _recurringJobManager = recurringJobManager;
        _backgroundJobClient = backgroundJobClient;
        _storage = storage;
        _producer = producer;
        _googleRepository = googleRepository;
        _flightOffersRepository = flightOffersRepository;
        _airportsRepository = airportsRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<CacheDemandSheetJob>> GetSheetJobs()
    {
        var jobsTask = GetAllCacheDemandJobs();
        var spreadsheetTasks = _googleRepository.GetSpreadsheets();

        var spreadsheets = (await spreadsheetTasks).ToList();
        var jobsLookup = (await jobsTask).Where(x => x != null).ToLookup(x => x!.Configuration.Name);

        var sheets = spreadsheets
            .SelectMany(spreadsheet => spreadsheet.Sheets.Select(sheet => new
            {
                spreadsheet.Id,
                spreadsheet.Directory,
                spreadsheet.Name,
                spreadsheet.LastModifyingUser,
                spreadsheet.ModifiedTime,
                sheet.LastSuccessfulValidationDate,
                SheetId = sheet.Id,
                SheetName = sheet.Name,
                FullName = $"{spreadsheet.Directory}|{spreadsheet.Name}|{sheet.Name}"
            }));

        var sheetsLookup = sheets.ToLookup(x => x.FullName);

        var keys = new HashSet<string>(sheetsLookup.Select(x => x.Key));
        keys.UnionWith(jobsLookup.Select(x => x.Key));

        var result = from key in keys
                     from spreadsheet in sheetsLookup[key].DefaultIfEmpty()
                     from job in jobsLookup[key].DefaultIfEmpty()
                     select new CacheDemandSheetJob
                     {
                         SpreadsheetId = spreadsheet?.Id,
                         SpreadsheetName = spreadsheet?.Name,
                         DirectoryName = spreadsheet?.Directory,
                         SheetName = spreadsheet?.SheetName,
                         SheetId = spreadsheet?.SheetId,
                         LastModifyingUser = spreadsheet?.LastModifyingUser,
                         ModifiedTime = spreadsheet?.ModifiedTime,
                         LastSuccessfulValidationDate = spreadsheet?.LastSuccessfulValidationDate,
                         JobId = job?.Id,
                         JobName = job?.Configuration.Name,
                         JobCreatedAt = job?.CreatedAt,
                         JobStartDate = job?.Configuration?.StartDate,
                         JobEndDate = job?.Configuration?.EndDate,
                         JobCronExpression = job?.Configuration?.CronExpression,
                         RemovalJobId = job?.Configuration?.RemovalJobId
                     };

        return result;
    }

    public async Task<CacheDemandValidationResult> ValidateSheetJob(string spreadsheetId, string sheetName)
    {
        var errors = new List<string>();
        var warnings = new List<string>();
        var nextOccurrences = new List<DateTime>();
        var content = new StringBuilder();
        var rowHashSet = new HashSet<string>();

        var values = await _googleRepository.GetValues(spreadsheetId, sheetName);

        if (values.Any())
        {
            ValidateFirstRow(errors, warnings, nextOccurrences, values[0]);
        }
        var header = values.Count > 1 ? string.Join("\t", values[1]) : null;
        foreach (var row in values.Skip(2))
        {
            var rowString = string.Join("\t", row);
            rowHashSet.Add(rowString);
            content.AppendLine(rowString);
        }

        var dryRunResult = await _producer.DryRun(content.ToString(), header, false);
        ValidateDryRunResult(errors, warnings, dryRunResult, rowHashSet);

        var isValid = !errors.Any();
        await UpdateSheet(dryRunResult, isValid, spreadsheetId, sheetName);

        return new CacheDemandValidationResult { IsValid = isValid, Errors = errors, Warnings = warnings, NextOccurrences = nextOccurrences };
    }

    public async Task<CacheDemandValidationResult> RunSheetJob(string spreadsheetId, string sheetName, string? jobId, string? removalJobId)
    {
        var validationResult = await ValidateSheetJob(spreadsheetId, sheetName);

        if (!validationResult.IsValid) return validationResult;

        var spreadsheetTask = _googleRepository.GetSpreadsheets();
        var valuesTask = _googleRepository.GetValues(spreadsheetId, sheetName);

        var spreadsheet = (await spreadsheetTask).First(x => x.Id == spreadsheetId);
        var values = await valuesTask;

        await UpsertCacheDemandJob(new CacheDemandJobConfiguration
        {
            CronExpression = values[0][1].ToString() ?? Cron.Never(),
            StartDate = ParseToDateTimeNullable((string)values[0][3]),
            EndDate = ParseToDateTimeNullable((string)values[0][5]),
            Name = CacheDemandHelpers.CreateJobName(spreadsheet, sheetName),
            Content = values.Skip(2).Aggregate(string.Empty, (current, row) => current + string.Join("\t", row) + "\n"),
            RemovalJobId = removalJobId,
            Header = string.Join("\t", values[1])
        }, jobId);

        return validationResult;
    }

    public void RemoveCacheDemandJob(string id)
    {
        _recurringJobManager.RemoveIfExists(id);
    }

    private async Task<IReadOnlyCollection<CacheDemandJobDto?>> GetAllCacheDemandJobs()
    {
        var producerType = typeof(CacheDemandProducer);
        var configParamIndex = producerType
            .GetMethods()
            .Where(i => i.Name == nameof(CacheDemandProducer.Generate))
            .Select(i => i
                .GetParameters()
                .Select((e, id) => (Parameter: e, Index: id))
                .First(e => e.Parameter.ParameterType == typeof(CacheDemandJobConfiguration))
                .Index)
            .Distinct()
            .First();

        var conn = _storage.GetConnection();
        var jobIds = conn.GetAllItemsFromSet("recurring-jobs").Where(x => Guid.TryParse(x, out _));

        var tasks = jobIds.Select(async id =>
        {
            return await Task.Run(() =>
            {
                var allEntriesFromHash = conn.GetAllEntriesFromHash("recurring-job:" + id);
                var job = InvocationData.DeserializePayload(allEntriesFromHash["Job"]).DeserializeJob();

                if (job.Type != producerType) return null;

                return new CacheDemandJobDto
                {
                    Id = id,
                    CreatedAt = JobHelper.DeserializeNullableDateTime(allEntriesFromHash["CreatedAt"]),
                    Configuration = job.Args[configParamIndex] as CacheDemandJobConfiguration ?? throw new Exception($"Could not find {nameof(CacheDemandJobConfiguration)} parameter")
                };
            });
        });

        return await Task.WhenAll(tasks);
    }

    private async Task UpsertCacheDemandJob(CacheDemandJobConfiguration config, string? jobId = null)
    {
        var job = await _producer.CreateAndValidateJob(config.Content, config.Header);
        if (!job.IsValid) return;

        var jobName = jobId ?? Guid.NewGuid().ToString();

        if (!string.IsNullOrWhiteSpace(config.RemovalJobId))
        {
            _backgroundJobClient.Delete(config.RemovalJobId);
        }

        config.RemovalJobId = config.EndDate.HasValue
            ? _backgroundJobClient.Schedule(() => _recurringJobManager.RemoveIfExists(jobName), config.EndDate.Value)
            : null;

        _recurringJobManager.AddOrUpdate<CacheDemandProducer>(
            jobName,
            prod => prod.Generate(config, null),
            config.CronExpression,
            TimeZoneInfo.Utc
        );
    }

    private static DateTime? ParseToDateTimeNullable(string text)
    {
        return DateTime.TryParse(text, out var dateTime) ? dateTime : null;
    }

    private static void ValidateFirstRow(ICollection<string> errors, ICollection<string> warnings,
        List<DateTime> nextOccurrences, IList<object> row)
    {
        var startDate = ParseToDateTimeNullable((string)row[3]);
        var endDate = ParseToDateTimeNullable((string)row[5]);
        if (startDate > endDate) errors.Add("StartDate > EndDate");
        if (endDate < DateTime.Now) errors.Add("EndDate < Now");
        if (startDate > DateTime.Now.AddMonths(13)) warnings.Add("StartDate > Now + 13 months");
        if (endDate > DateTime.Now.AddMonths(13)) warnings.Add("EndDate > Now + 13 months");

        var cron = (string)row[1];
        try
        {
            nextOccurrences.AddRange(GetNextCronOccurrences(5, CronExpression.Parse(cron)));
        }
        catch (Exception)
        {
            errors.Add("Invalid cron");
        }

        if (cron[0] == '*') errors.Add("Cron cannot start with '*' (too many executions)");
    }

    private static void ValidateDryRunResult(ICollection<string> errors,
        ICollection<string> warnings, CacheDemandJob dryRunResult, IReadOnlyCollection<string> rowHashSet)
    {
        if (dryRunResult.TotalRequestsGenerated > 100_000)
            warnings.Add("Job generates > 100 000 requests");
        if (dryRunResult.Items.Any(x => x.RequestsGenerated > 1500))
            warnings.Add("Row generates > 1500 requests");
        if (dryRunResult.Items.Any(x => x.InputModel.MaxStayLength - x.InputModel.MinStayLength > 30))
            warnings.Add("Stay length > 30 days");
        if (rowHashSet.Count != dryRunResult.Items.Count)
            errors.Add("Duplicated rows");
        if (!dryRunResult.Items.Any())
            errors.Add("There are no rows");
        if (!string.IsNullOrEmpty(dryRunResult.ErrorMessage))
            errors.Add(dryRunResult.ErrorMessage);
    }

    private async Task UpdateSheet(CacheDemandJob dryRunResult, bool isValid, string spreadsheetId, string sheetName)
    {
        var tasks = new List<Task>
        {
            _googleRepository.SetRowRequestsGenerated(spreadsheetId, sheetName,
                dryRunResult.Items.Select(x => x.RequestsGenerated), dryRunResult.RequestsGeneratedColumnIndex),
            _googleRepository.SetLastSuccessfulValidationDate(spreadsheetId, sheetName,
                isValid ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : string.Empty),
            _googleRepository.SetTotalRequestsGenerated(spreadsheetId, sheetName,
                dryRunResult.TotalRequestsGenerated)
        };

        await Task.WhenAll(tasks);
    }

    private static IEnumerable<DateTime> GetNextCronOccurrences(int count, CronExpression cron)
    {
        var result = new List<DateTime>();
        var nextOccurrence = DateTime.UtcNow;
        for (var i = 0; i < count; i++)
        {
            nextOccurrence = cron.GetNextOccurrence(nextOccurrence).GetValueOrDefault();
            result.Add(nextOccurrence);
        }

        return result;
    }

    public async Task<CacheDemandValidationResult> CalculateJobCoverage(string spreadsheetId, string sheetName)
    {
        var spreadsheetTask = _googleRepository.GetSpreadsheets();
        var valuesTask = _googleRepository.GetValues(spreadsheetId, sheetName);

        var spreadsheet = (await spreadsheetTask).First(x => x.Id == spreadsheetId);
        var values = await valuesTask;

        var columnMapping = ColumnMapping.Parse(values.GetHeader());
        
        if (columnMapping.CollectedDates == ColumnMapping.NotFound || columnMapping.CollectedFlights == ColumnMapping.NotFound || columnMapping.CollectedDatesWithMoreThan1Stop == ColumnMapping.NotFound)
        {
            return new CacheDemandValidationResult
            {
                IsValid = false,
                Errors =
                [
                    "The sheet is missing columns 'CollectedDates', 'CollectedFlights' or 'CollectedDatesMoreThan1Stop'. Please add these columns to the sheet and try again."
                ]
            };
        }

        _ = Task.Run(async () =>
        {
            try
            {
                var collectedDates = new List<int>();
                var collectedFlights = new List<int>();
                var collectedDatesWithMoreThan1Stop = new List<int>();
                var sourceName = CacheDemandHelpers.CreateSourceName(spreadsheet, sheetName);

                var counter = 0;
                var rowNumber = 3;
            
                foreach (var row in values.GetContent())
                {
                    var departure = row[columnMapping.Departure].ToString();
                    var arrival = row[columnMapping.Arrival].ToString();

                    if (string.IsNullOrEmpty(departure) || string.IsNullOrEmpty(arrival)) continue;

                    var departureAirports = await _airportsRepository.GetAirportCodesAsync(departure);
                    var arrivalAirports = await _airportsRepository.GetAirportCodesAsync(arrival);

                    var routes = departureAirports.SelectMany(d => arrivalAirports.Select(a => $"{d}-{a}")).ToArray();

                    var tripType = row[columnMapping.TripType].ToString();

                    var (dates, flights, datesWithMoreThan1Stop) = tripType == "OW"
                        ? await _flightOffersRepository.GetSourceStatsOw(sourceName, routes)
                        : await _flightOffersRepository.GetSourceStatsRt(sourceName, routes);

                    collectedDates.Add(dates);
                    collectedFlights.Add(flights);
                    collectedDatesWithMoreThan1Stop.Add(datesWithMoreThan1Stop);

                    counter++;

                    if (counter % 100 == 0)
                    {
                        await SaveCollectedValues(collectedDates, collectedFlights, collectedDatesWithMoreThan1Stop, rowNumber);

                        rowNumber += counter;
                        counter = 0;
                        collectedDates.Clear();
                        collectedFlights.Clear();
                        collectedDatesWithMoreThan1Stop.Clear();
                    }
                }
                
                await SaveCollectedValues(collectedDates, collectedFlights, collectedDatesWithMoreThan1Stop, rowNumber);
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "Error while calculating sheet job");
            }
        });

        return new CacheDemandValidationResult { IsValid = true };

        async Task SaveCollectedValues(List<int> collectedDates, List<int> collectedFlights, List<int> collectedDatesWithMoreThan1Stop, int rowNumber)
        {
            await _googleRepository.SetValues(spreadsheetId, sheetName, columnMapping.CollectedDates, rowNumber, collectedDates);
            await _googleRepository.SetValues(spreadsheetId, sheetName, columnMapping.CollectedFlights, rowNumber, collectedFlights);
            await _googleRepository.SetValues(spreadsheetId, sheetName, columnMapping.CollectedDatesWithMoreThan1Stop, rowNumber, collectedDatesWithMoreThan1Stop);
        }
    }
}

public record CacheDemandJobDto
{
    public required string Id { get; init; }
    public required DateTime? CreatedAt { get; init; }
    public required CacheDemandJobConfiguration Configuration { get; init; }
}