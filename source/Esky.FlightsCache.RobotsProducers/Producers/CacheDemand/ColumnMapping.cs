using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public class ColumnMapping
    {
        public const int NotFound = -1;
        public int AirlineFilter { get; init; }
        public int ProviderCode { get; init; }
        public int PartnerCode { get; init; }
        public int Departure { get; init; }
        public int Arrival { get; init; }
        public int DepartureFrom { get; init; }
        public int DepartureTo { get; init; }
        public int TripType { get; init; }
        public int MinStayLength { get; init; }
        public int MaxStayLength { get; init; }
        public int OfficeId { get; init; }
        public int AlcExcludedAirlines { get; init; }
        public int Requests { get; init; }
        public int CollectedDates { get; init; }
        public int CollectedFlights { get; init; }
        public int CollectedDatesWithMoreThan1Stop { get; init; }

        private static ColumnMapping DefaultMapping => new()
        {
            AirlineFilter = 0,
            ProviderCode = 1,
            PartnerCode = 2,
            Departure = 3,
            Arrival = 4,
            DepartureFrom = 5,
            DepartureTo = 6,
            TripType = 7,
            MinStayLength = 8,
            MaxStayLength = 9,
            OfficeId = -1,
            AlcExcludedAirlines = -1,
            Requests = 10,
            CollectedDates = -1,
            CollectedFlights = -1,
            CollectedDatesWithMoreThan1Stop = -1
        };

        public static ColumnMapping Parse(IList<string> parts)
        {
            if (parts.Count == 0) return DefaultMapping;

            var mapping = new ColumnMapping
            {
                AirlineFilter = GetHeaderIndex(nameof(AirlineFilter)),
                ProviderCode = GetHeaderIndex(nameof(ProviderCode)),
                PartnerCode = GetHeaderIndex(nameof(PartnerCode)),
                Departure = GetHeaderIndex(nameof(Departure)),
                Arrival = GetHeaderIndex(nameof(Arrival)),
                DepartureFrom = GetHeaderIndex(nameof(DepartureFrom)),
                DepartureTo = GetHeaderIndex(nameof(DepartureTo)),
                TripType = GetHeaderIndex(nameof(TripType)),
                MinStayLength = GetHeaderIndex(nameof(MinStayLength)),
                MaxStayLength = GetHeaderIndex(nameof(MaxStayLength)),
                OfficeId = GetHeaderIndex(nameof(OfficeId), isOptional: true),
                AlcExcludedAirlines = GetHeaderIndex(nameof(AlcExcludedAirlines), isOptional: true),
                Requests = GetHeaderIndex(nameof(Requests), fallback: () => GetHeaderIndex(parts.Last(x => !string.IsNullOrWhiteSpace(x))) + 1),
                CollectedDates = GetHeaderIndex(nameof(CollectedDates), isOptional: true),
                CollectedFlights = GetHeaderIndex(nameof(CollectedFlights), isOptional: true),
                CollectedDatesWithMoreThan1Stop = GetHeaderIndex(nameof(CollectedDatesWithMoreThan1Stop), isOptional: true)
            };

            return mapping;

            int GetHeaderIndex(string headerName, bool isOptional = false, Func<int>? fallback = null)
            {
                var idx = parts.IndexOf(headerName);
                if (idx >= 0) return idx;
                if (!isOptional && fallback is null) throw new ArgumentException("Missing header: {headerName}", headerName);
                return fallback?.Invoke() ?? -1;
            }
        }

        public static ColumnMapping Parse(string? header)
        {
            if (string.IsNullOrWhiteSpace(header)) return DefaultMapping;
            var parts = header.Split('\t').ToList();
            return Parse(parts);
        }
    }
}
