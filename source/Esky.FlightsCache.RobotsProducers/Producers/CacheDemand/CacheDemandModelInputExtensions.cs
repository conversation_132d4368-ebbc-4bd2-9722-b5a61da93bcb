using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Messages;

namespace Esky.FlightsCache.RobotsProducers.Producers.CacheDemand
{
    public static class CacheDemandModelInputExtensions
    {
        public static List<DateTime> GetDepartureDateRange(this CacheDemandModelInput input)
        {
            var departureDateFrom = input.DepartureDateFrom.GetDate(DateTime.UtcNow.Date).ToTomorrowIfPastOrToday();
            return departureDateFrom.Range(input.DepartureDateTo.GetDate(departureDateFrom)).ToList();
        }

        public static int[] GetReturnDaysRange(this CacheDemandModelInput input) => input is { IsRoundTrip: true, MinStayLength: not null, MaxStayLength: not null }
            ? Enumerable.Range(input.MinStayLength.Value, input.MaxStayLength.Value - input.MinStayLength.Value + 1).ToArray()
            : [];

        public static IEnumerable<CacheDemandJobItem> ToJobItems(this IEnumerable<CacheDemandModelInput> inputs, ICacheDemandRoutesAlgorithm algorithm)
        {
            return inputs.Select(input => input.ToJobItem(algorithm));
        }
        
        public static CacheDemandJobItem ToJobItem(this CacheDemandModelInput input, ICacheDemandRoutesAlgorithm algorithm)
        {
            var (elementsCount, requestsCount) = algorithm
                .GenerateQueueElements<QueueElement>(input, "")
                .Aggregate(
                    (ElementsCount: 0, RequestsCount: 0),
                    (tuple, element) => (tuple.ElementsCount + 1, tuple.RequestsCount + (element.ReturnDepartureDays?.Length ?? 1))
                );

            return new CacheDemandJobItem
            {
                InputModel = input,
                ElementsGenerated = elementsCount,
                RequestsGenerated = requestsCount
            };
        }

        public static CacheDemandJob Get(this CacheDemandJob self, JobType type)
        {
            var job = self with
            {
                Items = self.Items
                    .Where(e => (e.InputModel.Type ?? JobType.CacheDemand) == type)
                    .ToList()
            };
            return job;
        }
    }
}
