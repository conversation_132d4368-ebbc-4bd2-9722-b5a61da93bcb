using Newtonsoft.Json;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

[JsonConverter(typeof(RouteConverter))]
public record Route(string Departure, string Arrival) : IParsable<Route>
{
    private const char _separator = '-';
    public static Route Parse(string s, IFormatProvider? provider)
    {
        if (TryParse(s, provider, out var route))
        {
            return route;
        }

        throw new ArgumentException($"Cannot parse value '{s}' as route", nameof(s));
    }

    public static bool TryParse([NotNullWhen(true)] string? s, IFormatProvider? provider, [MaybeNullWhen(false)] out Route result)
    {
        var parts = s?.Split(_separator) ?? [];
        if (parts.Length == 2)
        {
            result = new Route(parts[0], parts[1]);
            return true;
        }

        result = default;
        return false;
    }
    public override string ToString() => $"{Departure}{_separator}{Arrival}";

    public static implicit operator Route(string route) => Parse(route, null);
}

public class RouteConverter : JsonConverter<Route>
{
    public override void WriteJson(JsonWriter writer, Route? value, JsonSerializer serializer)
    {
        writer.WriteValue(value?.ToString());
    }

    public override Route? ReadJson(
        JsonReader reader,
        Type objectType,
        Route? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        if (reader.Value is null)
            return null;

        var str = (string)reader.Value;
        return Route.Parse(str, null);
    }
}