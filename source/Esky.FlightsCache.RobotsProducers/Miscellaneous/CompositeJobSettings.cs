using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

public class CompositeGroupConfigurationConverter : IJobConverter<CompositeGroupConfiguration, CompositeJobSettings>
{
    public  CompositeGroupConfiguration[] FromJobs(IEnumerable<CompositeJobSettings> settings)
    {
        return settings.GroupBy(e => e.Group)
            .Select(e =>
            {
                var first = e.First();
                return new CompositeGroupConfiguration()
                {
                    Provider = first.Provider,
                    PartnerCode = first.PartnerCode,
                    PaxConfigurations = first.PaxConfigurations,
                    Next = first.Next,
                    ProceedNextOnSuccess = first.ProceedNextOnSuccess,
                    OverrideSettings = first.OverrideSettings,
                    
                    Name = e.Key,
                    Configurations = e.Select(GetConfiguration).ToArray(),
                    Supplier = first.Supplier,
                    Flex = first.Flex,
                    Airlines = first.Airlines,
                    DepartureAirports = first.DepartureAirports,
                    ExcludedDepartureAirports = first.ExcludedDepartureAirports,
                    ArrivalAirports = first.ArrivalAirports,
                    Routes = first.Routes,
                    IsOneWay = first.IsOneWay,
                    IgnoreTimetables = first.IgnoreTimetables,
                    StayLengths = first.StayLengths,
                    ActiveFromHour = first.ActiveFrom?.Hour,
                    ActiveToHour = first.ActiveTo?.Hour,
                    SkipTheSameDepartureDateAsReturnDeparture = first.SkipTheSameDepartureDateAsReturnDeparture,
                };
            })
            .ToArray();
        
        Configuration GetConfiguration(CompositeJobSettings settings) => new()
        {
            Cron = settings.Cron,
            RelativeDateRange = settings.RelativeDateRange,
            MaxProcessingWindow = settings.MaxProcessingWindow,
        };
    }

    public IEnumerable<(string JobName, CompositeJobSettings JobSettings)> ToJobs(DateTime referenceDate, CompositeGroupConfiguration group)
    {
        var jobs = group.Configurations
            .Select((c, i) => GetJobSettings(group, c, i.ToString()))
            .ToArray();

        for (var i = 0; i < jobs.Length; i++)
        {
            var job = jobs[i];
            for (var j = i + 1; j < jobs.Length; j++)
            {
                var result = job.Overlaps(jobs[j], referenceDate);
                if (result.Overlaps || result.OverlapsInDays.HasValue)
                {
                    jobs[j] = jobs[j] with { Exclusions = [..jobs[j].Exclusions, job.RelativeDateRange] };
                }
            }
        }

        return jobs.Select((s, i) => (group.GetJobName(i), s));
    }
    
    private CompositeJobSettings GetJobSettings(CompositeGroupConfiguration group, Configuration configuration,
        string suffix) => new()
    {
        Provider = group.Provider,
        PartnerCode = group.PartnerCode,
        PaxConfigurations = group.PaxConfigurations,
        Next = group.Next,
        ProceedNextOnSuccess = group.ProceedNextOnSuccess,
        OverrideSettings = group.OverrideSettings,
        
        Group = group.Name,
        Suffix = suffix,
        Cron = configuration.Cron,
        RelativeDateRange = configuration.RelativeDateRange,
        MaxProcessingWindow = configuration.MaxProcessingWindow,
        DepartureAirports = group.DepartureAirports,
        ExcludedDepartureAirports = group.ExcludedDepartureAirports,
        ArrivalAirports = group.ArrivalAirports,
        Routes = group.Routes,
        Airlines = group.Airlines,
        Flex = group.Flex,
        Supplier = group.Supplier,
        Exclusions = [],
        IsOneWay = group.IsOneWay,
        IgnoreTimetables = group.IgnoreTimetables,
        StayLengths = group.StayLengths,
        ActiveFrom = group.ActiveFromHour.HasValue
            ? new TimeOnly(group.ActiveFromHour.Value,
                0)
            : null,
        ActiveTo = group.ActiveToHour.HasValue
            ? new TimeOnly(group.ActiveToHour.Value,
                0)
            : null,
        SkipTheSameDepartureDateAsReturnDeparture = group.SkipTheSameDepartureDateAsReturnDeparture,
    };
}

public class CompositeGroupConfiguration : IGroup
{
    public ProviderCodeEnum Provider { get; init; }
    public required string PartnerCode { get; init; }
    public required string[] PaxConfigurations { get; init; }
    public CompositeQueueElement.Override? OverrideSettings { get; init; }
    public bool ProceedNextOnSuccess { get; init; }
    public NextInCompositeJobSettings? Next { get; init; }
    
    private const string _prefix = "Group";
    private const int _maxConfigs = 10;
    public required string Name { get; init; }
    public required Configuration[] Configurations { get; init; }
    public required string Supplier { get; init; }
    public string[] Airlines { get; init; } = [];
    public int Flex { get; init; }
    public string[] DepartureAirports { get; init; } = [];
    public string[]? ExcludedDepartureAirports { get; init; } = [];
    public string[] ArrivalAirports { get; init; } = [];
    public Route[] Routes { get; init; } = [];
    public bool IsOneWay { get; init; }
    public bool IgnoreTimetables { get; init; }
    public int[] StayLengths { get; init; } = [];
    public int? ActiveFromHour { get; init; }
    public int? ActiveToHour { get; init; }
    public bool SkipTheSameDepartureDateAsReturnDeparture { get; init; }

    public bool IsValid(out IReadOnlyCollection<string> errors)
    {
        var errorList = new List<string>();
        errors = errorList;

        if (Provider == default)
        {
            errorList.Add($"{nameof(Provider)} is empty");
        }
        
        if (string.IsNullOrWhiteSpace(PartnerCode))
        {
            errorList.Add($"{nameof(PartnerCode)} is empty");
        }

        if (PaxConfigurations.Length == 0 || Array.Exists(PaxConfigurations, string.IsNullOrWhiteSpace))
        {
            errorList.Add($"{nameof(PaxConfigurations)} cannot be empty");
        }

        if (Configurations.Length is 0 or > _maxConfigs)
        {
            errorList.Add($"{nameof(Configurations)} must be between 1 and 10");
        }

        if (Array.Exists(Configurations, e => !e.Valid()))
        {
            errorList.Add("some configuration is not valid");
        }

        if (IgnoreTimetables)
        {
            if (Routes.Length == 0) errorList.Add($"{nameof(Routes)} cannot be empty when {nameof(IgnoreTimetables)} is true");
            if (DepartureAirports.Length != 0) errorList.Add($"{nameof(DepartureAirports)} should be empty when {nameof(IgnoreTimetables)} is true");
            if (ArrivalAirports.Length != 0) errorList.Add($"{nameof(ArrivalAirports)} should be empty when {nameof(IgnoreTimetables)} is true");
        }
        else
        {
            if (Airlines.Length is 0)
            {
                errorList.Add($"{nameof(Airlines)} are empty");
            }
        }

        if (IsOneWay && StayLengths.Length is not 0)
        {
            errorList.Add($"{nameof(StayLengths)} should be empty when {nameof(IsOneWay)} is true");
        }

        if(ActiveFromHour.HasValue && (ActiveFromHour.Value < 0 || ActiveFromHour >= 24))
        {
            errorList.Add($"{nameof(ActiveFromHour)} should be between 0 and 24");
        }

        if (ActiveToHour.HasValue && (ActiveToHour.Value < 0 || ActiveToHour >= 24))
        {
            errorList.Add($"{nameof(ActiveToHour)} should be between 0 and 24");
        }

        return errorList.Count == 0;
    }

    public IEnumerable<OverlapCheckResult> CheckForOverlaps(DateTime referenceDate) =>
        Configurations.CheckForOverlaps(referenceDate);

    public static IEnumerable<string> GetJobNames(string groupName)
    {
        return Enumerable.Range(0, _maxConfigs).Select(index => GetJobName(groupName, index));
    }

    private static string GetJobName(string groupName, int index) => $"{_prefix}_{groupName}_{index}";
    public string GetJobName(int index) => GetJobName(Name, index);
    public IEnumerable<string> GetJobNames() => GetJobNames(Name);
}

public record CompositeJobSettings : JobSettings
{
    public required ProviderCodeEnum Provider { get; init; }
    public required string PartnerCode { get; init; }
    public required string[] PaxConfigurations { get; init; }

    public CompositeQueueElement.Override? OverrideSettings { get; init; }

    public bool ProceedNextOnSuccess { get; init; }
    public NextInCompositeJobSettings? Next { get; init; }

    public override string ToString() => base.ToString() + $" Composite: {Provider}:{Supplier}:{PartnerCode}[{Next}]";
}
public record NextInCompositeJobSettings
{
    public required bool ProceedNextOnSuccess { get; init; }
    public ProviderCodeEnum? Provider { get; init; }
    public string? Supplier { get; init; }
    public string? PartnerCode { get; init; }
    public string[]? PaxConfigurations { get; init; }
    public int? Flex { get; init; }
    public int[]? StayLengths { get; init; }
    public bool? SkipTheSameDepartureDateAsReturnDeparture { get; init; }
    
    public NextInCompositeJobSettings? Next { get; init; }

    public override string ToString() => $"[{Provider}:{Supplier}:{PartnerCode}[{Next}]]";
}