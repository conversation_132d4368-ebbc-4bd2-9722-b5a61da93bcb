using Esky.FlightsCache.Robots.Algorithms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;

public interface IRouteDatesProvider
{
    Task<IEnumerable<RouteDate>> GetRouteDates(JobSettings jobSettings, Action<string> log);
}

public class RouteDatesProvider: IRouteDatesProvider
{
    private readonly TimeProvider _timeProvider;
    private readonly IResolverFactory _resolverFactory;

    public RouteDatesProvider(TimeProvider timeProvider, IResolverFactory resolverFactory)
    {
        _timeProvider = timeProvider;
        _resolverFactory = resolverFactory;
    }

    public async Task<IEnumerable<RouteDate>> GetRouteDates(JobSettings jobSettings, Action<string> log)
    {
        var (itineraryResolver, routeDatesResolver) = _resolverFactory.Create(jobSettings);

        var itineraries = await itineraryResolver.Resolve(jobSettings, log);
        var settings = jobSettings with { Routes = itineraries.Select(e => e.Route).ToArray() };

        var today = _timeProvider.GetUtcNow().Date;
        var jobRange = settings.Range(today).WithStartNotBefore(today);
        log(
            $"""
             Gathering and filtering flying dates based on settings ({nameof(JobSettings.RelativeDateRange)}) from {jobRange.Start:yyyy-MM-dd} up to {jobRange.End:yyyy-MM-dd}
             Without excluded:
             {string.Join("\r\n", settings.GetExclusionRanges(today).Select(range => $"{range.Start:yyyy-MM-dd} to {range.End:yyyy-MM-dd}"))}
             ---
             """
        );

        var result = new List<RouteDate>();

        foreach (var itinerary in itineraries)
        {
            var departureDays = settings.WithoutOverlapped(itinerary.DepartureDates, today).Order().ToArray();
            var returnDays = itinerary.ReturnDates.Order().ToArray();

            var fixedItinerary = itinerary with { DepartureDates = departureDays, ReturnDates = returnDays };
            var routeDates = routeDatesResolver.Resolve(settings, fixedItinerary);

            var withMatchingDates = routeDates
                .Where(e => jobSettings.UseCalendar
                            || departureDays.Contains(e.DepartureDay))
                .Where(e => jobSettings.UseCalendar
                            || itineraryResolver.MatchReturnDate is false 
                            || e.ReturnDepartureDay is null 
                            || returnDays.Contains(e.ReturnDepartureDay.Value))
                .Where(e => jobSettings.IsOneWay 
                            || jobSettings.SkipTheSameDepartureDateAsReturnDeparture is false 
                            || e.DepartureDay != e.ReturnDepartureDay)
                .ToArray();

            var flexedWithMatchingDates = withMatchingDates.ApplyFlex(settings.Flex).ToArray();
            result.AddRange(flexedWithMatchingDates);
            var flexInfo = settings.Flex > 0 ? $" -> {flexedWithMatchingDates.Length} (flex)" : "";
            log($"{itinerary.Route.Departure}-{itinerary.Route.Arrival}: {fixedItinerary.DepartureDates.Count} of {itinerary.DepartureDates.Count}{flexInfo}");
        }

        return result;
    }
}