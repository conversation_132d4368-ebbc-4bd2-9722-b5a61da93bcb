using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;

public class OneWay : IRouteDatesResolver
{
    public IReadOnlyCollection<RouteDate> Resolve(JobSettings jobSettings, Itinerary itinerary)
    {
        var dates = itinerary.DepartureDates
            .Select(departureDate => new RouteDate(itinerary.Route, departureDate, null))
            .ToArray();
        return dates;
    }
}