using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;

public class ReturnDepartureAfterDepartureDay : IRouteDatesResolver
{
    public IReadOnlyCollection<RouteDate> Resolve(JobSettings jobSettings, Itinerary itinerary)
    {
        var dates = new[] { itinerary }
            .ReturnDepartureNextDay(
                e => e.DepartureDates,
                e => e.ReturnDates,
                (source, departureDate, returnDepartureDate) => new RouteDate(source.Route, departureDate, returnDepartureDate))
            .ToArray();
        return dates;
    }
}