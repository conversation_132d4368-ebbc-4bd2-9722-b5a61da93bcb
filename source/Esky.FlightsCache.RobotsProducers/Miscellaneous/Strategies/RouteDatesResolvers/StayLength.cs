using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;

public class StayLength : IRouteDatesResolver
{
    public IReadOnlyCollection<RouteDate> Resolve(JobSettings jobSettings, Itinerary itinerary)
    {
        var dates = itinerary.DepartureDates
            .SelectMany(departureDate => jobSettings.StayLengths.Select(days => new RouteDate(itinerary.Route, departureDate, departureDate.AddDays(days))))
            .ToArray();
        return dates;
    }
}