using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;
using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDepartureDatesResolvers;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using System;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies;

public interface IResolverFactory
{
    (IItineraryResolver, IRouteDatesResolver) Create(JobSettings jobSettings);
}

public class ResolverFactory : IResolverFactory
{
    private readonly TimeProvider _timeProvider;
    private readonly ITimetableServiceClient _timetableClient;

    public ResolverFactory(TimeProvider timeProvider, ITimetableServiceClient timetableClient)
    {
        _timeProvider = timeProvider;
        _timetableClient = timetableClient;
    }

    public (IItineraryResolver, IRouteDatesResolver) Create(JobSettings jobSettings)
    {
        ArgumentNullException.ThrowIfNull(jobSettings);
        return (ItineraryResolver(jobSettings), RouteDatesResolver(jobSettings));
    }

    private IItineraryResolver ItineraryResolver(JobSettings jobSettings)
    {
        var resolver = jobSettings.IgnoreTimetables 
            ? (IItineraryResolver)new FromJobSettings(_timeProvider) 
            : new FromTimetables(_timetableClient);

        return resolver;
    }

    private IRouteDatesResolver RouteDatesResolver(JobSettings jobSettings)
    {
        if (jobSettings.IsOneWay)
        {
            return new OneWay();
        }

        if (jobSettings.StayLengths.Length is not 0)
        {
            return new StayLength();
        }

        return jobSettings.UseCalendar 
            ? new SingleDatePerMonth(_timeProvider)
            : new ReturnDepartureAfterDepartureDay();
    }
}