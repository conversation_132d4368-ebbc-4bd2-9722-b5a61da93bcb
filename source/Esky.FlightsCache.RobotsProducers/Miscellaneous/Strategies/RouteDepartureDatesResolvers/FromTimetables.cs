using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDepartureDatesResolvers;

public class FromTimetables : IItineraryResolver
{
    private readonly ITimetableServiceClient _timetableClient;

    public FromTimetables(ITimetableServiceClient timetableClient)
    {
        _timetableClient = timetableClient;
    }

    public bool MatchReturnDate => true;

    public async Task<IReadOnlyCollection<Itinerary>> Resolve(JobSettings jobSettings, Action<string> log)
    {
        log($"Gathering routes for [{string.Join(',', jobSettings.Airlines)}]");
        var connectionNetworks = await Task.WhenAll(jobSettings.Airlines.Select(_timetableClient.GetConnectionNetworkByAirline));

        log(
            $"""
             Filtering routes based on settings
             {nameof(JobSettings.DepartureAirports)}: [{string.Join(',', jobSettings.DepartureAirports)}]
             {nameof(JobSettings.ArrivalAirports)}: [{string.Join(',', jobSettings.ArrivalAirports)}]
             {nameof(JobSettings.Routes)}: [{string.Join(',', jobSettings.Routes.Select(r => r.ToString()))}]
             {nameof(JobSettings.ExcludedDepartureAirports)}: [{string.Join(',', jobSettings.ExcludedDepartureAirports ?? [])}]
             """
        );
        var distinctRoutes = connectionNetworks
            .SelectMany(n => n)
            .SelectMany(
                connection => connection.ArrivalAirportCodes,
                (connection, arrival) => new Route(connection.DepartureAirportCode, arrival.ArrivalCode)
            )
            .ToHashSet();

        var allowedRoutes = distinctRoutes.Where(jobSettings.Contains).ToArray();
        log($"{allowedRoutes.Length} of {distinctRoutes.Count} routes");

        var result = new List<Itinerary>(allowedRoutes.Length);

        foreach (var route in allowedRoutes)
        {
            var allDepartureDays = await _timetableClient.GetFlyingDays(route.Departure, route.Arrival, jobSettings.Airlines);
            // log($"{route.Departure}-{route.Arrival}: {allDepartureDays.Count}");
            
            IList<DateTime> allReturnDays = new List<DateTime>();
            if (!jobSettings.IsOneWay)
            {
                allReturnDays = await _timetableClient.GetFlyingDays(route.Arrival, route.Departure, jobSettings.Airlines);
                // log($"{route.Arrival}-{route.Departure}: {allReturnDays.Count}");
            }

            result.Add(new Itinerary(route, allDepartureDays.AsReadOnly(), allReturnDays.AsReadOnly()));
        }

        return result;
    }
}