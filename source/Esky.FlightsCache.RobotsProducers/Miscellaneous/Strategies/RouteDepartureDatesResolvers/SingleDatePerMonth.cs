using Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDatesResolvers;
using System;
using System.Collections.Generic;
using System.Linq;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDepartureDatesResolvers;

public class SingleDatePerMonth : IRouteDatesResolver
{
    private readonly TimeProvider _timeProvider;

    public SingleDatePerMonth(TimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    public IReadOnlyCollection<RouteDate> Resolve(JobSettings jobSettings, Itinerary itinerary)
    {
        var today = _timeProvider.GetUtcNow().Date;
        var excludedRanges = jobSettings.Exclusions.Select(x => x.Range(today)).ToArray();
        var groupedByMonth = from departureDate in itinerary.DepartureDates
                             group departureDate by new { departureDate.Year, departureDate.Month } into g
                             select new { g.Key.Year, g.Key.Month };
        return groupedByMonth
            .Where(d => !excludedRanges.Any(range => RangeContainsMonth(range, d.Year, d.Month)))
            .Select(x => new RouteDate(itinerary.Route, new DateTime(x.Year, x.Month, 1), new DateTime(x.Year, x.Month, 2)))
            .ToArray();
    }

    private bool RangeContainsMonth(DateRange range, int year, int month)
    {
        var monthStart = new DateTime(year, month, 1);
        var monthEnd = monthStart.AddMonths(1).AddDays(-1);
        return range.Start <= monthEnd && range.End >= monthStart;
    }
}