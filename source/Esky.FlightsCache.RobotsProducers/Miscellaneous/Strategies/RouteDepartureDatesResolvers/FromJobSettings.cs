using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous.Strategies.RouteDepartureDatesResolvers;

public class FromJobSettings : IItineraryResolver
{
    private readonly TimeProvider _timeProvider;

    public FromJobSettings(TimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    public bool MatchReturnDate => false;

    public Task<IReadOnlyCollection<Itinerary>> Resolve(JobSettings jobSettings, Action<string> log)
    {
        log($"Routes [{string.Join(',', jobSettings.Routes.Select(r => r.ToString()))}]");

        var today = _timeProvider.GetUtcNow().Date;
        var range = jobSettings.Range(today).WithStartNotBefore(today);
        var dates = range.ToArray();

        var result = jobSettings.Routes.Select(route => new Itinerary(route, dates, dates)).ToArray();
        return Task.FromResult<IReadOnlyCollection<Itinerary>>(result);
    }
}