using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

public record RelativeDateRange(RelativeDate Start, RelativeDate End)
{
    public OverlapResult Overlaps(RelativeDateRange other, DateTime referenceDate)
    {
        var allRelativeOrAbsolute =
            (
                Start.IsRelative
                && End.IsRelative
                && other.Start.IsRelative
                && other.End.IsRelative
            )
            || 
            (
                !Start.IsRelative
                && !End.IsRelative
                && !other.Start.IsRelative
                && !other.End.IsRelative
            );

        var first = this.Range(referenceDate);
        var second = other.Range(referenceDate);
        var overlaps = first.Overlaps(second);

        if (allRelativeOrAbsolute)
        {
            return new OverlapResult(overlaps, null);
        }

        (first, second) = first.Start <= second.Start
            ? (first, second)
            : (second, first);

        var daysDiff = (second.Start - first.End).Days;
        return new OverlapResult(overlaps, daysDiff);
    }

    public override string ToString() => $"{Start}-{End}";

    public static implicit operator RelativeDateRange(Range range) => new(range.Start.Value, range.End.Value);
}

public readonly record struct OverlapResult(bool Overlaps, int? OverlapsInDays);