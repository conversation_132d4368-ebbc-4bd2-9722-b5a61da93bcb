using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

[DebuggerDisplay("{Start}->{End}")]
public readonly record struct DateRange(DateTime Start, DateTime End) : IEnumerable<DateTime>
{
    public bool Overlaps(DateRange range)
    {
        return Start <= range.End && range.Start <= End;
    }

    public bool Contains(DateTime dateTime)
    {
        return Start <= dateTime && dateTime <= End;
    }

    public DateRange WithStartNotBefore(DateTime referenceDate)
    {
        if (Start < referenceDate)
        {
            return this with { Start = referenceDate };
        }

        return this;
    }

    public IEnumerator<DateTime> GetEnumerator()
    {
        for (var date = Start; date <= End; date = date.AddDays(1))
        {
            yield return date;
        }
    }

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
}