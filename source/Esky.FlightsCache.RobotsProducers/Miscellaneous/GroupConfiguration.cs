using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

public interface IOverlapping : IRelativeRange
{
    RelativeDateRange[] Exclusions { get; }
}

public interface IJobConverter<TGroup, TJobSettings>
{
    TGroup[] FromJobs(IEnumerable<TJobSettings> parameters);
    IEnumerable<(string JobName, TJobSettings JobSettings)> ToJobs(DateTime referenceDate, TGroup group);
}

public class GroupConfigurationConverter : IJobConverter<GroupConfiguration, JobSettings>
{
    public GroupConfiguration[] FromJobs(IEnumerable<JobSettings> settings)
    {
        return settings.GroupBy(e => e.Group)
            .Select(e =>
            {
                var first = e.First();
                return new GroupConfiguration
                {
                    Name = e.Key,
                    Configurations = e.Select(GetConfiguration).ToArray(),
                    Supplier = first.Supplier,
                    Flex = first.Flex,
                    Airlines = first.Airlines,
                    DepartureAirports = first.DepartureAirports,
                    ExcludedDepartureAirports = first.ExcludedDepartureAirports,
                    ArrivalAirports = first.ArrivalAirports,
                    Routes = first.Routes,
                    IsOneWay = first.IsOneWay,
                    IgnoreTimetables = first.IgnoreTimetables,
                    StayLengths = first.StayLengths,
                    ActiveFromHour = first.ActiveFrom?.Hour,
                    ActiveToHour = first.ActiveTo?.Hour,
                    SkipTheSameDepartureDateAsReturnDeparture = first.SkipTheSameDepartureDateAsReturnDeparture,
                    UseCalendar = first.UseCalendar
                };
            })
            .ToArray();

        Configuration GetConfiguration(JobSettings settings1) => new()
        {
            Cron = settings1.Cron,
            RelativeDateRange = settings1.RelativeDateRange,
            MaxProcessingWindow = settings1.MaxProcessingWindow,
        };
    }
    public IEnumerable<(string JobName, JobSettings JobSettings)> ToJobs(DateTime referenceDate, GroupConfiguration group)
    {
        var jobs = group.Configurations
            .Select((c, i) => GetJobSettings(group, c, i.ToString()))
            .ToArray();

        for (var i = 0; i < jobs.Length; i++)
        {
            var job = jobs[i];
            for (var j = i + 1; j < jobs.Length; j++)
            {
                jobs[j] = jobs[j] with { Exclusions = [.. jobs[j].Exclusions, job.RelativeDateRange] };
            }
        }

        return jobs.Select((s, i) => (group.GetJobName(i), s));
    }

    private JobSettings GetJobSettings(GroupConfiguration group, Configuration configuration, string suffix) => new()
    {
        Group = group.Name,
        Suffix = suffix,
        Cron = configuration.Cron,
        RelativeDateRange = configuration.RelativeDateRange,
        MaxProcessingWindow = configuration.MaxProcessingWindow,
        DepartureAirports = group.DepartureAirports,
        ExcludedDepartureAirports = group.ExcludedDepartureAirports,
        ArrivalAirports = group.ArrivalAirports,
        Routes = group.Routes,
        Airlines = group.Airlines,
        Flex = group.Flex,
        Supplier = group.Supplier,
        Exclusions = [],
        IsOneWay = group.IsOneWay,
        IgnoreTimetables = group.IgnoreTimetables,
        UseCalendar = group.UseCalendar,
        StayLengths = group.StayLengths,
        ActiveFrom = group.ActiveFromHour.HasValue ? new TimeOnly(group.ActiveFromHour.Value, 0) : null,
        ActiveTo = group.ActiveToHour.HasValue ? new TimeOnly(group.ActiveToHour.Value, 0) : null,
        SkipTheSameDepartureDateAsReturnDeparture = group.SkipTheSameDepartureDateAsReturnDeparture,
    };
}

public interface IGroup
{
    public bool IsValid(out IReadOnlyCollection<string> errors);
    public IEnumerable<OverlapCheckResult> CheckForOverlaps(DateTime referenceDate);
    public IEnumerable<string> GetJobNames();
}

public class GroupConfiguration : IGroup
{
    private const string _prefix = "Group";
    private const int _maxConfigs = 10;
    public required string Name { get; init; }
    public required Configuration[] Configurations { get; init; }
    public string Supplier { get; init; }
    public string[] Airlines { get; init; } = [];
    public int Flex { get; init; }
    public string[] DepartureAirports { get; init; } = [];
    public string[]? ExcludedDepartureAirports { get; init; } = [];
    public string[] ArrivalAirports { get; init; } = [];
    public Route[] Routes { get; init; } = [];
    public bool IsOneWay { get; init; }
    public bool IgnoreTimetables { get; init; }
    public bool UseCalendar { get; set; }
    public int[] StayLengths { get; init; } = [];
    public int? ActiveFromHour { get; init; }
    public int? ActiveToHour { get; init; }
    public bool SkipTheSameDepartureDateAsReturnDeparture { get; init; }

    public bool IsValid(out IReadOnlyCollection<string> errors)
    {
        var errorList = new List<string>();
        errors = errorList;

        if (string.IsNullOrWhiteSpace(Supplier))
        {
            errorList.Add($"{nameof(Supplier)} is empty");
        }

        if (Configurations.Length is 0 or > _maxConfigs)
        {
            errorList.Add($"{nameof(Configurations)} must be between 1 and 10");
        }

        if (Array.Exists(Configurations, e => !e.Valid()))
        {
            errorList.Add("some configuration is not valid");
        }

        if (IgnoreTimetables)
        {
            if (Routes.Length == 0) errorList.Add($"{nameof(Routes)} cannot be empty when {nameof(IgnoreTimetables)} is true");
            if (DepartureAirports.Length != 0) errorList.Add($"{nameof(DepartureAirports)} should be empty when {nameof(IgnoreTimetables)} is true");
            if (ArrivalAirports.Length != 0) errorList.Add($"{nameof(ArrivalAirports)} should be empty when {nameof(IgnoreTimetables)} is true");
        }
        else
        {
            if (Airlines.Length is 0)
            {
                errorList.Add($"{nameof(Airlines)} are empty");
            }
        }

        if (IsOneWay && StayLengths.Length is not 0)
        {
            errorList.Add($"{nameof(StayLengths)} should be empty when {nameof(IsOneWay)} is true");
        }

        if (ActiveFromHour.HasValue && (ActiveFromHour.Value < 0 || ActiveFromHour >= 24))
        {
            errorList.Add($"{nameof(ActiveFromHour)} should be between 0 and 24");
        }

        if (ActiveToHour.HasValue && (ActiveToHour.Value < 0 || ActiveToHour >= 24))
        {
            errorList.Add($"{nameof(ActiveToHour)} should be between 0 and 24");
        }

        return errorList.Count == 0;
    }

    public IEnumerable<OverlapCheckResult> CheckForOverlaps(DateTime referenceDate) =>
        Configurations.CheckForOverlaps(referenceDate);

    public static IEnumerable<string> GetJobNames(string groupName)
    {
        return Enumerable.Range(0, _maxConfigs).Select(index => GetJobName(groupName, index));
    }

    private static string GetJobName(string groupName, int index) => $"{_prefix}_{groupName}_{index}";
    public string GetJobName(int index) => GetJobName(Name, index);
    public IEnumerable<string> GetJobNames() => GetJobNames(Name);

}

public record Configuration : IRelativeRange
{
    public required string Cron { get; init; }
    public required RelativeDateRange RelativeDateRange { get; init; }
    public TimeSpan? MaxProcessingWindow { get; init; }

    public bool Valid()
    {
        try
        {
            Cronos.CronExpression.Parse(Cron);
        }
        catch (Exception)
        {
            return false;
        }

        return MaxProcessingWindow is null || MaxProcessingWindow > TimeSpan.Zero;
    }
}

public readonly record struct OverlapCheckResult(bool Overlaps, int? OverlapsInDays, int JobIndex1, int JobIndex2);

public record JobSettings : IOverlapping
{
    public required string Group { get; init; }
    public required string Suffix { get; init; }
    public required string Cron { get; init; }
    public required RelativeDateRange RelativeDateRange { get; init; }
    public required string Supplier { get; init; }
    public required string[] Airlines { get; init; } = [];
    public int Flex { get; init; }
    public string[] DepartureAirports { get; init; } = [];
    public string[]? ExcludedDepartureAirports { get; init; } = [];
    public string[] ArrivalAirports { get; init; } = [];
    public Route[] Routes { get; init; } = [];
    public TimeSpan? MaxProcessingWindow { get; init; }
    public RelativeDateRange[] Exclusions { get; init; } = [];
    public bool IsOneWay { get; init; }
    public bool IgnoreTimetables { get; init; }
    public bool SkipTheSameDepartureDateAsReturnDeparture { get; init; }
    public int[] StayLengths { get; init; } = [];
    public TimeOnly? ActiveFrom { get; init; }
    public TimeOnly? ActiveTo { get; init; }
    public bool UseCalendar { get; set; }

    public bool Contains(Route route)
    {
        var isIncluded = (DepartureAirports.Length is 0 && ArrivalAirports.Length is 0 && Routes.Length is 0)
                         || Routes.Contains(route)
                         || DepartureAirports.Contains(route.Departure)
                         || ArrivalAirports.Contains(route.Arrival);

        var isExcluded = ExcludedDepartureAirports?.Contains(route.Departure) ?? false;

        return isIncluded && !isExcluded;
    }

    public bool IsWithinActivityPeriod(TimeOnly time)
    {
        return time.IsBetween(ActiveFrom ?? TimeOnly.MinValue, ActiveTo ?? TimeOnly.MaxValue);
    }

    public override string ToString() => $"[{Supplier}][{string.Join(',', Airlines)}] Days:{RelativeDateRange}{(Flex > 0 ? $"(+/-{Flex})" : string.Empty)}, Departures:{DepartureAirports.Length}, Arrivals:{ArrivalAirports.Length}, Routes:{Routes.Length}, {MaxProcessingWindow}";
}

public static class GroupConfigurationExtensions
{
    public static IEnumerable<DateRange> GetExclusionRanges(this IOverlapping overlapping, DateTime referenceDate)
    {
        foreach (var (start, end) in overlapping.Exclusions)
        {
            var excludedStart = start.GetDate(referenceDate);
            var excludedEnd = end.GetDate(excludedStart);
            var excludedRange = new DateRange(excludedStart, excludedEnd);
            yield return excludedRange;
        }
    }

    public static IEnumerable<DateTime> WithoutOverlapped(this IOverlapping overlapping, IEnumerable<DateTime> dates, DateTime referenceDate)
    {
        var jobRange = overlapping.RelativeDateRange.Range(referenceDate).WithStartNotBefore(referenceDate);
        var allowedDates = overlapping.GetExclusionRanges(referenceDate)
            .Aggregate(dates, (current, excludedRange) => current.Where(day => !excludedRange.Contains(day)))
            .Where(jobRange.Contains);

        return allowedDates;
    }
}