using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Miscellaneous;

public static class Extensions
{
    public static DateRange Range(this Configuration settings, DateTime referenceDate)
    {
        return settings.RelativeDateRange.Range(referenceDate);
    }

    public static DateRange Range(this JobSettings jobSettings, DateTime referenceDate)
    {
        return jobSettings.RelativeDateRange.Range(referenceDate);
    }

    public static DateRange Range(this RelativeDateRange relativeDateRange, DateTime referenceDate)
    {
        return Range(relativeDateRange.Start, relativeDateRange.End, referenceDate);
    }

    public static DateRange Range(this RelativeDate start, RelativeDate end, DateTime referenceDate)
    {
        var startDate = start.GetDate(referenceDate);
        var endDate = end.GetDate(startDate);
        return new DateRange(startDate, endDate);
    }

    public static DateRange Range(this DateTime start, DateTime end)
    {
        return new DateRange(start, end);
    }

    public static OverlapResult Overlaps(this IRelativeRange self, IRelativeRange other, DateTime referenceDate)
    {
        return self.RelativeDateRange.Overlaps(other.RelativeDateRange, referenceDate);
    }
    
    public static IEnumerable<OverlapCheckResult> CheckForOverlaps(this IRelativeRange[] configurations, DateTime referenceDate)
    {
        for (var i = 0; i < configurations.Length; i++)
        {
            var job = configurations[i];
            for (var j = i + 1; j < configurations.Length; j++)
            {
                var result = job.Overlaps(configurations[j], referenceDate);
                yield return new OverlapCheckResult(result.Overlaps, result.OverlapsInDays, i, j);
            }
        }
    }

    /// <summary>
    /// applies flex to departure and return departure dates
    /// in such way that dates after flex must be in provided dates
    /// </summary>
    /// <param name="self">for proper handling it should be for one route</param>
    /// <param name="flex">flex in days, no effect when less or equal to 0 </param>
    /// <returns>new RouteDate enumeration with flex applied when flex > 0 otherwise self</returns>
    public static IEnumerable<RouteDate> ApplyFlex(this IEnumerable<RouteDate> self, int flex)
    {
        return flex > 0 ? Flex() : self;

        IEnumerable<RouteDate> Flex()
        {
            var ordered = self
                .OrderBy(t => t.DepartureDay)
                .ThenBy(t => t.ReturnDepartureDay)
                .ToArray();

            DateTime lastReturnedStartDate = default;
            DateTime? lastReturnedEndDate = default;
            var i = -1;
            foreach (var (route, start, end) in ordered)
            {
                i++;

                if (start <= lastReturnedStartDate.AddDays(flex)
                    && (lastReturnedEndDate is null || end <= lastReturnedEndDate.Value.AddDays(flex)))
                {
                    continue;
                }

                lastReturnedStartDate = start.AddDays(flex);
                lastReturnedEndDate = end?.AddDays(flex);

                if (!Array.Exists(ordered, e => e.DepartureDay == lastReturnedStartDate)
                    && i < ordered.Length - 1
                    && ordered[i + 1].DepartureDay < lastReturnedStartDate)
                {
                    lastReturnedStartDate = ordered[i + 1].DepartureDay;
                }

                if (!Array.Exists(ordered, e => e.DepartureDay == lastReturnedStartDate))
                {
                    lastReturnedStartDate = start;
                }

                if (!Array.Exists(ordered, e => e.ReturnDepartureDay == lastReturnedEndDate)
                    && i < ordered.Length - 1
                    && ordered[i + 1].ReturnDepartureDay < lastReturnedEndDate)
                {
                    lastReturnedEndDate = ordered[i + 1].ReturnDepartureDay;
                }

                if (!Array.Exists(ordered, e => e.ReturnDepartureDay == lastReturnedEndDate))
                {
                    lastReturnedEndDate = end;
                }

                yield return new RouteDate(route, lastReturnedStartDate, lastReturnedEndDate);
            }
        }
    }

    public delegate TResult ReturnDepartureNextDayResultSelector<in TSource, out TResult>(TSource source, DateTime departureDay, DateTime returnDepartureDay);
    public static IEnumerable<TResult> ReturnDepartureNextDay<TSource, TResult>(
        this IEnumerable<TSource> source,
        Func<TSource, IReadOnlyCollection<DateTime>> departureDatesSelector,
        Func<TSource, IReadOnlyCollection<DateTime>> returnDatesSelector,
        ReturnDepartureNextDayResultSelector<TSource, TResult> resultSelector)
    {
        var rd = new List<(TSource Source, DateTime DepartureDay, DateTime ReturnDepartureDay)>();

        foreach (var input in source.Where(e => departureDatesSelector(e).Count > 0))
        {
            var departures = departureDatesSelector(input).OrderBy(x => x).ToArray();
            var returnDepartures = returnDatesSelector(input).OrderBy(x => x).ToArray();

            var firstDeparture = departures.First();
            if (returnDepartures.Contains(firstDeparture))
            {
                rd.Add((input, firstDeparture, firstDeparture));
                if(departures.Length == 1) continue;
            }
            
            foreach (var departure in departures)
            {
                var returnDeparture = returnDepartures.FirstOrDefault(x => x > departure);
                if (returnDeparture == default)
                {
                    returnDeparture = returnDepartures.FirstOrDefault(x => x >= departure);
                    if (returnDeparture == default) continue;
                }
                rd.Add((input, departure, returnDeparture));
            }
        }

        var elements = rd
            .OrderBy(e => e.DepartureDay)
            .Select(e => resultSelector(e.Source, e.DepartureDay, e.ReturnDepartureDay))
            .Where(e => e != null);

        return elements;
    }
}