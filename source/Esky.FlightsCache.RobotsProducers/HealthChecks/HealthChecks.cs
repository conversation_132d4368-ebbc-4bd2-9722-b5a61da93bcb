using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Esky.FlightsCache.RobotsProducers.HealthChecks
{
    public static class HealthChecks
    {
        internal static class Tags
        {
            public const string Readiness = "readiness";
            public const string Liveness = "liveness";
        }

        public static IServiceCollection AddCustomHealthCheck(this IServiceCollection services, IConfiguration configuration)
        {
            var hcBuilder = services.AddHealthChecks();

            hcBuilder.AddCheck("self", () => HealthCheckResult.Healthy(), new[] { Tags.Liveness });

            return services;
        }

        public static IApplicationBuilder UseCustomHealthChecks(this IApplicationBuilder app)
        {
            return app
                .UseHealthChecks("/health/ready", new HealthCheckOptions
                {
                    Predicate = r => true
                })
                .UseHealthChecks("/health/live", new HealthCheckOptions
                {
                    Predicate = r => r.Tags.Contains(Tags.Liveness)
                });
        }
    }
}