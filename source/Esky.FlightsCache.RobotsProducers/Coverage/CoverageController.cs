using Esky.FlightsCache.RobotsProducers.Examples;
using Hangfire;
using Hangfire.Storage;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsProducers.Coverage;

[ApiController]
[Route("api/Robots/Coverage")]
public class CoverageController(IRecurringJobManager jobManager) : ControllerBase
{
    [HttpGet]
    public IEnumerable<CoverageRobotSettings> GetCoverageRobots([FromServices] JobStorage storage)
    {
        var recurringJobs = storage.GetConnection().GetRecurringJobs();
        var configs = recurringJobs
            .Where(e => e.Job?.Args.Any(a => a?.GetType() == typeof(List<CoverageRobotSettings>)) ?? false)
            .SelectMany(e => (e.Job.Args.First(a => a?.GetType() == typeof(List<CoverageRobotSettings>)) as List<CoverageRobotSettings>)!);
        
        return configs;
    }

    /// <summary>
    /// Update robots to check cache coverage for airlines. Use GET to get current settings!
    /// </summary>
    /// <param name="cron">Cron</param>
    /// <param name="configs">Job settings airlines + provider/supplier pairs (use cache provider codes)</param>
    [HttpPut]
    [Route("{cron}")]
    [SwaggerRequestExample(typeof(IEnumerable<CoverageRobotSettings>), typeof(CoverageRequestExamples))]
    public void PutCoverageRobot([FromBody] IEnumerable<CoverageRobotSettings> configs, [FromRoute] string cron = "0 0 * * *")
    {
        jobManager.AddOrUpdate<CoverageRobot>(
            "CoverageRobot",
            producer => producer.ScheduleJobs(configs),
            cron,
            TimeZoneInfo.Utc
        );
    }
}