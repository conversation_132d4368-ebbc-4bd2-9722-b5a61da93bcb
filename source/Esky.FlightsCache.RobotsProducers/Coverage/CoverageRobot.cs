using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.Robots.BigQuery;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Route = Esky.FlightsCache.RobotsProducers.Miscellaneous.Route;

namespace Esky.FlightsCache.RobotsProducers.Coverage;

public sealed class CoverageRobot(
    ITimetableServiceClient timetableClient,
    IFlightOffersRepository flightOffersRepository,
    IAirportsRepository airportsRepository,
    ICoverageStorage coverageStorage)
{
    public void ScheduleJobs(IEnumerable<CoverageRobotSettings> configs)
    {
        string? parentJobId = null;
        foreach (var config in configs)
        {
            if (string.IsNullOrEmpty(parentJobId))
            {
                parentJobId = BackgroundJob.Enqueue(() => CheckCoverage(config.Airlines, config, null));
            }
            else
            {
                parentJobId = BackgroundJob.ContinueJobWith(parentJobId, () => CheckCoverage(config.Airlines, config, null), JobContinuationOptions.OnAnyFinishedState);
            }
        }
    }

    [DisplayName("[Coverage] Airlines: {0}")]
    public async Task CheckCoverage(string airlines, CoverageRobotSettings settings, PerformContext? context)
    {
        var airlinesTab = airlines.Split(',');
        var connectionNetworksTasks = airlinesTab.Select(async airline => await timetableClient.GetConnectionNetworkByAirline(airline));
        var connectionNetworks = (await Task.WhenAll(connectionNetworksTasks)).SelectMany(x => x);

        var distinctRoutes = connectionNetworks
            .SelectMany(
                connection => connection.ArrivalAirportCodes,
                (connection, arrival) => new Route(connection.DepartureAirportCode, arrival.ArrivalCode))
            .ToHashSet();

        context?.WriteLine("Found {0} routes for airlines {1}", distinctRoutes.Count, airlines);
        var bar = context?.WriteProgressBar();

        var results = new List<CoverageResult>();
        var i = 0;

        foreach (var route in distinctRoutes.WithProgress(bar))
        {
            try
            {
                if(await airportsRepository.IsMultiportAsync(route.Departure) || await airportsRepository.IsMultiportAsync(route.Arrival)) continue;
            
                var timetableDays = (await timetableClient.GetFlyingDays(route.Departure, route.Arrival, airlinesTab)).ToHashSet();

                foreach (var ps in settings.ProviderSuppliers)
                {
                    var cacheDays = await flightOffersRepository.GetDepartureDatesOW(ps.Provider, ps.Supplier, route.ToString());

                    var departureCountry = await airportsRepository.GetCountryAsync(route.Departure);
                    var arrivalCountry = await airportsRepository.GetCountryAsync(route.Arrival);

                    results.Add(new CoverageResult
                    {
                        Airline = airlines,
                        Provider = ps.Provider,
                        Supplier = ps.Supplier,
                        Departure = route.Departure,
                        Arrival = route.Arrival,
                        DepartureCountry = departureCountry,
                        ArrivalCountry = arrivalCountry,
                        CacheDays = cacheDays,
                        TimetableDays = timetableDays
                    });

                    i++;

                    if (i % 100 == 0)
                    {
                        await coverageStorage.SaveCoverageAsync(results);
                        results.Clear();
                    }
                }
            }
            catch (Exception ex)
            {
                context?.WriteLine("Error processing route {0}: {1}", route, ex.Message);
            }
        }
        
        await coverageStorage.SaveCoverageAsync(results);
    }
}