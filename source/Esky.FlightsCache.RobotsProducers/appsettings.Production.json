{"ExternalServices": {"TimeTableService": {"Url": "http://esky-timetables-api.query.consul./"}, "FlightsCacheService": {"Url": "http://esky-flightscache-api.query.consul./"}}, "PartnerSettings": {"Environment": "pro"}, "CacheDemandSettings": {"GoogleCredentialsPath": "/creds/account.json"}, "BigQuerySettings": {"ProjectId": "esky-ets-logs-pro", "GoogleCredentialsPath": "/creds/account.json"}, "RedisCaching": {"ConnectionString": "secret"}, "HangfireSettings": {"ConnectionString": "secret", "WorkerCount": 5, "MaxDegreeOfParallelismForSchedulers": 5}, "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "System.Net.Http.HttpClient.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "<PERSON><PERSON>*", "minLevel": "<PERSON><PERSON>", "writeTo": "rabbit", "final": true}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}