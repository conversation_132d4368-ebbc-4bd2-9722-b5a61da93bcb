using Hangfire;
using Microsoft.AspNetCore.Mvc;

namespace Esky.FlightsCache.RobotsProducers.Jet2;

[ApiController]
[Route("api/Jet2")]
public class Jet2Controller : ControllerBase
{
    [HttpPost]
    public IActionResult Post([FromBody] Producer.Settings settings, [FromServices] IRecurringJobManager recurringJobManager)
    {
        recurringJobManager.AddOrUpdate<Producer>(
            Producer.JobName,
            p => p.Produce(settings, default, default),
            settings.CronExpression
        );
        return Ok();
    }

    [HttpDelete]
    public IActionResult Delete([FromServices] IRecurringJobManager recurringJobManager)
    {
        recurringJobManager.RemoveIfExists(Producer.JobName);
        return Ok();
    }
}