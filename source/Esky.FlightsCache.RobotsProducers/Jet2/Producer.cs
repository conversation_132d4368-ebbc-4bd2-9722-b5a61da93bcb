using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsProducers.Publishers;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.Jet2;

public class Producer
{
    internal static readonly string JobName = "Jet2";

    private readonly ITimetableServiceClient _timetableClient;
    private readonly IQueueElementPublisherBuilder<Jet2QueueElement> _publisherBuilder;

    public Producer(ITimetableServiceClient timetableClient, IQueueElementPublisherBuilder<Jet2QueueElement> publisherBuilder)
    {
        _timetableClient = timetableClient;
        _publisherBuilder = publisherBuilder;
    }

    public async Task Produce(Settings settings, PerformContext? performContext, CancellationToken cancellationToken)
    {
        var connectionNetwork = await _timetableClient.GetConnectionNetworkByProviderCode((int)ProviderCodeEnum.Jet2, []);
        const int daysForward = 900;
        var queueElements = connectionNetwork
            .SelectMany(e => e.ArrivalAirportCodes.SelectMany(arrival => new[]
                {
                    Jet2QueueElement.ForFlightsOnRoute(e.DepartureAirportCode, arrival.ArrivalCode, daysForward, isRoundTrip: false),
                    Jet2QueueElement.ForFlightsOnRoute(e.DepartureAirportCode, arrival.ArrivalCode, daysForward, isRoundTrip: true)
                }))
            .ToArray();

        var publisher = _publisherBuilder
            .WithEvenlyDistribution(queueElements.Length)
            .WithLogger(performContext is null ? Console.WriteLine : performContext.WriteLine)
            .WithCron(settings.CronExpression)
            .WithJobName(JobName)
            .Build();
        await publisher.Publish(queueElements, cancellationToken);
        await publisher.Complete(cancellationToken);
    }

    public record Settings(string CronExpression);
}