using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.DirectRyanair;

public class RyanAirDirectDailyProducer
{
    private readonly TimeProvider _timeProvider;
    private readonly IConnectionNetworkProvider _connectionNetworkProvider;
    private readonly IQueuePublisher _queuePublisher;

    public RyanAirDirectDailyProducer(TimeProvider timeProvider, IConnectionNetworkProvider connectionNetworkProvider, IQueuePublisher queuePublisher)
    {
        _timeProvider = timeProvider;
        _connectionNetworkProvider = connectionNetworkProvider;
        _queuePublisher = queuePublisher;
    }

    internal static string GetJobName(int startingDayOffset, int numberOfDaysForward) => $"RyanairDirect.Daily<{startingDayOffset}>";

    [DisplayName("RyanairDirect.Daily<{0};+{1})")]
    public async Task ProduceDailyForward(int startingDayOffset, int numberOfDaysForward)
    {
        var startingDay = _timeProvider.GetUtcNow().Date.AddDays(startingDayOffset);
        var endDay = startingDay.AddDays(numberOfDaysForward);
        var connectionNetwork = await _connectionNetworkProvider.GetConnectionNetwork(["FR"]);

        var elements = connectionNetwork
            .SelectMany(route => route.FlyingDates.Select(date => new { route.DepartureCode, route.ArrivalCode, Date = date }))
            .OrderBy(e => e.Date)
            .Where(e => startingDay <= e.Date && e.Date < endDay)
            .Select(
                e => new RyanAirDirectQueueElement
                {
                    DepartureCode = e.DepartureCode,
                    ArrivalCode = e.ArrivalCode,
                    DepartureDay = e.Date,
                    PartnerCode = "ADMIN",
                    DeleteDepartureDayFrom = e.Date,
                    DeleteDepartureDayTo = e.Date,
                    ProviderCode = ProviderCodeEnum.DirectRyanair
                }
            );

        await _queuePublisher.Publish(elements, CancellationToken.None);
    }
}
