using Esky.FlightsCache.Database.Repositories;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Producers;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire.Console;
using Hangfire.Server;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsProducers.DirectRyanair;

public class RyanAirDirectLowAvailabilityProducer
{
    private readonly IFlightOffersRepository _dbRepository;
    private readonly IQueueElementCreator _queueElementCreator;
    private readonly IQueuePublisher _queuePublisher;

    public RyanAirDirectLowAvailabilityProducer(
        IFlightOffersRepository dbRepository,
        IQueueElementCreator queueElementCreator,
        IQueuePublisher queuePublisher)
    {
        _dbRepository = dbRepository;
        _queueElementCreator = queueElementCreator;
        _queuePublisher = queuePublisher;
    }

    internal static string JobName => "RyanairDirect.LowAvailability";

    [DisplayName("RyanairDirect.LowAvailability")]
    public async Task Publish(int availableSeatsCount, int daysForward, PerformContext? context)
    {
        var elements = await GenerateQueueElements(availableSeatsCount, daysForward, context);
        await _queuePublisher.Publish(elements, CancellationToken.None);
    }

    private async Task<List<RyanAirDirectQueueElement>> GenerateQueueElements(int availableSeatsCount, int daysForward, PerformContext? context)
    {
        var items = await _dbRepository.GetDirectRyanairLowAvailabilityItems(availableSeatsCount, daysForward);

        var elements = items
            .Select(CreateQueueElement)
            .ToList();
        context?.WriteLine($"Elements to publish: {elements.Count}");

        return elements;
    }

    private RyanAirDirectQueueElement CreateQueueElement((string Route, DateTime DepartureDate) key)
    {
        var codes = key.Route.Split('-');
        return _queueElementCreator
            .CreateOneWayElement(
                ProviderCodeEnum.DirectRyanair,
                RobotConstants.PARTNER_CODE,
                departureCode: codes[0],
                arrivalCode: codes[1],
                key.DepartureDate
            )
            .ConfigureDeleteForFlex()
            .ConfigureSourceName("DirectRyanairConsumer_LowAvailability")
            .Resolve()
            .To<RyanAirDirectQueueElement>();
    }
}