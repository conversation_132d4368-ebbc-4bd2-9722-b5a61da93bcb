#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
ENV ASPNETCORE_HTTP_PORTS=80
WORKDIR /app
RUN apt-get update && apt-get install -y libc-dev && apt-get clean
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

COPY ["nuget.config", ""]
COPY ["source/Esky.FlightsCache.RobotsProducers/Esky.FlightsCache.RobotsProducers.csproj", "source/Esky.FlightsCache.RobotsProducers/"]
COPY ["source/Esky.FlightsCache.PartnerSettings/Esky.FlightsCache.PartnerSettings.csproj", "source/Esky.FlightsCache.PartnerSettings/"]
COPY ["source/Esky.FlightsCache.Robots.Messages/Esky.FlightsCache.Robots.Messages.csproj", "source/Esky.FlightsCache.Robots.Messages/"]
COPY ["source/Esky.FlightsCache.Robots/Esky.FlightsCache.Robots.csproj", "source/Esky.FlightsCache.Robots/"]
COPY ["source/Esky.FlightsCache.CurrencyProvider/Esky.FlightsCache.CurrencyProvider.csproj", "source/Esky.FlightsCache.CurrencyProvider/"]
COPY ["source/Esky.FlightsCache.Database/Esky.FlightsCache.Database.csproj", "source/Esky.FlightsCache.Database/"]
COPY ["source/Esky.FlightsCache.ProviderMapping/Esky.FlightsCache.ProviderMapping.csproj", "source/Esky.FlightsCache.ProviderMapping/"]
RUN dotnet restore "source/Esky.FlightsCache.RobotsProducers/Esky.FlightsCache.RobotsProducers.csproj"
COPY . .
WORKDIR "/src/source/Esky.FlightsCache.RobotsProducers"
RUN dotnet build "Esky.FlightsCache.RobotsProducers.csproj" -c Release -o /app/build

FROM build AS test
WORKDIR "/src/"
COPY ["source/Esky.FlightsCache.Tests/Esky.FlightsCache.Tests.csproj", "source/Esky.FlightsCache.Tests/"]
RUN dotnet test "source/Esky.FlightsCache.Tests/Esky.FlightsCache.Tests.csproj"

FROM build AS publish
RUN dotnet publish "Esky.FlightsCache.RobotsProducers.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Esky.FlightsCache.RobotsProducers.dll"]