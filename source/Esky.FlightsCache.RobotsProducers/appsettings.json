{"FlightsCacheConfiguration": {"CollectionName": "providerConfiguration"}, "DatabaseSettings": {"ConnectionString": "secret", "DatabaseName": "RobotsProducers"}, "FlightOffersConfiguration": {"ConnectionString": "secret"}, "AirportCurrencySettings": {"ConnectionString": "secret", "UpdateIntervalInMinutes": 720}, "Basic": {"Environment": "PRO"}, "CacheDemandSettings": {"SingleLineRequestsLimit": 1500, "TotalRequestsLimit": 100000, "GoogleDirectoryId": "0AG4lzBk0o4lZUk9PVA"}, "SscRobotSettings": {"ExcludedAirportCodes": {"wizzair": ["LON", "PAR", "MIL", "CAM", "ROM", "VEN", "OOS", "STO", "BUH"]}}, "OpenTelemetry": {"ServiceName": "FlightsCache.RobotsProducers", "Metrics": "<PERSON><PERSON><PERSON><PERSON><PERSON>,FlightsCache.RobotsProducers", "Traces": "<PERSON><PERSON><PERSON><PERSON><PERSON>,FlightsCache.RobotsProducers", "Jaeger": {"SettingsPollIntervalMinutes": 5}}, "HangfireSettings": {"Prefix": "Hangfire"}, "PartnerSettings": {"ApiKey": "secret", "Environment": "pro", "System": "ets", "Url": "http://esky-partnersettings-api-green.query.consul./"}, "RobotServiceBus": {"Url": "rabbitmq://rabbitmq-flightscache.service.query.consul./ets", "Login": "ets", "Password": "5c71e08b3d5a3f4d9eda01a5e9ecc614", "UseCluster": false, "SerializationProvider": "SystemTextJson", "RabbitMqBatchPublishSettings": {"Enabled": true}}, "BigQuerySettings": {"EskyDatacHotelsProjectId": "esky-datac-hotels"}, "ServiceBus_Login": "secret", "ServiceBus_Password": "secret", "ServiceBus_Clustermembers": "secret", "ServiceBus_VHost": "secret", "NLog": {"throwConfigExceptions": true, "internalLogFile": "~\\nlog\\internal-nlog.txt", "internalLogLevel": "<PERSON><PERSON>", "internalLogToConsole": true, "autoReload": true, "extensions": [{"assembly": "Esky.NLog.RabbitMQ.Target"}], "targets": {"async": true, "rabbit": {"type": "RabbitMQ", "username": "${configsetting:item=ServiceBus_Login}", "password": "${configsetting:item=ServiceBus_Password}", "clustermembers": "${configsetting:item=ServiceBus_Clustermembers}", "vhost": "${configsetting:item=ServiceBus_VHost}", "fields": [{"key": "HostName", "name": "HostName", "layout": "${machinename}"}, {"key": "Date", "name": "Date", "layout": "${date:universalTime=False:format=s}"}, {"key": "Application", "name": "Application", "layout": "esky-flightscache-robotsproducers"}, {"key": "Environment", "name": "Environment", "layout": "${configsetting:item=ASPNETCORE_ENVIRONMENT}"}, {"key": "Exception", "name": "Exception", "layout": "${exception}"}, {"key": "AdditionalMessage", "name": "AdditionalMessage", "layout": "${exception:format=toString,Data}"}, {"key": "StackTrace", "name": "StackTrace", "layout": "${exception:format=StackTrace}"}, {"key": "Context", "name": "Provider", "layout": "${event-properties:Context}"}]}}}}