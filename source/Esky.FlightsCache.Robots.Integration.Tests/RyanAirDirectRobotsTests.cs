using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Text;
using static Esky.FlightsCache.RobotsProducers.Controllers.RyanAirDirectRobotsController;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class RyanAirDirectRobotsTests : IDisposable
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly ITestOutputHelper _output;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly HttpClient _client;
    private readonly IAirportCurrencyRepository _airportCurrencyRepository;

    public RyanAirDirectRobotsTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;

        _client = infrastructure.CreateProducersApp().CreateClient();
        _airportCurrencyRepository = infrastructure
            .CreateConsumersApp()
            .Services.CreateScope()
            .ServiceProvider.GetRequiredService<IAirportCurrencyRepository>();

        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
    }

    public void Dispose()
    {
        _rabbitMqFixture.QueuePurge(_queue);
    }

    [Fact(Skip = "mocking different responses for same path needs to be added")]
    public async void RyanAirDirect_Remaining_Seats_Price()
    {
        //Setup
        var airlineCode = "FR";
        var providerCode = 78;
        var supplier = "test_supplier";
        var departureFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date,
            Departure = "LHR",
            Destination = "KRK",
            AirlineCode = airlineCode,
            PriceAmount = 11.1m,
            Supplier = supplier
        };
        var departureFlight2 = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date,
            Departure = "LHR",
            Destination = "KRK",
            AirlineCode = airlineCode,
            PriceAmount = 22.2m,
            Supplier = supplier
        };
        var returnFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(4),
            Departure = "KRK",
            Destination = "LHR",
            AirlineCode = airlineCode,
            PriceAmount = 33.3m,
            Supplier = supplier
        };
        var returnFlight2 = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(4),
            Departure = "KRK",
            Destination = "LHR",
            AirlineCode = airlineCode,
            PriceAmount = 44.4m,
            Supplier = supplier
        };

        var flights = new List<FlightModel>
        {
            departureFlight,
            departureFlight2,
            returnFlight,
            returnFlight2
        };

        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub(flights);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent(flights);
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/connectionnetworks/airline/{airlineCode}", method: "GET");

        var route = $"{departureFlight.Departure}-{departureFlight.Destination}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent_AirlineRouteSchedule(departureFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/flyingdays?airlineCodes={airlineCode}", "GET");

        route = $"{returnFlight.Departure}-{returnFlight.Destination}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent_AirlineRouteSchedule(returnFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/flyingdays?airlineCodes={airlineCode}", "GET");

        var job = new Job(0, 10, "* * 30 2 *");
        var jobContent = JsonConvert.SerializeObject(job);

        await _airportCurrencyRepository.Update("KRK", ProviderCodeEnum.DirectRyanair.ToString(), "PLN", DateTime.UtcNow, CancellationToken.None);
        await _airportCurrencyRepository.Update("LHR", ProviderCodeEnum.DirectRyanair.ToString(), "GBP", DateTime.UtcNow, CancellationToken.None);

        //Act
        var response = await _client.PutAsync("/api/Robots/RyanAirDirect/JobConfiguration", new StringContent(jobContent, Encoding.UTF8, "application/json"));
        response.EnsureSuccessStatusCode();
        response = await _client.PostAsync("/hangfire/recurring/trigger", new FormUrlEncodedContent(
            [
                new KeyValuePair<string, string>("jobs[]", "RyanairDirect.Daily<0>")
            ]));
        response.EnsureSuccessStatusCode();

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 8).ToList();

        //Assert
        messages.Should().HaveCount(8);

        messages.ForEach(message =>
        {
            _output.WriteLine("message: " + JsonConvert.SerializeObject(messages));
            using (new AssertionScope())
            {
                message.Flights.Should().HaveCount(2);
                foreach (var flight in message.Flights)
                {
                    flight.ProviderCode.Should().Be(providerCode);
                    flight.Legs.First().AirlineCode.Should().Be(airlineCode);
                    flight.Legs.First().DepartureCode.Should().BeOneOf("LHR", "KRK");
                    flight.Legs.First().ArrivalCode.Should().BeOneOf("LHR", "KRK");
                    flight.Legs.First().DepartureDate.Should().BeOneOf(departureFlight.FlightDate, returnFlight.FlightDate);
                    flight.Legs.First().AdultPrices.Should().HaveCount(2);
                }
            }
        });
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}