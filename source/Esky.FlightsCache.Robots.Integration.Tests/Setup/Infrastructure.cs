using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Containers;
using Testcontainers.MongoDb;
using Testcontainers.RabbitMq;

namespace Esky.FlightsCache.Robots.Integration.Tests.Setup;

public sealed class Infrastructure : IAsyncLifetime
{
    private const string _mongoDbName = "RobotsProducers";
    private const int _mockServerPort = 1090;

    private readonly MongoDbContainer _mongoDb;
    private readonly RabbitMqContainer _rabbitMq;
    private readonly IContainer _mockServer;

    public Infrastructure()
    {
        _mongoDb = new MongoDbBuilder()
            .WithImage("mongo:7.0")
            .WithUsername(null)
            .WithPassword(null)
            .Build();
        _rabbitMq = new RabbitMqBuilder()
           .WithImage("masstransit/rabbitmq")
           .WithUsername("robotuser")
           .WithPassword("robotpassword")
           .WithResourceMapping(new FileInfo("rabbitmq.conf"), "/etc/rabbitmq/conf.d/")
           .WithResourceMapping(new FileInfo("definitions.json"), "/etc/rabbitmq/")
           .WithPortBinding(15672, true)
           .Build();
        _mockServer = new ContainerBuilder()
            .WithImage("mockserver/mockserver")
            .WithResourceMapping(new FileInfo("initializerJson.json"), "/config/")
            .WithEnvironment("MOCKSERVER_PROPERTY_FILE", "/config/mockserver.properties")
            .WithEnvironment("MOCKSERVER_INITIALIZATION_JSON_PATH", "/config/initializerJson.json")
            .WithEnvironment("SERVER_PORT", _mockServerPort.ToString())
            .WithPortBinding(_mockServerPort, true)
            .Build();
    }

    public string MongoConnectionString => new UriBuilder(_mongoDb.GetConnectionString()) { Path = _mongoDbName }.ToString();
    public string RabbitConnectionString => _rabbitMq.GetConnectionString();
    public Uri MockServerAddress => new UriBuilder("http", _mockServer.Hostname, _mockServer.GetMappedPublicPort(_mockServerPort)).Uri;

    public RobotsProducersApp CreateProducersApp() => new(RabbitConnectionString, MockServerAddress.ToString(), MongoConnectionString);

    public RobotsConsumersApp CreateConsumersApp() => new(RabbitConnectionString, MockServerAddress.ToString(), MongoConnectionString);

    public async Task InitializeAsync()
    {
        await Task.WhenAll(_rabbitMq.StartAsync(), _mongoDb.StartAsync(), _mockServer.StartAsync());
    }

    public Task DisposeAsync()
    {
        _ = Task.WhenAll(_rabbitMq.StopAsync(), _mongoDb.StopAsync(), _mockServer.StopAsync());
        return Task.CompletedTask;
    }
}