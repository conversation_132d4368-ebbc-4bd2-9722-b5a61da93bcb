using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsProducers.Tools;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.Robots.Integration.Tests.Setup;

public class RobotsConsumersApp : WebApplicationFactory<RobotsReceiveEndpointConnector>
{
    private readonly string _rabbitConnectionString;
    private readonly string _mockServerUrl;
    private readonly string _mongoConnectionString;
    private readonly string _testSupplier = "testsupplier";
    private readonly string _retryPartner = "RETRY_PARTNER";

    public RobotsConsumersApp(string rabbitConnectionString, string mockServerUrl, string mongoConnectionString)
    {
        _rabbitConnectionString = rabbitConnectionString;
        _mockServerUrl = mockServerUrl;
        _mongoConnectionString = mongoConnectionString;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        var config = new Dictionary<string, string?>
        {
            { "RobotServiceBus:Url", _rabbitConnectionString },
            { "PartnerSettings:Url", _mockServerUrl },
            { "ExternalServices:TimeTableService:Url", _mockServerUrl },
            { "ExternalServices:FlightsCacheService:Url", _mockServerUrl },
            { "ProviderSearch:GenericApiFlightProviderUrl", _mockServerUrl },
            { "CacheApi:Url", _mockServerUrl },
            { "AmadeusLiveCheck:ApiUrl", _mockServerUrl },
            { "AmadeusLiveCheck:RuntimeSettingsFallbackUrl", _mockServerUrl },
            { "DirectRyanair:MaxSeatsCountToCheck", "3" },
            { "SSCProvider:ApiUrl", _mockServerUrl },
            { "AirportCurrencySettings:ConnectionString", _mongoConnectionString },
        };
        builder.ConfigureTestServices(services =>
            {
                services.Replace(ServiceDescriptor.Singleton(_ => new TravelFusionConsumerSettings()
                {
                    Retry = new TravelFusionConsumerSettings.RetrySettings()
                    {
                        Conditions = [new TravelFusionConsumerSettings.RetrySettings.Condition
                        {

                            Suppliers = [_testSupplier],
                            MaxAgeInMinutes = 120,
                            DepartureDaysTo = 4,
                        }],
                        PartnerCode = _retryPartner
                    }
                }));
            });
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(config)
            .Build();
        builder.UseConfiguration(configuration);
    }
}