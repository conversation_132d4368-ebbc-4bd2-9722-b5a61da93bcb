using Meziantou.Extensions.Logging.Xunit;
using Microsoft.Extensions.Logging;

namespace Esky.FlightsCache.Robots.Integration.Tests.Setup;

public static class Extensions
{
    public static async Task<bool> TriggerHangfireRecurringJob(this HttpClient client, string jobName)
    {
        var response = await client.PostAsync(
            "/hangfire/recurring/trigger",
            new MultipartFormDataContent { { new StringContent(jobName), "jobs[]" } }
        );

        return response.IsSuccessStatusCode;
    }

    public static ILoggingBuilder AddXunitLogger(this ILoggingBuilder builder, ITestOutputHelper output)
    {
        return builder.AddProvider(
            new XUnitLoggerProvider(
                output,
                new XUnitLoggerOptions { IncludeCategory = true, IncludeScopes = true, IncludeLogLevel = true }
            )
        );
    }
}