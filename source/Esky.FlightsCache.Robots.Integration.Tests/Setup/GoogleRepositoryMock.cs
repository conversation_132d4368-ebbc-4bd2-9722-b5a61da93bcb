using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;

namespace Esky.FlightsCache.Robots.Integration.Tests.Setup
{
    public class GoogleRepositoryMock : IGoogleRepository
    {
        private readonly List<IList<object>> _dataRows = [];
        private readonly List<object> _header = ["AirlineFilter", "ProviderCode", "PartnerCode", "Departure", "Arrival", "DepartureFrom", "DepartureTo", "TripType", "MinS<PERSON>yLength", "MaxStayLength" ];
        private readonly ICacheDemandRoutesAlgorithm _algorithm;

        public GoogleRepositoryMock(ICacheDemandRoutesAlgorithm algorithm)
        {
            _algorithm = algorithm;
        }

        public int ExpectedMessageCount { get; set; }


        public Task<IEnumerable<Spreadsheet>> GetSpreadsheets()
        {
            return Task.FromResult<IEnumerable<Spreadsheet>>(
                new[]
                {
                    new Spreadsheet()
                    {
                        Id = "spreadsheetId",
                        Name = "test",
                        Directory = "testDirectory",
                        ModifiedTime = DateTime.Now,
                        LastModifyingUser = "tester",
                        Sheets = new List<Sheet>
                        {
                            new()
                            {
                                Id = 1, LastSuccessfulValidationDate = DateTime.Now, Name = "testSheet"
                            }
                        }
                    }
                });
        }

        public Task<IList<IList<object>>> GetValues(string spreadsheetId, string sheetName)
        {
            var sheetContent = new List<IList<object>>
            {
                new List<object> {"", "1 1 29 2 *", "", "", "", "" },
                _header
            };
            sheetContent.AddRange(_dataRows);
            return Task.FromResult<IList<IList<object>>>(sheetContent);
        }

        public Task SetLastSuccessfulValidationDate(string spreadsheetId, string sheetName, string value)
        {
            return Task.CompletedTask;
        }

        public Task SetTotalRequestsGenerated(string spreadsheetId, string sheetName, int value)
        {
            return Task.CompletedTask;
        }

        public Task SetRowRequestsGenerated(string spreadsheetId, string sheetName, IEnumerable<int> values, int? rowRequestsGeneratedColumn = null)
        {
            return Task.CompletedTask;
        }

        public Task SetValues(string spreadsheetId, string sheetName, int column, int rowNumber, IEnumerable<int> values)
        {
            return Task.CompletedTask;
        }

        public void PrepareDataRows(params CacheDemandModelInput[] model)
        {
            _dataRows.Clear();
            ExpectedMessageCount = 0;

            foreach (var input in model)
            {
                var row = new List<object>
                {
                    string.Join(",", input.AirlinesFilter),
                    input.ProviderCode is null ? "ALC" : (int?)input.ProviderCode,
                    input.PartnerCode,
                    input.DepartureAirportCode,
                    input.ArrivalAirportCode,
                    input.DepartureDateFrom.ToString(),
                    input.DepartureDateTo.ToString(),
                    input.IsRoundTrip ? "RT" : "OW",
                    input.MinStayLength ?? 0,
                    input.MaxStayLength ?? 0
                };
                if(input.ProviderCode is null)
                {
                    row.Add(input.OfficeId ?? "");
                    row.Add(string.Join(",", input.AlcExcludedAirlines ?? []));
                }
                _dataRows.Add(row);
                ExpectedMessageCount += input.ToJobItem(_algorithm).ElementsGenerated;
            }
        }
        public void SetAlcHeader()
        {
            _header.Add("OfficeId");
            _header.Add("AlcExcludedAirlines");
        } 
    }
}