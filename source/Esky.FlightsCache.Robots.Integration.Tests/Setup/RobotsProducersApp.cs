using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Hangfire;
using Hangfire.MemoryStorage;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace Esky.FlightsCache.Robots.Integration.Tests.Setup;

public class RobotsProducersApp : WebApplicationFactory<CacheDemandProducer>
{
    private readonly string _rabbitConnectionString;
    private readonly string _mockServerUrl;
    private readonly string _mongoConnectionString;

    public RobotsProducersApp(string rabbitConnectionString, string mockServerUrl, string mongoConnectionString)
    {
        _rabbitConnectionString = rabbitConnectionString;
        _mongoConnectionString = mongoConnectionString;
        _mockServerUrl = mockServerUrl;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        var config = new Dictionary<string, string?>
        {
            { "HangfireSettings:ConnectionString", _mongoConnectionString },
            { "RobotServiceBus:Url", _rabbitConnectionString },
            { "DatabaseSettings:ConnectionString", _mongoConnectionString },
            { "FlightsCacheConfiguration:ConnectionUrl", _mongoConnectionString },
            { "AirportCurrencySettings:ConnectionString", _mongoConnectionString },
            { "PartnerSettings:Url", _mockServerUrl },
            { "ExternalServices:TimeTableService:Url", _mockServerUrl },
            { "ExternalServices:FlightsCacheService:Url", _mockServerUrl },
            { "CacheDemandSettings:GoogleCredentialsPath", "" },
            { "RobotsProducerEvenlyDistributionSettings:MaxProcessingWindow", TimeSpan.FromSeconds(1).ToString() /*force one chunk*/ }
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(config)
            .Build();
        builder.ConfigureTestServices(services =>
        {
            services.AddHangfire(e => e.UseMemoryStorage().UseRecommendedSerializerSettings(e => e.Converters.Add(new RelativeDateJsonSerializer())));
        });
        builder.UseConfiguration(configuration);
    }
}