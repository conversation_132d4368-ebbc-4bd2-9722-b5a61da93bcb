using System.IO.Compression;
using System.Text;

namespace Esky.FlightsCache.Robots.Integration.Tests
{
    public class GzipHelper
    {
        public static string Unzip(byte[] bytes)
        {
            using var msi = new MemoryStream(bytes);
            using var mso = new MemoryStream();
            using var gs = new GZipStream(msi, CompressionMode.Decompress);

            gs.CopyTo(mso);

            return Encoding.UTF8.GetString(mso.ToArray());
        }
    }
}
