using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Newtonsoft.Json;
using System.Text;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class TravelFusionRobotsTests
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly HttpClient _client;

    public TravelFusionRobotsTests(Infrastructure infrastructure)
    {
        _client = infrastructure.CreateProducersApp().CreateClient();
        infrastructure.CreateConsumersApp().CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
    }

    [Theory]
    [InlineData("ADMIN")]
    [InlineData("TEST")]
    public async void TravelFusion_By_Multiple_Suppliers(string partnerCode)
    {
        //Setup
        var airlineCode = "FR";
        var providerCode = 58;
        var suppliers = new string[] { "wizzair", "ryanair" };
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = airlineCode,
            Supplier = suppliers[0]
        };

        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/connectionnetworks/providers/{providerCode}?suppliers={string.Join(',', suppliers)}",
            method: "GET");
        mockResponseContent = MockServerFixture.CreateTravelFusionTimetableContent([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/Timetables/travelfusion/days",
            method: "POST");

        //Act
        var response = await _client.PostAsync(
            $"/api/Robots/TravelFusion/Producers?daysToDepartureFrom=1&daysToDepartureTo=1&partnerCode={partnerCode}",
            new StringContent(JsonConvert.SerializeObject(suppliers), Encoding.UTF8, "application/json"));
        response.EnsureSuccessStatusCode();

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 1).ToList();

        //Assert
        messages.Count.Should().Be(1);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, partnerCode, paxConfiguration: "1.0.1.1");

        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}