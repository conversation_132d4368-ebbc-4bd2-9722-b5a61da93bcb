using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using System.Text;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class TravelFusionRobotsCategorizedSuppliersTests : IDisposable
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly ITestOutputHelper _output;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly IMongoCollection<TravelFusionSupplier> _suppliers;
    private readonly HttpClient _client;

    public TravelFusionRobotsCategorizedSuppliersTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;
        _client = infrastructure.CreateProducersApp().CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
        _suppliers = new MongoClient(infrastructure.MongoConnectionString)
            .GetDatabase("RobotsProducers")
            .GetCollection<TravelFusionSupplier>("TravelFusionSuppliers");
    }

    public void Dispose()
    {
        _rabbitMqFixture.QueuePurge(_queue);
        _suppliers.DeleteMany(new BsonDocument());
    }

    [Theory]
    [InlineData("*******")]
    [InlineData(null)]
    public async void TravelFusion_By_Category(string? paxConfiguration)
    {
        //Setup
        var airlineCode = "FR";
        var providerCode = 58;
        var supplier = "test_supplier";
        var flight = new FlightModel
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = airlineCode,
            Supplier = supplier,
        };
        var travelFusionSupplierRequest = new TravelFusionSupplierRequest
        {
            Category = TravelFusionSupplierCategory.Every3Hours,
            PaxConfiguration = paxConfiguration!
        };

        var content = JsonConvert.SerializeObject(travelFusionSupplierRequest);
        var response =
            await _client.PutAsync($"/api/Robots/TravelFusion/Producers/categorized/suppliers/{supplier}",
                new StringContent(content, Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();

        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = MockServerFixture.CreateTravelFusionTimetableContent([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/Timetables/travelfusion/days",
            method: "POST");

        //Act  
        response =
            await _client.PostAsync(
                $"/api/Robots/TravelFusion/Producers/categorized/{travelFusionSupplierRequest.Category}", null);
        response.EnsureSuccessStatusCode();

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 1).ToList();
        messages.ForEach(message => _output.WriteLine("message: " + JsonConvert.SerializeObject(message)));

        //Assert
        messages.Count.Should().Be(1);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, paxConfiguration: paxConfiguration ?? "1.0.1.1");

        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}