using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Producers.TravelFusion;
using Hangfire;
using MongoDB.Driver;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class TravelFusionSuppliersProducer : IDisposable
{
    private readonly RabbitMqFixture _rabbitMqFixture;
    private readonly MockServerFixture _mockServerFixture;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly IMongoCollection<TravelFusionSupplier> _suppliers;
    private readonly HttpClient _client;

    public TravelFusionSuppliersProducer(Infrastructure infrastructure)
    {
        _client = _client = infrastructure.CreateProducersApp().CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
        _suppliers = new MongoClient(infrastructure.MongoConnectionString)
                .GetDatabase("RobotsProducers")
                .GetCollection<TravelFusionSupplier>("TravelFusionSuppliers");
    }

    public async void Dispose()
    {
        await _client.DeleteAsync("/TravelFusionSuppliersProducer");
    }

    [Fact]
    public async Task TravelFusionSuppliersProducerTest()
    {
        var travelFusionSupplier = new TravelFusionSupplier
        {
            Category = TravelFusionSupplierCategory.Every60Minutes,
            Id = new TravelFusionSupplier.TravelFusionSupplierId()
            {
                Supplier = "wizzair"
            },
            PaxConfiguration = "*******"
        };
        await _suppliers.ReplaceOneAsync(s => s.Id.Equals(travelFusionSupplier.Id), travelFusionSupplier, new ReplaceOptions { IsUpsert = true });

        //Setup
        var airlineCode = "FR";
        var providerCode = 58;
        var supplier = "wizzair";
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = airlineCode,
            Supplier = supplier,
        };

        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub(new() { flight });
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent(new() { flight });
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/connectionnetworks/providers/{providerCode}?suppliers={supplier}", method: "GET");
        mockResponseContent = MockServerFixture.CreateTravelFusionTimetableContent(new() { flight });
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/Timetables/travelfusion/days", method: "POST");

        //Act
        var response = await _client.PutAsync($"/TravelFusionSuppliersProducer/Cron/{Cron.Never()}", null);
        response.EnsureSuccessStatusCode();

        await _client.TriggerHangfireRecurringJob(RobotsProducers.Producers.TravelFusion.TravelFusionSuppliersProducer.JobName);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 1).ToList();

        //Assert
        messages.Count.Should().Be(1);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode);

        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}