using Esky.FlightsCache.MessageContract;

namespace Esky.FlightsCache.Robots.Integration.Tests
{
    public class QueueMessage
    {
        public string? MessageId { get; set; }
        public string? ConversationId { get; set; }
        public string? SourceAddress { get; set; }
        public string? DestinationAddress { get; set; }
        public string[]? MessageType { get; set; }
        public required Message Message { get; set; }
        public DateTime SentTime { get; set; }
        public Headers? Headers { get; set; }
        public Host? Host { get; set; }
    }

    public class Message
    {
        public required FlightCache[] Flights { get; set; }
        public required Commandoptions CommandOptions { get; set; }
        public required Sourcedescription SourceDescription { get; set; }
    }

    public class Commandoptions
    {
        public string? GroupName { get; set; }
        public Deleteoptions? DeleteOptions { get; set; }
    }

    public class Deleteoptions
    {
        public bool IsEnabled { get; set; }
        public string[]? AirlineCodes { get; set; }
        public DateTime DeleteDepartureDayFrom { get; set; }
        public DateTime DeleteDepartureDayTo { get; set; }
    }

    public class Sourcedescription
    {
        public string? Name { get; set; }
        public string? MachineName { get; set; }
        public string? SearchDepartureCode { get; set; }
        public string? SearchArrivalCode { get; set; }
        public DateTime SearchDepartureDate { get; set; }
        public DateTime SendDate { get; set; }
        public int Flex { get; set; }
        public string? Provider { get; set; }
        public string? PartnerCode { get; set; }
        public string? PaxConfiguration { get; set; }
        public string? SessionId { get; set; }
    }

    public class Headers
    {
        public string? MtActivityId { get; set; }
    }

    public class Host
    {
        public string? MachineName { get; set; }
        public string? ProcessName { get; set; }
        public int ProcessId { get; set; }
        public string? Assembly { get; set; }
        public string? AssemblyVersion { get; set; }
        public string? FrameworkVersion { get; set; }
        public string? MassTransitVersion { get; set; }
        public string? OperatingSystemVersion { get; set; }
    }

}
