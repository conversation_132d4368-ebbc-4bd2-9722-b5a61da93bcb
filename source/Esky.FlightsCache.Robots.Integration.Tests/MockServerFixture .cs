using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots.ExternalServices.FlightsCache.Contract;
using Esky.FlightsCache.Robots.TimetableServiceClient;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Newtonsoft.Json;
using System.Text;
using static Esky.FlightsCache.RobotsProducers.TimetableServiceClient.TimetableServiceClient;

namespace Esky.FlightsCache.Robots.Integration.Tests
{
    public class MockServerFixture : IDisposable
    {
        private string MockUrl => _httpClient.BaseAddress!.AbsoluteUri;
        private readonly HttpClient _httpClient;

        public MockServerFixture(Uri baseAddress)
        {
            _httpClient = new HttpClient
            {
                BaseAddress = baseAddress
            };
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }

        public void MockExpectations(string content, string path, string method = "GET")
        {
            content = BuildContent(content, path, method);
            var request = new HttpRequestMessage(HttpMethod.Put, $"{MockUrl}mockserver/expectation")
            {
                Content = new StringContent(content, Encoding.UTF8, "application/json")
            };

            var response = _httpClient.Send(request);
            response.EnsureSuccessStatusCode();
        }

        private static string BuildContent(string content, string path, string httpMethod)
        {

            if (!path.Contains('?'))
                return $@"{{
    ""id"": ""{path}"",

    ""httpRequest"": {{
      ""method"": ""{httpMethod}"",
      ""path"": ""{path}"",

    }},
    ""httpResponse"": {{
      ""body"": {content}
  }}
}}";
            else
            {
                var pathWithParameters = path.Split('?');
                var onlyPath = pathWithParameters[0];
                var parameters = pathWithParameters[1].Split('&');

                StringBuilder mockRequest = new StringBuilder($@"{{
    ""id"": ""{path}"",
    ""httpRequest"": {{
      ""method"": ""{httpMethod}"",
      ""path"": ""{onlyPath}"",
      ""queryStringParameters"":{{");



                foreach (var param in parameters)
                {
                    string[] paramTable = param.Split('=');
                    var key = paramTable[0];
                    var value = paramTable[1];
                    mockRequest.Append($@"
                                ""{key}"": [
                                ""{value}""
                                ],");
                }
                mockRequest.Remove(mockRequest.Length - 1, 1);
                mockRequest.Append($@"}}

    }},
    ""httpResponse"": {{
      ""body"": {content}
  }}
}}");
                return mockRequest.ToString();
            }
        }

        public static string CreateSearchResponseOWStub(List<FlightModel> flightModels)
        {
            var searchResponse = new SearchResponse()
            {
                Data = [],
                Currency = "EUR",
                IsError = false,
            };
            flightModels.ForEach(flightModel =>
            {
                searchResponse.Data.Add(
                    new FlightDto()
                    {
                        Legs =
                        [
                            new LegDto()
                            {
                                Segments =
                                 [
                                     new SegmentDto()
                                     {
                                         AirlineCode = flightModel.AirlineCode,
                                         FlightNumber = "1642",
                                         OriginAirport = flightModel.Departure,
                                         DestinationAirport = flightModel.Destination,
                                         DepartureLocalTime = flightModel.FlightDate,
                                         ArrivalLocalTime = flightModel.FlightDate.AddHours(1),
                                         DepartureUtc = flightModel.FlightDate.ToUniversalTime(),
                                         ArrivalUtc = flightModel.FlightDate.ToUniversalTime(),
                                         OperatedBy = "test",
                                         AircraftCode = "test",
                                         IsSelfTransfer = false,
                                         BookingClass = "Y",
                                         Duration = 60,
                                         SeatsRemaining = 1
                                     }
                                 ],
                                Duration = 60,
                                SeparationOptions = new SeparationOptions()
                                {
                                    Options = SeparationOptionEnum.RoundTripOutbound,
                                    MinStay = 0,
                                },
                                DataTimestamp = DateTime.UtcNow.AddHours(-5)
                            }
                        ],
                        TotalAmount = flightModel.PriceAmount,
                        TaxAmount = 0,
                        PnrCount = 1,
                        Baggage = AvailabilityEnum.Surcharge,
                        Prices =
                        [
                            new PriceDto()
                            {
                            PassengerType = PassengerTypeEnum.ADT,
                            PriceType = PriceType.Base,
                            Amount = flightModel.PriceAmount,
                            }
                        ],
                        TripType = TripType.Leg1,
                        Currency = "EUR",
                        Supplier = flightModel.Supplier
                    }
                );
            });

            return JsonConvert.SerializeObject(searchResponse);
        }

        public static string CreateSearchResponseRTStub(List<FlightModel> flightModels)
        {
            var searchResponse = new SearchResponse()
            {
                Data = [],
                Currency = "EUR",
                IsError = false,
            };
            flightModels.ForEach(flightModel =>
            {
                searchResponse.Data.Add(
                    new FlightDto()
                    {
                        Legs =
                        [
                            new LegDto()
                            {
                                Segments =
                                 [
                                     new SegmentDto()
                                     {
                                         AirlineCode = flightModel.AirlineCode,
                                         FlightNumber = "1642",
                                         OriginAirport = flightModel.Departure,
                                         DestinationAirport = flightModel.Destination,
                                         DepartureLocalTime = flightModel.FlightDate,
                                         ArrivalLocalTime = flightModel.FlightDate.AddHours(1),
                                         DepartureUtc = flightModel.FlightDate.ToUniversalTime(),
                                         ArrivalUtc = flightModel.FlightDate.ToUniversalTime(),
                                         OperatedBy = "test",
                                         AircraftCode = "test",
                                         IsSelfTransfer = false,
                                         BookingClass = "Y",
                                         Duration = 60,
                                     }
                                 ],
                                Duration = 60,
                                SeparationOptions = new SeparationOptions()
                                {
                                    Options = SeparationOptionEnum.RoundTripOutbound,
                                    MinStay = 5,
                                },
                                DataTimestamp = DateTime.UtcNow.AddHours(-5)
                            },
                            new LegDto()
                            {
                                Segments =
                                 [
                                     new SegmentDto()
                                     {
                                         AirlineCode = flightModel.AirlineCode,
                                         FlightNumber = "1643",
                                         OriginAirport = flightModel.Destination,
                                         DestinationAirport = flightModel.Departure,
                                         DepartureLocalTime = flightModel.FlightDate.AddDays(5),
                                         ArrivalLocalTime = flightModel.FlightDate.AddHours(1),
                                         DepartureUtc = flightModel.FlightDate.AddDays(5).ToUniversalTime(),
                                         ArrivalUtc = flightModel.FlightDate.AddDays(5).ToUniversalTime(),
                                         OperatedBy = "test",
                                         AircraftCode = "test",
                                         IsSelfTransfer = false,
                                         BookingClass = "Y",
                                         Duration = 60,
                                     }
                                 ],
                                Duration = 60,
                                SeparationOptions = new SeparationOptions()
                                {
                                    Options = SeparationOptionEnum.RoundTripInbound,
                                    MinStay = 5,
                                },
                                DataTimestamp = DateTime.UtcNow.AddHours(-5)
                            }
                        ],
                        TotalAmount = 49.98m,
                        TaxAmount = 0,
                        PnrCount = 1,
                        Baggage = AvailabilityEnum.Surcharge,
                        Prices =
                        [
                            new PriceDto()
                            {
                                PassengerType = PassengerTypeEnum.ADT,
                                PriceType = PriceType.Base,
                                Amount = 24.99m,
                            }
                        ],
                        TripType = TripType.RT,
                        Currency = "EUR",
                        Supplier = flightModel.Supplier
                    }
                );
            });

            return JsonConvert.SerializeObject(searchResponse);
        }

        public string CreateCacheApiResponseStub(FlightModel flightModel, int providerCode)
        {
            var searchResponse = new Contract.SearchResponse()
            {
                Data =
                [
                    [
                        new Contract.FlightDto()
                        {
                            Legs =
                            [
                                new()
                                {
                                    Segments =
                                    [
                                        new Contract.SegmentDto()
                                        {
                                            AirlineCode = flightModel.AirlineCode,
                                            FlightNumber = "1642",
                                            OriginAirport = flightModel.Departure,
                                            DestinationAirport = flightModel.Destination,
                                            DepartureLocalTime = flightModel.FlightDate,
                                            ArrivalLocalTime = flightModel.FlightDate.AddHours(1),
                                            OperatedBy = "test",
                                            AircraftCode = "test",
                                            BookingClass = "Y",
                                        }
                                    ]
                                }
                            ],
                            Prices =
                            [
                                new()
                                {
                                    PassengerType = Contract.PassengerType.ADT,
                                    PriceType = Contract.PriceType.Base,
                                    Amount = 24.99m,
                                    Currency = "EUR"
                                }
                            ],
                            ProviderCode = providerCode,
                            SourceId = 0,
                            ValidatingCarrier = string.Empty,
                            FlightId = "1111",
                            RefreshDate = DateTime.UtcNow,

                        }
                    ]
                ]
            };

            return JsonConvert.SerializeObject(searchResponse);

        }

        public string CreateConnectionNetworkContent(List<FlightModel> flightModels)
        {
            var timetablesResponse = flightModels
                .Select(flightModel =>
                    new ConnectionNetwork()
                    {
                        DepartureAirportCode = flightModel.Departure,
                        ArrivalAirportCodes =
                        [
                             new ConnectionNetworkArrivalAirport()
                             {
                                 ArrivalCode = flightModel.Destination,
                                 Months = [int.Parse($"{flightModel.FlightDate.Year}{flightModel.FlightDate.Month:00}")],
                                 IsNew = false
                             }
                        ]
                    })
                .ToList();
            return JsonConvert.SerializeObject(timetablesResponse);
        }

        public static string CreateTravelFusionTimetableContent(List<FlightModel> flightModels)
        {
            var searchTimetablesResponseArray = flightModels
                .Select(flightModel =>
                    new SearchTimetablesResponseItem()
                    {
                        DepartureAirportCode = flightModel.Departure,
                        ArrivalAirportCode = flightModel.Destination,
                        Dates = [flightModel.FlightDate]
                    })
                .ToArray();
            return JsonConvert.SerializeObject(searchTimetablesResponseArray);
        }

        public static string CreateTimetableFlyingDaysContent(FlightModel flightModel)
        {
            var timetablesResponse = new List<GetFlyingDaysResponse>
            {
                new()
                {
                    createTime = DateTime.Now,
                    id = new GetFlyingDaysResponse.FlyingDaysId()
                    {
                        airlineCode = flightModel.AirlineCode,
                        departureAirportCode = flightModel.Departure,
                        arrivalAirportCode = flightModel.Destination,
                        day = flightModel.FlightDate.Date
                    }
                }
            };
            return JsonConvert.SerializeObject(timetablesResponse);
        }

        public static string CreateTimetableFlyingDaysContent_AirlineRouteSchedule(FlightModel flightModel)
        {
            var timetablesResponse = new List<AirlineRouteSchedule>
            {
                new()
                {
                    AirlineCode = flightModel.AirlineCode,
                    DepartureAirportCode = flightModel.Departure,
                    ArrivalAirportCode= flightModel.Destination,
                    Year = flightModel.FlightDate.Year,
                    Month = flightModel.FlightDate.Month,
                    Days = [flightModel.FlightDate.Day]
                }
            };
            return JsonConvert.SerializeObject(timetablesResponse);
        }

        public void Reset()
        {
            var request = new HttpRequestMessage(HttpMethod.Put, $"{MockUrl}mockserver/reset");

            var response = _httpClient.Send(request);
            response.EnsureSuccessStatusCode();
        }

    }
}