using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Google;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using Esky.Framework.PartnerSettings.Enums;
using Hangfire;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class CacheDemandTests : IDisposable
{
    private readonly RabbitMqFixture _rabbitMqFixture;
    private readonly MockServerFixture _mockServerFixture;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly HttpClient _client;
    private readonly RobotsConsumersApp _robotsConsumersApp;
    private GoogleRepositoryMock _googleRepositoryMock;
    private WebApplicationFactory<CacheDemandProducer> _robotsProducersApp;

    public CacheDemandTests(Infrastructure infrastructure)
    {
        _robotsProducersApp = infrastructure
            .CreateProducersApp()
            .WithWebHostBuilder(
                builder => builder.ConfigureTestServices(
                    services =>
                    {
                        services.Replace(ServiceDescriptor.Scoped<IGoogleRepository>(sp => _googleRepositoryMock ??= new GoogleRepositoryMock(sp.GetRequiredService<ICacheDemandRoutesAlgorithm>())));
                    }
                )
            );
        _googleRepositoryMock ??= (GoogleRepositoryMock)_robotsProducersApp.Services.CreateScope().ServiceProvider.GetRequiredService<IGoogleRepository>();
        _client = _robotsProducersApp.CreateClient();

        _robotsConsumersApp = infrastructure.CreateConsumersApp();
        _robotsConsumersApp.CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
    }

    public void Dispose()
    {
        _robotsProducersApp?.Dispose();
        _robotsConsumersApp?.Dispose();
    }

    [Fact]
    public async void CacheDemand_Job()
    {
        _googleRepositoryMock.PrepareDataRows(new CacheDemandModelInput
        {
            AirlinesFilter = ["FR"],
            ProviderCode = ProviderCodeEnum.TravelFusion,
            PartnerCode = "ADMIN",
            DepartureAirportCode = "VIE",
            ArrivalAirportCode = "KTW",
            IsRoundTrip = false,
            Type = JobType.CacheDemand,
            DepartureDateFrom = new RelativeDate(1),
            DepartureDateTo = new RelativeDate(3)
        });
        var jobName = "testName";
        var spreadsheetId = "spreadsheetId";
        var sheetName = "test";
        var providerCode = 58;
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = "FR",
            Supplier = "wizzair"
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");

        //Act  
        var response =
            await _client.PostAsync(
                $"/api/Robots/CacheDemand/SheetJobs/{spreadsheetId}/{sheetName}/run?jobId={jobName}", null);

        response.EnsureSuccessStatusCode();

        await _client.TriggerHangfireRecurringJob(jobName);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, _googleRepositoryMock.ExpectedMessageCount).ToList();

        //Assert
        messages.Count.Should().Be(_googleRepositoryMock.ExpectedMessageCount);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, paxConfiguration: "*******");
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }

    [Fact]
    public async void CacheDemand_Job_RT()
    {
        _googleRepositoryMock.PrepareDataRows(new CacheDemandModelInput
        {
            AirlinesFilter = ["FR"],
            ProviderCode = ProviderCodeEnum.TravelFusion,
            PartnerCode = "ADMIN",
            DepartureAirportCode = "VIE",
            ArrivalAirportCode = "KTW",
            IsRoundTrip = true,
            Type = JobType.CacheDemand,
            DepartureDateFrom = new RelativeDate(1),
            DepartureDateTo = new RelativeDate(3),
            MinStayLength = 5,
            MaxStayLength = 5
        });
        var jobName = "testName";
        var spreadsheetId = "spreadsheetId";
        var sheetName = "test";
        var providerCode = 58;
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = "FR",
            Supplier = "wizzair",
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseRTStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");

        //Act  
        var response =
            await _client.PostAsync(
                $"/api/Robots/CacheDemand/SheetJobs/{spreadsheetId}/{sheetName}/run?jobId={jobName}", null);

        response.EnsureSuccessStatusCode();

        await _client.TriggerHangfireRecurringJob(jobName);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, _googleRepositoryMock.ExpectedMessageCount).ToList();

        //Assert
        messages.Count.Should().Be(_googleRepositoryMock.ExpectedMessageCount);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, paxConfiguration: "*******");
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }

    //[Fact]
#pragma warning disable xUnit1013
    public async void ALC_CacheDemand_Job_Fallback() // future test for potential use of fallback endpoint at some point in the future
#pragma warning restore xUnit1013
    {
        _googleRepositoryMock.SetAlcHeader();
        _googleRepositoryMock.PrepareDataRows(new CacheDemandModelInput
        {
            AirlinesFilter = ["FR"],
            ProviderCode = null,
            PartnerCode = "ADMIN",
            DepartureAirportCode = "VIE",
            ArrivalAirportCode = "KTW",
            IsRoundTrip = false,
            Type = JobType.AmadeusLiveCheck,
            DepartureDateFrom = new RelativeDate(1),
            DepartureDateTo = new RelativeDate(3),
            OfficeId = "TESTOFFICEID",
            AlcExcludedAirlines = []
        });
        var jobName = "testName";
        var spreadsheetId = "spreadsheetId";
        var sheetName = "test";
        var providerCode = 35;
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = "FR",
            Supplier = "wizzair"
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");

        mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/api/flights", "POST");

        //Act  
        var response =
            await _client.PostAsync(
                $"/api/Robots/CacheDemand/SheetJobs/{spreadsheetId}/{sheetName}/run?jobId={jobName}", null);

        response.EnsureSuccessStatusCode();

        await _client.TriggerHangfireRecurringJob(jobName);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, _googleRepositoryMock.ExpectedMessageCount).ToList();

        //Assert
        messages.Count.Should().Be(_googleRepositoryMock.ExpectedMessageCount);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, paxConfiguration: "*******");
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }

    [Fact]
    public async void ALC_CacheDemand_Job()
    {
        _googleRepositoryMock.SetAlcHeader();
        _googleRepositoryMock.PrepareDataRows(new CacheDemandModelInput
        {
            AirlinesFilter = ["FR"],
            ProviderCode = null,
            PartnerCode = "ADMIN",
            DepartureAirportCode = "VIE",
            ArrivalAirportCode = "KTW",
            IsRoundTrip = false,
            Type = JobType.AmadeusLiveCheck,
            DepartureDateFrom = new RelativeDate(1),
            DepartureDateTo = new RelativeDate(3),
            OfficeId = "TESTOFFICEID",
            AlcExcludedAirlines = []
        });
        var jobName = "testName";
        var spreadsheetId = "spreadsheetId";
        var sheetName = "test";
        var providerCode = 35;
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = "FR",
            Supplier = "wizzair"
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub([flight]);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/AmadeusLiveCheck", "POST");

        mockResponseContent = _mockServerFixture.CreateCacheApiResponseStub(flight, providerCode);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/api/flights", "POST");

        //Act  
        var response =
            await _client.PostAsync(
                $"/api/Robots/CacheDemand/SheetJobs/{spreadsheetId}/{sheetName}/run?jobId={jobName}", null);

        response.EnsureSuccessStatusCode();

        await _client.TriggerHangfireRecurringJob(jobName);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, _googleRepositoryMock.ExpectedMessageCount).ToList();

        //Assert
        messages.Count.Should().Be(_googleRepositoryMock.ExpectedMessageCount);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode, paxConfiguration: "*******");
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}

