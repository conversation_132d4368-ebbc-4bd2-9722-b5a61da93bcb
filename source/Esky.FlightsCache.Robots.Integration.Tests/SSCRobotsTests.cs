using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.SSC;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Miscellaneous;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Google.Protobuf.WellKnownTypes;
using Hangfire;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MongoDB.Bson.Serialization;
using Newtonsoft.Json;
using NSubstitute;
using System.Text;
using System.Xml.Linq;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class SSCRobotsTests
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly HttpClient _client;
    private readonly RobotsConsumersApp _consumerApp;
    private readonly ITestOutputHelper _output;

    public SSCRobotsTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;
        _client = infrastructure.CreateProducersApp().CreateClient();

        _consumerApp = infrastructure.CreateConsumersApp();
        _consumerApp.CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);

    }

    [Fact]
    public async void SSCRobotProducerTest()
    {
        //Setup
        var supplier = "wizzair";
        var airlineCode = "W6";
        var sscProviderCode = 6;

        var reference = new Configuration
        {
            Cron = Cron.Never(),
            RelativeDateRange = 1..14
        };
        var groupConfiguration = new GroupConfiguration
        {
            Name = "TestSSC",
            Configurations = [reference with { RelativeDateRange = new RelativeDateRange(DateTime.UtcNow.Date.AddDays(1), DateTime.UtcNow.Date.AddDays(10)) }],
            DepartureAirports = ["WAW", "WMI"],
            Supplier = supplier,
            Airlines = ["W6"]
        };
        var json = JsonConvert.SerializeObject(groupConfiguration, new JsonSerializerSettings() { Converters = { new RelativeDateJsonSerializer() } });

        var departureFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(2),
            Departure = "WAW",
            Destination = "LHR",
            AirlineCode = airlineCode,
            PriceAmount = 11.1m,
            Supplier = supplier
        };
        var returnFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(6),
            Departure = "LHR",
            Destination = "WAW",
            AirlineCode = airlineCode,
            PriceAmount = 33.3m,
            Supplier = supplier
        };
        var flights = new List<FlightModel>
        {
            departureFlight,
            returnFlight,
        };

        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub(flights);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/flights?config=Robots.SSC.{supplier}&origin={departureFlight.Departure}&destination={departureFlight.Destination}&dd={departureFlight.FlightDate.ToString("yyyy-MM-dd")}&rd={returnFlight.FlightDate.ToString("yyyy-MM-dd")}&flex=0&adt=2&yth=0&chd=0&inf=0", "GET");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent(flights);

        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/connectionnetworks/airline/{airlineCode}", method: "GET");

        var route = $"{departureFlight.Departure}-{departureFlight.Destination}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent_AirlineRouteSchedule(departureFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/flyingdays?airlineCodes={airlineCode}", "GET");
        
        route = $"{departureFlight.Destination}-{departureFlight.Departure}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent_AirlineRouteSchedule(returnFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/flyingdays?airlineCodes={airlineCode}", "GET");

        //Act
        var response = await _client.PostAsync(
            $"/api/SSCRobot/Configuration/Groups",
            new StringContent(json, Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();
        var jobname = GroupConfiguration.GetJobNames("TestSSC").First();
        await _client.TriggerHangfireRecurringJob(jobname);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 2).ToList();

        //Assert
        messages.Count.Should().Be(2);
        messages.ForEach(message =>
        {
            using (new AssertionScope())
            {
                var flight = message.Flights.Should().ContainSingle().Which;
                flight.ProviderCode.Should().Be(sscProviderCode);
                flight.Legs[0].AirlineCode.Should().Be(airlineCode);
                flight.Legs[0].DepartureCode.Should().BeOneOf("LHR", "WAW");
                flight.Legs[0].ArrivalCode.Should().BeOneOf("LHR", "WAW");
                flight.Legs[0].DepartureDate.Should().BeOneOf(departureFlight.FlightDate, returnFlight.FlightDate);
            }
        });

        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}