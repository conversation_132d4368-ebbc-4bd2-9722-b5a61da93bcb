using Esky.FlightsCache.MessageContract;
using Newtonsoft.Json;
using RabbitMQ.Client;

namespace Esky.FlightsCache.Robots.Integration.Tests
{
    public class RabbitMqFixture
    {
        private readonly ConnectionFactory _factory;

        public RabbitMqFixture(string connectionString)
        {
            _factory = new ConnectionFactory
            {
                Uri = new Uri(connectionString),
            };
        }

        public IEnumerable<Message> ReadMessagesFromQueue(string queueName, int expectedMessageCount = 0)
        {
            var cancellationToken = new CancellationTokenSource(TimeSpan.FromSeconds(3 * 60)).Token;
            using var connection = _factory.CreateConnection();
            using var model = connection.CreateModel();
            var counter = 0;
            while (counter < expectedMessageCount && !cancellationToken.IsCancellationRequested)
            {
                var result = model.BasicGet(queueName, autoAck: true);
                if (result == null)
                {
                    continue;
                }

                counter++;

                var unzipped = GzipHelper.Unzip(result.Body.Span.ToArray());
                var message = JsonConvert.DeserializeObject<QueueMessage>(unzipped)!.Message;

                yield return message;
            }
        }

        public void QueuePurge(string queueName)
        {
            using var connection = _factory.CreateConnection();
            using var model = connection.CreateModel();
            model.QueuePurge(queueName);
        }

        public uint MessageCount(string queueName)
        {
            var connection = _factory.CreateConnection();
            var model = connection.CreateModel();
            return model.MessageCount(queueName);
        }

        public async Task<bool> WaitForQueueFillUp(string queueName, uint expectedMessageCount)
        {
            using var connection = _factory.CreateConnection();
            using var model = connection.CreateModel();
            var i = 0;
            while (model.MessageCount(queueName) < expectedMessageCount)
            {
                await Task.Delay(1000);
                if (i >= 30)
                    return false;
                i++;
            }
            return true;
        }
    }
}