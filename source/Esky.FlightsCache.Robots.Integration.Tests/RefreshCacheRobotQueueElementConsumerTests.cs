using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.IBE.Robots.Queue.Messages;
using MassTransit;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Newtonsoft.Json;
using NSubstitute;
using QueueElement = Esky.FlightsCache.RobotsProducers.Messages.QueueElement;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class RefreshCacheRobotQueueElementConsumerTests : IDisposable
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly ITestOutputHelper _output;
    private readonly IBus _bus;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly RobotsConsumersApp _consumer;

    public RefreshCacheRobotQueueElementConsumerTests(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _output = output;
        var producer = infrastructure.CreateProducersApp();
        _consumer = infrastructure.CreateConsumersApp();

        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
        _bus = producer.Services.GetRequiredService<IBus>();
    }

    public void Dispose()
    {
        _rabbitMqFixture.QueuePurge(_queue);
        _consumer.Dispose();
    }

    [Fact]
    public async Task RefreshDirectRyanair()
    {
        // Arrange
        _consumer.CreateClient();
        var airlineCode = "FR";
        var providerCode = 78;
        var supplier = "test_supplier";

        var queueElement = new QueueElement()
        {
            DepartureDay = DateTime.UtcNow.AddDays(10),
            ReturnDepartureDay = DateTime.UtcNow.AddDays(15),
            DepartureCode = "LHR",
            ArrivalCode = "KRK",
            PartnerCode = "ADMIN",
        };

        var outboundFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(10),
            Departure = "LHR",
            Destination = "KRK",
            AirlineCode = airlineCode,
            PriceAmount = 11.1m,
            Supplier = supplier
        };

        var inboundFlight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(15),
            Departure = "KRK",
            Destination = "LHR",
            AirlineCode = airlineCode,
            PriceAmount = 33.3m,
            Supplier = supplier
        };

        var flights = new List<FlightModel>
        {
            outboundFlight,
            inboundFlight
        };
        var message = new RefreshCacheRobotQueueElement
        {
            InnerElement = queueElement,
            SourceType = RefreshCacheRobotQueueElement.RefreshCacheSourceType.FlightNotAvailable,
            ProviderCode = Framework.PartnerSettings.Enums.ProviderCodeEnum.DirectRyanair
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub(flights);
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent(flights);
        _mockServerFixture.MockExpectations(mockResponseContent, path: $"/api/connectionnetworks/airline/{airlineCode}", method: "GET");

        var route = $"{queueElement.DepartureCode}-{queueElement.ArrivalCode}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent(outboundFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/airline/{airlineCode}/flyingdays", "GET");

        route = $"{inboundFlight.Departure}-{inboundFlight.Destination}";
        mockResponseContent = MockServerFixture.CreateTimetableFlyingDaysContent(inboundFlight);
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/timetables/destination/{route}/airline/{airlineCode}/flyingdays", "GET");

        // Act
        await _bus.Publish(message);

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 2).ToList();

        //Assert
        messages.Should().HaveCount(2);

        messages.ForEach(message =>
        {
            _output.WriteLine("message: " + JsonConvert.SerializeObject(messages));
            using (new AssertionScope())
            {
                message.SourceDescription.Name.Should().Be("DirectRyanairRefresh_FlightNotAvailable");
                message.Flights.Should().HaveCount(1);
                foreach (var flight in message.Flights)
                {
                    flight.ProviderCode.Should().Be(providerCode);
                    flight.Legs.First().AirlineCode.Should().Be(airlineCode);
                    flight.Legs.First().DepartureCode.Should().BeOneOf("LHR", "KRK");
                    flight.Legs.First().ArrivalCode.Should().BeOneOf("LHR", "KRK");
                    flight.Legs.First().DepartureDate.Should().BeOneOf(outboundFlight.FlightDate, inboundFlight.FlightDate);
                    flight.Legs.First().AdultPrices.Should().ContainSingle();
                }
            }
        });
        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }

    [Fact]
    public async Task RefreshAnyProvider()
    {
        // Arrange
        var searchServiceMock = Substitute.For<ISearchService<RefreshCacheRobotQueueElement>>();
        _consumer
            .WithWebHostBuilder(
                b => b.ConfigureTestServices(
                    services => services.Replace(ServiceDescriptor.Scoped<ISearchService<RefreshCacheRobotQueueElement>>(_ => searchServiceMock))
                )
            )
            .CreateClient();

        var queueElement = new QueueElement()
        {
            DepartureDay = DateTime.UtcNow.AddDays(10),
            ReturnDepartureDay = DateTime.UtcNow.AddDays(15),
            DepartureCode = "LHR",
            ArrivalCode = "KRK",
            PartnerCode = "ADMIN",
        };

        var message = new RefreshCacheRobotQueueElement
        {
            InnerElement = queueElement,
            SourceType = RefreshCacheRobotQueueElement.RefreshCacheSourceType.FlightNotAvailable,
            ProviderCode = Framework.PartnerSettings.Enums.ProviderCodeEnum.Test
        };

        // Act
        await _bus.Publish(message);

        //Assert
        await Task.Delay(500); //wait for consumer to consume message
        await searchServiceMock
            .Received(1)
            .Search(
                Arg.Is<RefreshCacheRobotQueueElement>(e => e.SourceType == message.SourceType && e.ProviderCode == message.ProviderCode),
                "RefreshFlight",
                Arg.Any<CancellationToken>()
            );
    }
}