<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>

    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>

    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions.Json" Version="6.1.0" />
    <PackageReference Include="Meziantou.Extensions.Logging.Xunit" Version="1.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="RabbitMQ.Client" Version="6.5.0" />
    <PackageReference Include="Testcontainers" Version="3.6.0" />
    <PackageReference Include="Testcontainers.MongoDb" Version="3.6.0" />
    <PackageReference Include="Testcontainers.RabbitMq" Version="3.6.0" />
    <PackageReference Include="xunit" Version="2.7.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.7">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Esky.FlightsCache.RobotsConsumers\Esky.FlightsCache.RobotsConsumers.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.RobotsProducers\Esky.FlightsCache.RobotsProducers.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots\Esky.FlightsCache.Robots.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\definitions.json">
      <Link>definitions.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\initializerJson.json">
      <Link>initializerJson.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="..\..\rabbitmq.conf">
      <Link>rabbitmq.conf</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!--<ItemGroup>
    <None Update="Properties\xunit.runner.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>-->

</Project>
