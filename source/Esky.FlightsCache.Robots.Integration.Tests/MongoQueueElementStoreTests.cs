using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Messages;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class MongoQueueElementStoreTests
{
    private readonly IMongoCollection<TestElement<SSCQueueElement>> _testCollection;

    public MongoQueueElementStoreTests(Infrastructure infrastructure)
    {
        _testCollection = new MongoClient(infrastructure.MongoConnectionString)
            .GetDatabase("RobotsProducers")
            .GetCollection<TestElement<SSCQueueElement>>(Guid.NewGuid().ToString());

        infrastructure.CreateProducersApp().CreateClient(); // initialize app to configure Mongo mappings
    }

    [Fact]
    public async Task QueueElementDateTimeUnspecified_NotConvertedToUtc()
    {
        var dateTime = new DateTime(2024, 10, 3, 0, 0, 0, DateTimeKind.Unspecified);

        await _testCollection.InsertOneAsync(new TestElement<SSCQueueElement> { Elements = [new SSCQueueElement { DepartureDay = dateTime, Supplier = "wizzair" }] });
        var obj = await _testCollection.AsQueryable().FirstAsync();

        var departureDay = obj.Elements.Should().ContainSingle().Which.DepartureDay;
        departureDay.Should().Be(dateTime, "MongoDb should not convert to UTC on save - date and time parts should be the same");
        departureDay.Kind.Should().Be(DateTimeKind.Utc, "MongoDb stores DateTime in UTC and it can't tell what it was before");
    }

    private class TestElement<T> where T : QueueElement
    {
        [BsonId] public ObjectId Id { get; init; }
        public T[] Elements { get; init; } = [];
    }
}