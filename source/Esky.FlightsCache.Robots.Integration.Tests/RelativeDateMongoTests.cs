using Esky.FlightsCache.Robots.Integration.Tests.Setup;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand;
using Esky.FlightsCache.RobotsProducers.Producers.CacheDemand.Model;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class RelativeDateMongoTests : IDisposable
{
    private readonly IMongoCollection<CacheDemandModelInput> _cacheOnDemand;

    public RelativeDateMongoTests(Infrastructure infrastructure)
    {
        _cacheOnDemand = new MongoClient(infrastructure.MongoConnectionString)
            .GetDatabase("RobotsProducers")
            .GetCollection<CacheDemandModelInput>("cache_demand_test");
    }

    [Fact]
    public async Task RelativeDate_SaveAndRead_MongoTest()
    {
        BsonSerializer.TryRegisterSerializer(RelativeDateBsonSerializer.Instance);

        var departureDateTo = DateTime.UtcNow.Date.AddDays(7);
        await _cacheOnDemand.InsertOneAsync(new CacheDemandModelInput
        {
            DepartureDateFrom = RelativeDate.Parse("2"),
            DepartureDateTo = RelativeDate.Parse(departureDateTo.ToString("yyyy-MM-dd")),
            PartnerCode = "ESKY",
            DepartureAirportCode = "KTW",
            ArrivalAirportCode = "STN"
        });

        var input = await _cacheOnDemand.AsQueryable().FirstAsync();

        Assert.Equal(new RelativeDate(2), input.DepartureDateFrom);
        Assert.Equal(new RelativeDate(departureDateTo), input.DepartureDateTo);
        Assert.Equal(6, input.GetDepartureDateRange().Count);
    }

    public void Dispose()
    {
        _cacheOnDemand.DeleteMany(new BsonDocument());
    }
}