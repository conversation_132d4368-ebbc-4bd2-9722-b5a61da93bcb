using Esky.FlightsCache.Robots.Integration.Tests.Setup;

namespace Esky.FlightsCache.Robots.Integration.Tests;

[Collection(RobotsAppTestCollection.Name)]
public class TravelFusionRobotsRouteDiscoveryTest : IDisposable
{
    readonly RabbitMqFixture _rabbitMqFixture;
    readonly MockServerFixture _mockServerFixture;
    private readonly ITestOutputHelper _output;
    private readonly string _queue = "WorkerServiceStubQueue";
    private readonly HttpClient _client;

    public TravelFusionRobotsRouteDiscoveryTest(ITestOutputHelper output, Infrastructure infrastructure)
    {
        _client = infrastructure.CreateProducersApp().CreateClient();
        _rabbitMqFixture = new RabbitMqFixture(infrastructure.RabbitConnectionString);
        _mockServerFixture = new MockServerFixture(infrastructure.MockServerAddress);
        _output = output;
    }

    public void Dispose()
    {
    }

    [Fact]
    public async void TravelFusion_Route_Discovery()
    {
        //Setup
        var providerCode = 58;
        var flight = new FlightModel()
        {
            FlightDate = DateTime.UtcNow.Date.AddDays(1),
            Departure = "VIE",
            Destination = "KRK",
            AirlineCode = "FR",
            Supplier = "wizzair"
        };
        var mockResponseContent = MockServerFixture.CreateSearchResponseOWStub(new() { flight });
        _mockServerFixture.MockExpectations(mockResponseContent, $"/{providerCode}", "POST");
        mockResponseContent = _mockServerFixture.CreateConnectionNetworkContent(new() { flight });
        _mockServerFixture.MockExpectations(mockResponseContent,
            path: $"/api/connectionnetworks/providers/{providerCode}?newRoutes=True", method: "GET");

        //Act  
        var response = await _client.PostAsync("/api/Robots/TravelFusion/Producers/routeDiscovery/Producers", null);
        response.EnsureSuccessStatusCode();

        var messages = _rabbitMqFixture.ReadMessagesFromQueue(_queue, 2).ToList();

        //Assert
        messages.Count.Should().Be(2);
        AssertionExtension.VerifyMessagesFromQueue(messages, flight, providerCode);

        _rabbitMqFixture.MessageCount(_queue).Should().Be(0);
    }
}