using Esky.FlightsCache.MessageContract;

namespace Esky.FlightsCache.Robots.Integration.Tests
{
    internal static class AssertionExtension
    {
        public static void VerifyMessagesFromQueue(List<Message> messages, FlightModel flight, int providerCode, string partnerCode = "ADMIN", string paxConfiguration = "2.0.0.0")
        {
            messages.ForEach(message =>
            {
                using (new AssertionScope())
                {
                    message.Flights.First().Supplier.Should().Be(flight.Supplier);
                    message.Flights.Should().HaveCount(1);
                    message.Flights.First().ProviderCode.Should().Be(providerCode);
                    message.Flights.First().Legs.First().AirlineCode.Should().Be(flight.AirlineCode);
                    message.Flights.First().Legs.First().DepartureCode.Should().Be(flight.Departure);
                    message.Flights.First().Legs.First().ArrivalCode.Should().Be(flight.Destination);
                    message.Flights.First().Legs.First().DepartureDate.Should().Be(flight.FlightDate);
                    message.SourceDescription.PartnerCode.Should().Be(partnerCode);
                    message.SourceDescription.PaxConfiguration.Should().Be(paxConfiguration);
                }
            });
        }
    }
}
