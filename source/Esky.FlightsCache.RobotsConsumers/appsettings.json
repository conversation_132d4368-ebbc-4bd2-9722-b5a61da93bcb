{"Basic": {"Environment": "PRO"}, "Default": {"SearchTimeoutInSeconds": 30}, "AirportCurrencySettings": {"ConnectionString": "secret", "UpdateIntervalInMinutes": 720}, "OpenTelemetry": {"ServiceName": "FlightsCache.RobotsConsumers", "Metrics": "<PERSON><PERSON><PERSON><PERSON><PERSON>,FlightsCache.RobotsConsumers", "Traces": "<PERSON><PERSON><PERSON><PERSON><PERSON>,FlightsCache.RobotsConsumers"}, "RobotServiceBus": {"Url": "rabbitmq://rabbitmq-flightscache.service.query.consul./ets", "Login": "ets", "Password": "secret", "UseCluster": false, "SerializationMode": "JsonGzip", "SerializationProvider": "SystemTextJson"}, "CacheApi": {"Url": "http://esky-flightscache-api.query.consul./"}, "PartnerSettings": {"ApiKey": "secret", "Environment": "pro", "System": "ets", "Url": "http://esky-partnersettings-api-green.query.consul./"}, "DatabaseSettings": {"ConnectionString": "..."}, "AmadeusLiveCheck": {"ApiUrl": "http://esky-content-amadeus-api.query.consul./api/", "RuntimeSettingsFallbackUrl": "https://noded1.test.webservices.amadeus.com", "CacheLifetimeInHours": 104}, "SSCProvider": {"ApiUrl": "http://esky-content-ssc-api.query.consul/api/"}, "DirectRyanair": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 7, "PrefetchCount": 10, "ConcurrentMessageLimit": 3}, "SSCSupplierConfiguration": {"wizzair": {"UseSeparateQueueForCalendar": true}, "easyjet": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 3}, "lufthansa": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 2}, "vueling": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 2}, "tui": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 1}, "ryanair-new": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 1}, "skyexpress": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 4}, "corendon": {"InitialSeatsCountToCheck": 2, "MaxSeatsCountToCheck": 9}, "sunexpress": {}, "tarom": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 2}, "lot": {"InitialSeatsCountToCheck": 1, "MaxSeatsCountToCheck": 2}}, "RyanairSSCComparisonSettings": {"SamplingOneOf": 1000}, "Jet2AvailabilityChecker": {"MaxSeats": 8, "DaysForward": 7}, "ProviderSettings": {"Configuration": {"6.tui": {"ReturnsRoundTripAsRoundTrip": true}}}, "HotelsApi": {"Url": "http://esky-hotels-api-robots-green.query.consul."}, "BigQuerySettings": {"EskyDatacHotelsProjectId": "esky-datac-hotels"}, "ServiceBus_Login": "secret", "ServiceBus_Password": "secret", "ServiceBus_Clustermembers": "secret", "ServiceBus_VHost": "secret", "NLog": {"throwConfigExceptions": true, "internalLogFile": "~\\nlog\\internal-nlog.txt", "internalLogLevel": "<PERSON><PERSON>", "internalLogToConsole": true, "autoReload": true, "extensions": [{"assembly": "Esky.NLog.RabbitMQ.Target"}], "targets": {"async": true, "rabbit": {"type": "RabbitMQ", "username": "${configsetting:item=ServiceBus_Login}", "password": "${configsetting:item=ServiceBus_Password}", "clustermembers": "${configsetting:item=ServiceBus_Clustermembers}", "vhost": "${configsetting:item=ServiceBus_VHost}", "fields": [{"key": "HostName", "name": "HostName", "layout": "${machinename}"}, {"key": "Date", "name": "Date", "layout": "${date:universalTime=False:format=s}"}, {"key": "Application", "name": "Application", "layout": "esky-flightscache-robotsconsumers"}, {"key": "Environment", "name": "Environment", "layout": "${configsetting:item=ASPNETCORE_ENVIRONMENT}"}, {"key": "Exception", "name": "Exception", "layout": "${exception}"}, {"key": "AdditionalMessage", "name": "AdditionalMessage", "layout": "${exception:format=toString,Data}"}, {"key": "StackTrace", "name": "StackTrace", "layout": "${exception:format=StackTrace}"}, {"key": "Context", "name": "Provider", "layout": "${event-properties:Context}"}]}}}, "TravelFusion": {"Retry": {"Conditions": [{"Suppliers": ["vueling", "pegasus", "ezy", "wizzairxml"], "MaxAgeInMinutes": 120, "DepartureDaysTo": 4}, {"Suppliers": ["germanwingsb2b"], "MaxAgeInMinutes": 240, "DepartureDaysTo": 4}, {"Suppliers": ["wizzairxml", "ezy"], "MaxAgeInMinutes": 1440, "DepartureDaysTo": 14}, {"Suppliers": ["ezy"], "MaxAgeInMinutes": 2880, "DepartureDaysTo": 56}], "PartnerCode": "MASTER"}}, "RateLimiterSettings": {"78": {"Limit": 2, "WindowInMilliseconds": 1000, "QueueLimit": 1000}, "ALC": {"Limit": 3, "WindowInMilliseconds": 1300, "QueueLimit": 1000}}, "RetrySettings": {"MaxRetryAttempts": 3, "InitialDelayInMilliseconds": 2000}}