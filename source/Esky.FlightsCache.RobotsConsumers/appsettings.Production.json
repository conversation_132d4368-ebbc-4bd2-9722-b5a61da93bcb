{"ExternalServices": {"TimeTableService": {"Url": "http://esky-timetables-api.query.consul./"}, "CurrencyService": {"Url": "http://esky-currencyservice-api-green.query.consul./"}}, "BigQuerySettings": {"ProjectId": "esky-ets-logs-pro", "GoogleCredentialsPath": "/creds/account.json"}, "ProviderSearch": {"GenericApiFlightProviderUrl": "http://esky-flightsintegration-api.service.consul./api/flights/"}, "PartnerSettings": {"Environment": "pro"}, "AmadeusLiveCheck": {"RuntimeSettingsFallbackUrl": "https://noded1.production.webservices.amadeus.com"}, "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "System.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "Esky.CurrencyService.ApiClient.Logger.DefaultCommunicationLogger", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "<PERSON>", "finalMinLevel": "Error"}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}