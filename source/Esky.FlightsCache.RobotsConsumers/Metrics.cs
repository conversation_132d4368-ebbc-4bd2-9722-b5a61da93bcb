using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Net;

namespace Esky.FlightsCache.RobotsConsumers;

public static class Metrics
{
    private static readonly Meter _meter = new("FlightsCache.RobotsConsumers");
    private static readonly Counter<int> _providerSearchCounter = _meter.CreateCounter<int>("provider-search");
    private static readonly Counter<int> _routesInCacheCounter = _meter.CreateCounter<int>("route-in-cache");
    private static readonly Counter<int> _providerRequestsCounter = _meter.CreateCounter<int>("provider-requests");
    private static readonly Counter<int> _itinerariesRequestedCounter = _meter.CreateCounter<int>("itineraries-requested");
    private static readonly Counter<int> _itinerariesReturnedCounter = _meter.CreateCounter<int>("itineraries-returned");
    private static readonly Counter<int> _hotelSearchCounter = _meter.CreateCounter<int>("hotel-search");

    private const string _type = "type";
    private const string _source = "source";
    private const string _officeId = "officeId";
    private const string _httpStatusCode = "httpStatusCode";

    public static class AmadeusLiveCheck
    {
        public static readonly Counter<int> NoSingleTicketsCounter = _meter.CreateCounter<int>("no-single-tickets");
        public static readonly Counter<int> NoFlightsCounter = _meter.CreateCounter<int>("no-flights");

        private const string _alc = "AmadeusLiveCheck";
        private const string _exists = "exists";

        public static void ReportRouteExistsInCache(string source)
        {
            _routesInCacheCounter.Add(1,
                new KeyValuePair<string, object?>(_type, _alc),
                new KeyValuePair<string, object?>(_exists, "true"),
                new KeyValuePair<string, object?>(_source, source));
        }

        public static void ReportRouteNotExistsInCache(string source)
        {
            _routesInCacheCounter.Add(1,
                new KeyValuePair<string, object?>(_type, _alc),
                new KeyValuePair<string, object?>(_exists, "false"),
                new KeyValuePair<string, object?>(_source, source));
        }

        public static void ReportAlcRequest(string source, string officeId, HttpStatusCode httpStatusCode)
        {
            _providerRequestsCounter.Add(1,
                new KeyValuePair<string, object?>(_type, _alc),
                new KeyValuePair<string, object?>(_source, source),
                new KeyValuePair<string, object?>(_officeId, officeId),
                new KeyValuePair<string, object?>(_httpStatusCode, (int)httpStatusCode));
        }

        public static void ReportAlcRequestedItineraries(int count, string source)
        {
            _itinerariesRequestedCounter.Add(count,
                new KeyValuePair<string, object?>(_type, _alc),
                new KeyValuePair<string, object?>(_source, source));
        }

        public static void ReportAlcReturnedItineraries(int count, string source)
        {
            _itinerariesReturnedCounter.Add(count,
                new KeyValuePair<string, object?>(_type, _alc),
                new KeyValuePair<string, object?>(_source, source));
        }
    }

    public static class Provider
    {
        private const string _provider = "provider";
        private const string _succeeded = "succeeded";
        private const string _supplier = "supplier";
        public static void ReportSearch(int providerCode, bool succeeded, string? supplier = default)
        {
            _providerSearchCounter.Add(1,
                new KeyValuePair<string, object?>(_provider, providerCode),
                new KeyValuePair<string, object?>(_succeeded, succeeded),
                new KeyValuePair<string, object?>(_supplier, supplier));
        }
    }

    public static class Hotels
    {
        private const string _provider = "provider";
        private const string _succeeded = "succeeded";
        public static void ReportSearch(int providerCode, bool succeeded, int count = 1)
        {
            _hotelSearchCounter.Add(count,
                new KeyValuePair<string, object?>(_provider, providerCode),
                new KeyValuePair<string, object?>(_succeeded, succeeded));
        }
    }
}