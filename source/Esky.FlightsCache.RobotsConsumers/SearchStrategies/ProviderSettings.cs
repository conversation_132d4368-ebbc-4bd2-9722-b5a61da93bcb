using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

/// <summary>
/// TODO: remove temporary solution when implemented https://eskygroup.atlassian.net/browse/FCACHE-1699
/// </summary>
public interface IProviderSettings
{
    bool ReturnsRtAsSeparateOw(ProviderCodeEnum? providerCode, string? supplier);
}

public class AppSettingsProviderSettings : IProviderSettings
{
    private readonly Dictionary<(ProviderCodeEnum?, string?), bool> _map;

    public AppSettingsProviderSettings(IOptions<Config> obj)
    {
        _map = obj.Value.Configuration.ToDictionary(GetKey, e => !e.Value.ReturnsRoundTripAsRoundTrip);
    }

    private static (ProviderCodeEnum?, string?) GetKey(KeyValuePair<string, Config.Entry> kp)
    {
        var parts = kp.Key.Split('.');
        if (parts.Length is not 2) throw new ArgumentException("Invalid key format");
        var providerCode = Enum.Parse<ProviderCodeEnum>(parts[0]);
        var supplier = parts[1];
        return (providerCode, supplier);
    }

    public bool ReturnsRtAsSeparateOw(ProviderCodeEnum? providerCode, string? supplier)
    {
        return _map.GetValueOrDefault((providerCode, supplier?.ToLower()), true);
    }

    public class Config
    {
        public required Dictionary<string, Entry> Configuration { get; set; } = new();
        public record Entry(bool ReturnsRoundTripAsRoundTrip);
    }
}