using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public interface IStrategyFactory
{
    ISearchStrategy<T> IncrementalAdultSeats<T>(ISearchService<T> searchService, Context context) where T : QueueElement;
    ISearchStrategy<T> OneWayBothDirectionsOnAirportCurrencyDiff<T>(ISearchStrategy<T> searchStrategy) where T : QueueElement, new();
    ISearchStrategy<T> RoundTripAsTwoOneWayWithDepartureAirportCurrency<T>(ISearchStrategy<T> searchStrategy, IExtension? extension = null) where T : QueueElement, new();
    ISearchStrategy<T> DiffCurrencyOneWayBothCurrenciesStrategy<T>(ISearchStrategy<T> searchStrategy, IExtension? extension = null) where T : QueueElement, new();
}

public class StrategyFactory : IStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;

    public StrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public ISearchStrategy<T> IncrementalAdultSeats<T>(ISearchService<T> searchService, Context context) where T : QueueElement
    {
        return ActivatorUtilities.CreateInstance<IncrementalAdultSeatsStrategy<T>>(_serviceProvider, searchService, context);
    }

    public ISearchStrategy<T> OneWayBothDirectionsOnAirportCurrencyDiff<T>(ISearchStrategy<T> searchStrategy) where T : QueueElement, new()
    {
        return ActivatorUtilities.CreateInstance<DiffCurrencyOneWayBothDirectionsStrategy<T>>(_serviceProvider, searchStrategy);
    }

    public ISearchStrategy<T> RoundTripAsTwoOneWayWithDepartureAirportCurrency<T>(ISearchStrategy<T> searchStrategy, IExtension? extension = null) where T : QueueElement, new()
    {
        return ActivatorUtilities.CreateInstance<RoundTripAsTwoOneWayWithDepartureAirportCurrencyExt<T>>(_serviceProvider, searchStrategy, extension ?? NoopExtension.Instance);
    }

    public ISearchStrategy<T> DiffCurrencyOneWayBothCurrenciesStrategy<T>(ISearchStrategy<T> searchStrategy, IExtension? extension = null) where T : QueueElement, new()
    {
        return ActivatorUtilities.CreateInstance<DiffCurrencyOneWayBothCurrenciesStrategy<T>>(_serviceProvider, searchStrategy, extension ?? NoopExtension.Instance);
    }
}