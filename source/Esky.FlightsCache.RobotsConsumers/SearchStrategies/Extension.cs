using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public interface IExtension
{
    void OnSameCurrency(IReadOnlyCollection<CacheRequest> cacheRequests);
    void OnDifferentCurrency(IReadOnlyCollection<CacheRequest> cacheRequestsDifferentCurrency);
}

public sealed class NoopExtension : IExtension
{
    public static readonly NoopExtension Instance = new();
    public void OnSameCurrency(IReadOnlyCollection<CacheRequest> cacheRequests) { }
    public void OnDifferentCurrency(IReadOnlyCollection<CacheRequest> cacheRequestsDifferentCurrency) { }
}