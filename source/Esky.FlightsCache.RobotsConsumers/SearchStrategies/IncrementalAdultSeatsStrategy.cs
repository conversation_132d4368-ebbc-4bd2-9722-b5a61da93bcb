using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public sealed class Context
{
    public Context(int initialSeatsCountToCheck, int maxSeatsCountToCheck)
    {
        ArgumentOutOfRangeException.ThrowIfLessThan(maxSeatsCountToCheck, initialSeatsCountToCheck, nameof(maxSeatsCountToCheck));

        InitialSeatsCountToCheck = initialSeatsCountToCheck;
        MaxSeatsCountToCheck = maxSeatsCountToCheck;
    }

    public int InitialSeatsCountToCheck { get; }
    public int MaxSeatsCountToCheck { get; }
}

public class IncrementalAdultSeatsStrategy<T> : ISearchStrategy<T> where T : QueueElement
{
    private readonly Context _context;
    private readonly ISearchService<T> _searchService;
    private readonly ICacheRequestBuilder _cacheRequestBuilder;
    private readonly IAirportCurrencyService _airportCurrencyService;
    private readonly IFlightCacheConverter _flightCacheConverter;
    private readonly ICacheTimeoutConfiguration _cacheTimeoutConfiguration;
    private readonly IProviderSettings _providerSettings;

    private readonly RequestBuildSettings _outboundRequestBuildSettings;
    private readonly RequestBuildSettings _inboundRequestBuildSettings;

    public IncrementalAdultSeatsStrategy(
        Context context,
        ISearchService<T> searchService,
        IAirportCurrencyService airportCurrencyService,
        IFlightCacheConverter flightCacheConverter,
        ICacheTimeoutConfiguration cacheTimeoutConfiguration,
        ICacheRequestBuilder cacheRequestBuilder,
        IProviderSettings providerSettings)
    {
        _context = context;
        _searchService = searchService;
        _airportCurrencyService = airportCurrencyService;

        _flightCacheConverter = flightCacheConverter;
        _cacheTimeoutConfiguration = cacheTimeoutConfiguration;
        _cacheRequestBuilder = cacheRequestBuilder;
        _providerSettings = providerSettings;

        _outboundRequestBuildSettings = new RequestBuildSettings();
        _inboundRequestBuildSettings = new RequestBuildSettings { SkipSQLSave = true };
    }

    public async Task<IReadOnlyCollection<CacheRequest>> Execute(T queueElement, CancellationToken ct)
    {
        string? departureCurrency = null;
        var cacheRequests = new List<CacheRequest[]>();
        var seatsForKnownPrices = 0;
        for (var seatsCount = _context.InitialSeatsCountToCheck; seatsCount <= _context.MaxSeatsCountToCheck; seatsCount++)
        {
            if (seatsForKnownPrices >= seatsCount) continue;

            queueElement.PaxConfiguration = $"{seatsCount}.0.0.0";
            var (query, searchResponse) = await _searchService.Search(queueElement, queueElement.FeatureName ?? "", ct);
            if (searchResponse?.Data is null) break;

            departureCurrency = searchResponse.Currency;
            cacheRequests.Add(CreateCacheRequests(searchResponse, query, queueElement).ToArray());

            var totalFaresLeft = searchResponse.Data.Min(f => f.Legs.Min(l => l.TotalFaresLeft));
            if (totalFaresLeft <= seatsCount) break;

            var seatsRemaining = searchResponse.Data.Min(f => f.Legs.Min(l => l.Segments.Min(s => s.SeatsRemaining)));
            if (seatsRemaining is null) break;

            seatsForKnownPrices = seatsRemaining.Value;
        }

        if (cacheRequests.All(x => x.Length == 0))
        {
            return [];
        }

        // if we request flights in specific currency it does not mean that this is airport's default
        if (string.IsNullOrWhiteSpace(queueElement.Currency))
        {
            _ = _airportCurrencyService.Update(queueElement.DepartureCode, queueElement.ProviderCode.ToString() ?? "", departureCurrency!, ct);
        }

        return cacheRequests.MergeCacheRequests();
    }

    private IEnumerable<CacheRequest> CreateCacheRequests(
        SearchResponse? searchResponse,
        SearchFlightsProviderQuery query,
        QueueElement message)
    {
        if (searchResponse?.Data is null)
        {
            yield break;
        }

        var allFlights = searchResponse.Data;
        var timeoutConfig = _cacheTimeoutConfiguration.GetConfiguration.Result;

        // can't think of a better solution until proper converter is implemented
        // and logic for deleting flights is moved to the CacheRequestConsumer
        // or we need to get in response whether provider returns RT as 2 OW flights
        // in the end there should always be only one CacheRequest - to preserve correct source - and CacheRequestConsumer should handle the rest
        // TODO: simplify when proper converter is implemented https://eskygroup.atlassian.net/browse/FCACHE-1699
        var returnsRtAsSeparateOw = _providerSettings.ReturnsRtAsSeparateOw(message.ProviderCode, message.Supplier);
        if (!returnsRtAsSeparateOw)
        {
            var cacheFlights = _flightCacheConverter.Convert(allFlights, message, timeoutConfig, query);
            var cacheRequest = _cacheRequestBuilder
                .Build(cacheFlights, query, message, new RequestBuildSettings())
                .ApplyGroupName($"{message.DepartureCode}-{message.ArrivalCode}")
                .ApplyPaxCounts(message, query);
            yield return cacheRequest;
            yield break;
        }

        var outboundFlights = _flightCacheConverter.Convert(
            allFlights.Where(s => !s.IsReturn ?? s.GetDepartureAirportCode() == message.DepartureCode),
            message,
            timeoutConfig,
            query
        );

        if (outboundFlights.Count == 0)
        {
            yield return _cacheRequestBuilder.Build(
                    outboundFlights,
                    query,
                    message.ForOutbound(),
                    _outboundRequestBuildSettings
                )
                .ApplyGroupName($"{message.DepartureCode}-{message.ArrivalCode}")
                .ApplyPaxCounts(message, query);
        }
        else
        {
            var groupedOutboundFlights = outboundFlights.GroupBy(x => (x.Legs[0].DepartureCode, x.Legs[0].ArrivalCode));

            foreach (var groupedFlights in groupedOutboundFlights)
            {
                yield return _cacheRequestBuilder.Build(
                        groupedFlights.ToList(),
                        query,
                        message.ForOutbound(),
                        _outboundRequestBuildSettings
                    )
                    .ApplyGroupName($"{groupedFlights.Key.DepartureCode}-{groupedFlights.Key.ArrivalCode}")
                    .ApplyPaxCounts(message, query);
            }
        }

        var inboundQueueElement = message.ForInbound();
        if (inboundQueueElement is null)
        {
            yield break;
        }

        var inboundFlights = _flightCacheConverter.Convert(
            allFlights.Where(s => s.IsReturn ?? s.GetDepartureAirportCode() == message.ArrivalCode),
            message,
            timeoutConfig,
            query
        );

        if (inboundFlights.Count == 0)
        {
            yield return _cacheRequestBuilder.Build(
                inboundFlights,
                query,
                inboundQueueElement,
                _inboundRequestBuildSettings
            )
            .ApplyGroupName($"{message.DepartureCode}-{message.ArrivalCode}")
            .ApplyPaxCounts(message, query);
        }
        else
        {
            var groupedInboundFlights = inboundFlights.GroupBy(x => (x.Legs[0].DepartureCode, x.Legs[0].ArrivalCode));

            foreach (var groupedFlights in groupedInboundFlights)
            {
                yield return _cacheRequestBuilder.Build(
                        groupedFlights.ToList(),
                        query,
                        inboundQueueElement,
                        _inboundRequestBuildSettings
                    )
                    .ApplyGroupName($"{groupedFlights.Key.ArrivalCode}-{groupedFlights.Key.DepartureCode}")
                    .ApplyPaxCounts(message, query);
            }
        }
    }
}