using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public class RoundTripAsTwoOneWayWithDepartureAirportCurrencyExt<T> : ISearchStrategy<T> where T : QueueElement, new()
{
    private readonly IExtension _extension;
    private readonly ISearchStrategy<T> _searchStrategy;
    private readonly IAirportCurrencyService _airportCurrencyService;

    public RoundTripAsTwoOneWayWithDepartureAirportCurrencyExt(IExtension extension, ISearchStrategy<T> searchStrategy, IAirportCurrencyService airportCurrencyService)
    {
        _extension = extension;
        _searchStrategy = searchStrategy;
        _airportCurrencyService = airportCurrencyService;
    }

    public async Task<IReadOnlyCollection<CacheRequest>> Execute(T queueElement, CancellationToken ct)
    {
        var result = new List<CacheRequest>();

        var outboundElement = queueElement.ForOutbound();
        var ob = await _searchStrategy.Execute(outboundElement, ct);
        result.AddRange(ob);

        var sameCurrency = await _airportCurrencyService.SameCurrency(outboundElement.DepartureCode, outboundElement.ArrivalCode, outboundElement.ProviderCode!.ToString());
        if (sameCurrency)
        {
            _extension.OnSameCurrency(ob);
            return result;
        }

        var inboundElement = queueElement.ForInbound();
        if (inboundElement is null)
        {
            return result;
        }

        inboundElement.Currency = await _airportCurrencyService.Get(outboundElement.DepartureCode, outboundElement.ProviderCode!.ToString(), ct);
        var ib = await _searchStrategy.Execute(inboundElement, ct);
        _extension.OnDifferentCurrency(ib);
        result.AddRange(ib);

        return result;
    }
}