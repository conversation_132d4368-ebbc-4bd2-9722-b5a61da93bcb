using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public class DiffCurrencyOneWayBothDirectionsStrategy<T> : ISearchStrategy<T> where T : QueueElement, new()
{
    private readonly ISearchStrategy<T> _searchStrategy;
    private readonly IAirportCurrencyService _airportCurrencyService;

    public DiffCurrencyOneWayBothDirectionsStrategy(
        ISearchStrategy<T> searchStrategy,
        IAirportCurrencyService airportCurrencyService)
    {
        _searchStrategy = searchStrategy;
        _airportCurrencyService = airportCurrencyService;
    }

    public async Task<IReadOnlyCollection<CacheRequest>> Execute(T queueElement, CancellationToken ct)
    {
        var cacheRequests = new List<CacheRequest>();

        var outboundFlights = await _searchStrategy.Execute(queueElement, ct);
        cacheRequests.AddRange(outboundFlights);

        if (queueElement.IsRoundTrip)
        {
            return cacheRequests;
        }

        var providerName = queueElement.ProviderCode!.Value.ToString();
        var sameCurrency = await _airportCurrencyService.SameCurrency(queueElement.DepartureCode, queueElement.ArrivalCode, providerName);

        if (!sameCurrency)
        {
            var departureAirportCurrency = await _airportCurrencyService.Get(queueElement.DepartureCode, providerName, ct);
            var reversedRoute = queueElement.ReverseRoute();
            reversedRoute.Currency = departureAirportCurrency;
            var inboundFlights = await _searchStrategy.Execute(reversedRoute, ct);
            cacheRequests.AddRange(inboundFlights);
        }

        return cacheRequests;
    }
}