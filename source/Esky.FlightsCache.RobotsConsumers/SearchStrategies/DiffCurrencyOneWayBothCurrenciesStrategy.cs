using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public class DiffCurrencyOneWayBothCurrenciesStrategy<T> : ISearchStrategy<T> where T : QueueElement, new()
{
    private readonly IExtension _extension;
    private readonly ISearchStrategy<T> _searchStrategy;
    private readonly IAirportCurrencyService _airportCurrencyService;

    public DiffCurrencyOneWayBothCurrenciesStrategy(IExtension extension, ISearchStrategy<T> searchStrategy, IAirportCurrencyService airportCurrencyService)
    {
        _extension = extension;
        _searchStrategy = searchStrategy;
        _airportCurrencyService = airportCurrencyService;
    }

    public async Task<IReadOnlyCollection<CacheRequest>> Execute(T queueElement, CancellationToken ct)
    {
        var cacheRequests = new List<CacheRequest>();

        var withDepartureCurrency = await _searchStrategy.Execute(queueElement, ct);
        cacheRequests.AddRange(withDepartureCurrency);

        if (withDepartureCurrency.All(e => e.Flights.Count == 0))
        {
            return cacheRequests;
        }

        var providerName = queueElement.ProviderCode!.Value.ToString();
        var sameCurrency = await _airportCurrencyService.SameCurrency(queueElement.DepartureCode, queueElement.ArrivalCode, providerName);

        if (sameCurrency)
        {
            _extension.OnSameCurrency(cacheRequests);
            return cacheRequests;
        }

        if (queueElement.IsRoundTrip)
        {
            return cacheRequests;
        }

        var arrivalAirportCurrency = await _airportCurrencyService.Get(queueElement.ArrivalCode, providerName, ct);
        var arrivalCurrencyElement = new T { InnerElement = queueElement, Currency = arrivalAirportCurrency };
        var withArrivalCurrency = await _searchStrategy.Execute(arrivalCurrencyElement, ct);
        _extension.OnDifferentCurrency(withArrivalCurrency);
        cacheRequests.AddRange(withArrivalCurrency);

        return cacheRequests;
    }
}