using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing.Helpers;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Esky.FlightsCache.RobotsConsumers.SearchStrategies;

public static class CacheRequestExtensions
{
    /// <summary>
    /// Adds the inbound separation option to each CacheRequest in the collection.
    /// </summary>
    /// <typeparam name="T">The type of the collection, which must implement IEnumerable&lt;CacheRequest&gt;.</typeparam>
    /// <param name="self">The collection of CacheRequest objects.</param>
    /// <returns>The modified collection of CacheRequest objects.</returns>
    public static T AddInboundSeparationOption<T>(this T self) where T : IEnumerable<CacheRequest>
    {
        ArgumentNullException.ThrowIfNull(self, nameof(self));

        foreach (var cacheRequest in self)
        {
            cacheRequest.AddInboundSeparationOption();
        }

        return self;
    }

    /// <summary>
    /// Adds the inbound separation option to the CacheRequest.
    /// </summary>
    /// <param name="self">The CacheRequest object.</param>
    /// <returns>The modified CacheRequest object.</returns>
    public static CacheRequest AddInboundSeparationOption(this CacheRequest self)
    {
        ArgumentNullException.ThrowIfNull(self, nameof(self));

        foreach (var leg in self.Flights?.SelectMany(f => f.Legs) ?? [])
        {
            leg.SeparationOptions ??= new SeparationOptions();
            leg.SeparationOptions.Options |= SeparationOptionEnum.RoundTripInbound;
        }

        return self;
    }

    /// <summary>
    /// Sets the inbound separation option for each CacheRequest in the collection.
    /// </summary>
    /// <typeparam name="T">The type of the collection, which must implement IEnumerable&lt;CacheRequest&gt;.</typeparam>
    /// <param name="self">The collection of CacheRequest objects.</param>
    /// <returns>The modified collection of CacheRequest objects.</returns>
    public static T SetInboundSeparationOption<T>(this T self) where T : IEnumerable<CacheRequest>
    {
        ArgumentNullException.ThrowIfNull(self, nameof(self));

        foreach (var cacheRequest in self)
        {
            cacheRequest.SetInboundSeparationOption();
        }

        return self;
    }

    /// <summary>
    /// Sets the inbound separation option for the CacheRequest.
    /// </summary>
    /// <param name="self">The CacheRequest object.</param>
    /// <returns>The modified CacheRequest object.</returns>
    public static CacheRequest SetInboundSeparationOption(this CacheRequest self)
    {
        ArgumentNullException.ThrowIfNull(self, nameof(self));

        foreach (var leg in self.Flights?.SelectMany(f => f.Legs) ?? [])
        {
            leg.SeparationOptions ??= new SeparationOptions();
            leg.SeparationOptions.Options = SeparationOptionEnum.RoundTripInbound;
        }

        return self;
    }

    public static List<CacheRequest> OverrideProvider(this List<CacheRequest> cacheRequests,
        CompositeQueueElement.Override? overrideSetting)
    {
        if (overrideSetting?.Provider == null && overrideSetting?.Supplier == null)
        {
            return cacheRequests;
        }
        
        cacheRequests.ForEach(x =>
        {
            if (overrideSetting.Provider != null)
            {
                var providerCode = (int)overrideSetting.Provider.ProviderCode!;
                x.SourceDescription.Provider = providerCode.ToString();
                x.SourceDescription.ProviderCode = providerCode;
                x.Flights.ForEach(f => f.ProviderCode = providerCode);
            }

            if (overrideSetting.Supplier != null)
            {
                var requestedSupplier = x.SourceDescription.Supplier;
                x.SourceDescription.Supplier = overrideSetting.Supplier.SupplierName;
                x.Flights.ForEach(f =>
                {
                    if (f.Supplier == requestedSupplier) f.Supplier = overrideSetting.Supplier.SupplierName;
                });
            }
        });
        return cacheRequests;
    }
    
    public static List<CacheRequest> MergeCacheRequests(this List<CacheRequest[]> cacheRequests)
    {
        var firstResultSet = cacheRequests[0].ToList();
        if (cacheRequests.Count == 1)
        {
            return firstResultSet;
        }

        var legsToMergePrices = firstResultSet
            .SelectMany(r => r.Flights.SelectMany(f => f.Legs).Where(l => l.AvailableSeatsCount is not null))
            .Select(l => (LegHash(l), leg: l));

        var resultsWithAdditionalPrices = cacheRequests.Skip(1);
        var additionalPrices = resultsWithAdditionalPrices
            .SelectMany(r => r.SelectMany(cr => cr.Flights.SelectMany(f => f.Legs)))
            .GroupBy(LegHash)
            .ToDictionary(x => x.Key, group => group.ToArray());

        foreach (var (legHash, leg) in legsToMergePrices)
        {
            if (additionalPrices.TryGetValue(legHash, out var legs))
            {
                leg.AdultPrices = GetMergedPaxPrice(leg, legs, l => l.AdultPrices);
                leg.YouthPrices = GetMergedPaxPrice(leg, legs, l => l.YouthPrices);
                leg.ChildPrices = GetMergedPaxPrice(leg, legs, l => l.ChildPrices);
                leg.InfantPrices = GetMergedPaxPrice(leg, legs, l => l.InfantPrices);
            }
        }

        var maxPaxConfiguration = cacheRequests
            .SelectMany(r => r.Select(c => c.SourceDescription.PaxConfiguration))
            .MaxBy(p => new PaxConfiguration(p).SeatsCount);
        firstResultSet.ForEach(r => r.SourceDescription.PaxConfiguration = maxPaxConfiguration);

        return firstResultSet;

        string LegHash(FlightCacheLeg leg)
        {
            // based on Esky.FlightsCache.Processing.Helpers.FlightCacheExtensions.GetSegmentsHash(this FlightCacheLeg leg)
            var sr = new StringBuilder();
            var firstDepartureDate = leg.Segments[0].DepartureDate;
            var index = 0;

            sr.Append(firstDepartureDate.Date.ToString("d"));
            foreach (var s in leg.Segments)
            {
                var depDiff = index == 0 ? 0 : (s.DepartureDate.Date - firstDepartureDate.Date).Days;
                var arrDiff = (s.ArrivalDate.Date - firstDepartureDate.Date).Days;

                var coder = new SegmentHashCoder
                {
                    DepartureCode = s.DepartureCode,
                    DepartureTime = s.DepartureDate.ToString("HH:mm"),
                    DepartureDiff = depDiff,
                    ArrivalCode = s.ArrivalCode,
                    ArrivalTime = s.ArrivalDate.ToString("HH:mm"),
                    ArrivalDiff = arrDiff,
                    AirlineCode = s.AirlineCode,
                    OperatingAirlineCode = s.OperatingAirlineCode,
                    FlightNumber = s.FlightNumber,
                    OperatingFlightNumber = s.OperatingFlightNumber,
                    BookingClass = s.BookingClass,
                    FareDetails = new FareDetails { OfficeId = s.FareDetails?.OfficeId }
                };
                sr.Append(coder);
                sr.Append('|');

                index++;
            }

            return sr.ToString();
        }

        List<PriceCacheEntry> GetMergedPaxPrice(
            FlightCacheLeg baseLeg,
            IEnumerable<FlightCacheLeg> additionalLegs,
            Func<FlightCacheLeg, IReadOnlyCollection<PriceCacheEntry>?> selector)
        {
            var prices = (selector(baseLeg) ?? [])
                .Concat(
                    additionalLegs
                        .Where(l => l.CurrencyCode == baseLeg.CurrencyCode)
                        .SelectMany(l => selector(l) ?? [])
                )
                .DistinctBy(p => (p.BasePrice, p.TaxPrice))
                .ToList();

            return prices;
        }
    }
}