#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
ENV ASPNETCORE_HTTP_PORTS=80
WORKDIR /app
RUN apt-get update && apt-get install -y libc-dev && apt-get clean

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["nuget.config", ""]
COPY ["source/Esky.FlightsCache.RobotsConsumers/Esky.FlightsCache.RobotsConsumers.csproj", "source/Esky.FlightsCache.RobotsConsumers/"]
COPY ["source/Esky.FlightsCache.PartnerSettings/Esky.FlightsCache.PartnerSettings.csproj", "source/Esky.FlightsCache.PartnerSettings/"]
COPY ["source/Esky.FlightsCache.Robots.Messages/Esky.FlightsCache.Robots.Messages.csproj", "source/Esky.FlightsCache.Robots.Messages/"]
COPY ["source/Esky.FlightsCache.Robots/Esky.FlightsCache.Robots.csproj", "source/Esky.FlightsCache.Robots/"]
COPY ["source/Esky.FlightsCache.CurrencyProvider/Esky.FlightsCache.CurrencyProvider.csproj", "source/Esky.FlightsCache.CurrencyProvider/"]
RUN dotnet restore "source/Esky.FlightsCache.RobotsConsumers/Esky.FlightsCache.RobotsConsumers.csproj"
COPY . .
WORKDIR "/src/source/Esky.FlightsCache.RobotsConsumers"
RUN dotnet build "Esky.FlightsCache.RobotsConsumers.csproj" -c Release -o /app/build

FROM build AS test
WORKDIR "/src/"
COPY ["Esky.FlightsCache.RobotsConsumers.Tests/Esky.FlightsCache.RobotsConsumers.Tests.csproj", "Esky.FlightsCache.RobotsConsumers.Tests/"]
RUN dotnet test "Esky.FlightsCache.RobotsConsumers.Tests/Esky.FlightsCache.RobotsConsumers.Tests.csproj"

FROM build AS publish
RUN dotnet publish "Esky.FlightsCache.RobotsConsumers.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Esky.FlightsCache.RobotsConsumers.dll"]