using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;
using IPartnerSettingsService = Esky.FlightsCache.PartnerSettings.IPartnerSettingsService;
using Esky.Providers.Content.Contract.Metadata;
using System.Text;

namespace Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

public class AmadeusLiveCheckClient : IAmadeusLiveCheckClient
{
    private const string _rateLimiterAlcKey = "ALC";

    private readonly ILogger<AmadeusLiveCheckClient> _logger;
    private readonly string _fallbackUrl;
    private readonly IPartnerSettingsService _officeSettings;
    private readonly HttpClient _httpClient;
    private readonly IRateLimiter _rateLimiter;
    private readonly string _apiUrl;

    public AmadeusLiveCheckClient(
        ILogger<AmadeusLiveCheckClient> logger,
        IPartnerSettingsService officeSettings,
        HttpClient httpClient,
        IOptions<Options> options,
        IRateLimiter rateLimiter)
    {
        _logger = logger;
        _officeSettings = officeSettings;
        _httpClient = httpClient;
        _rateLimiter = rateLimiter;
        _fallbackUrl = options.Value.RuntimeSettingsFallbackUrl;        
        _apiUrl = $"{options.Value.ApiUrl}AmadeusLiveCheck";
    }

    public async Task<IReadOnlyCollection<FlightDto>> Search(
        string officeId,
        IReadOnlyCollection<Esky.FlightsCache.Contract.FlightDto> flights,
        PaxConfiguration paxConfiguration,
        string source,
        CancellationToken cancellationToken)
    {
        var resultFlights = new List<FlightDto>();

        var officeIds = flights
            .SelectMany(f => f.Legs)
            .SelectMany(l => l.Segments)
            .Select(s => s.FareDetails?.OfficeId ?? officeId)
            .Concat([officeId])
            .Distinct();

        var settings = await Task.WhenAll(officeIds.Select(x => _officeSettings.GetOfficeSettingsAsync(x)));
        var settingsMap = settings.ToDictionary(x => x.OfficeId);
        if (!settingsMap.ContainsKey(officeId))
        {
            _logger.LogWarning("Unknown office id: {OfficeId}", officeId);
            return [];
        }

        var requests = flights.CreateAmadeusLiveCheckRequests(paxConfiguration, settingsMap, officeId, _fallbackUrl);
        foreach (var request in requests)
        {
            Metrics.AmadeusLiveCheck.ReportAlcRequestedItineraries(request.Legs.Sum(leg => leg.Options.Length), source);

            var serializedRequest = JsonConvert.SerializeObject(request);
            var requestContent = new StringContent(serializedRequest, Encoding.UTF8, "application/json");
            var pipeline = _rateLimiter.GetPipeline(_rateLimiterAlcKey);
            var response = await pipeline.ExecuteAsync(async token =>
            {
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, _apiUrl)
                {
                    Content = requestContent,
                    Headers = { { HeaderNames.Metadata, CreateMetadataHeader(source) } }
                };

                return await _httpClient.SendAsync(httpRequest, token);
            }, cancellationToken);
            Metrics.AmadeusLiveCheck.ReportAlcRequest(source, officeId, response.StatusCode);
            if (response.StatusCode == HttpStatusCode.NoContent)
            {
                continue;
            }
            var stringContent = await response.Content.ReadAsStringAsync(cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Got error for officeId [{OfficeId}] with message {ErrorMessage}", officeId, stringContent);
                continue;
            }
            var deserializedContent = JsonConvert.DeserializeObject<SearchResponse>(stringContent);
            if (deserializedContent is null)
            {
                continue;
            }

            deserializedContent.Data.ForEach(f => f.Currency ??= deserializedContent.Currency);

            Metrics.AmadeusLiveCheck.ReportAlcReturnedItineraries(deserializedContent.Data.Sum(f => f.Legs.Count), source);
            resultFlights.AddRange(deserializedContent.Data);
        }

        return resultFlights;
    }

    private string CreateMetadataHeader(string source)
    {
        var header = new SearchMetadata
        {
            IsLive = true,
            Caller = new Caller
            {
                ClientName = "esky-flightscache-robotsconsumers",
                SourceName = "Cache",
                FeatureName = "ALC",
                FeatureDetails = source
            }
        };
        return header.Serialize();
    }

    public sealed class Options
    {
        public required string ApiUrl { get; set; }
        public required string RuntimeSettingsFallbackUrl { get; set; }
        public required int CacheLifetimeInHours { get; set; }
    }
}