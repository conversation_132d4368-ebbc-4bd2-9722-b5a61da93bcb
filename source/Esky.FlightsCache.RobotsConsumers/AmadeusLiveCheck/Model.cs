using System;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

public class AmadeusLiveCheckRequest
{
    public int Adults { get; set; }
    public int Children { get; set; }
    public int Infants { get; set; }
    public int Youths { get; set; }
    public ServiceClass ServiceClass { get; set; }
    public string? CurrencyCode { get; set; }
    public required AmadeusLiveCheckContext Context { get; set; }
    public required AmadeusLiveCheckLeg[] Legs { get; set; }
}

public enum ServiceClass
{
    Any = 0,
    Economy = 1,
    First = 2,
    Business = 3,
    EconomyPremium = 4
}

public class AmadeusLiveCheckContext
{
    public required AmadeusRuntimeSettings RuntimeSettings { get; set; }
    public bool UseMajorCabinQualifier { get; set; }
    public bool OverrideCurrencyConversion { get; set; }
    public bool TourOperatorFares { get; set; }
}

public class AmadeusRuntimeSettings 
{
    public required string AgencyCode { get; set; }
    public string? CurrencyCode { get; set; }
    public List<string>? TourCodes { get; set; }
    public required string Url { get; set; }
    public required string User { get; set; }
    public required string Password { get; set; }
}

public class AmadeusLiveCheckLeg
{
    public required string Origin { get; set; }
    public required string Destination { get; set; }
    public required DateTime DepartureDate { get; set; }
    public required AmadeusLiveCheckOption[] Options { get; set; }
}

public class AmadeusLiveCheckOption
{
    public required AmadeusLiveCheckSegment[] Segments { get; set; }
}

public class AmadeusLiveCheckSegment
{
    public required string Origin { get; set; }
    public required string Destination { get; set; }
    public required DateTime DepartureLocal { get; set; }
    public required DateTime ArrivalLocal { get; set; }
    public required string Airline { get; set; }
    public string? Aircraft { get; set; }
    public required string FlightNo { get; set; }
}