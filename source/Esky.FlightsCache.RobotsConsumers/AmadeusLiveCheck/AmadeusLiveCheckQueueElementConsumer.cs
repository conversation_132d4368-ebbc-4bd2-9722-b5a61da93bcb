using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Contract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.CacheApi;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FlightDto = Esky.Flights.Integration.Providers.Contract.FlightDto;

namespace Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

public class AmadeusLiveCheckQueueElementConsumer : IConsumer<AmadeusLiveCheckQueueElement>
{
    private readonly ICacheApiClient _cacheApiClient;
    private readonly IPartnerSettingsService _partnerSettings;
    private readonly ISearchFlightsQueryBuilder _searchFlightsQueryBuilder;
    private readonly IFlightCacheConverter _flightCacheConverter;
    private readonly ICacheRequestBuilder _cacheRequestBuilder;
    private readonly RequestBuildSettings _requestBuildSettings = new();
    private readonly IAmadeusLiveCheckClient _amadeusLiveCheckClient;
    private readonly ILogger<AmadeusLiveCheckQueueElementConsumer> _logger;
    private readonly IGenericApiFlightProvider _provider;
    private readonly AmadeusLiveCheckClient.Options _options;

    public AmadeusLiveCheckQueueElementConsumer(
        ICacheApiClient cacheApiClient,
        IPartnerSettingsService partnerSettings,
        ISearchFlightsQueryBuilder searchFlightsQueryBuilder,
        IFlightCacheConverter flightCacheConverter,
        ICacheRequestBuilder cacheRequestBuilder,
        IAmadeusLiveCheckClient amadeusLiveCheckClient,
        ILogger<AmadeusLiveCheckQueueElementConsumer> logger,
        IGenericApiFlightProvider provider,
        IOptions<AmadeusLiveCheckClient.Options> options)
    {
        _cacheApiClient = cacheApiClient;
        _partnerSettings = partnerSettings;
        _searchFlightsQueryBuilder = searchFlightsQueryBuilder;
        _flightCacheConverter = flightCacheConverter;
        _cacheRequestBuilder = cacheRequestBuilder;
        _amadeusLiveCheckClient = amadeusLiveCheckClient;
        _logger = logger;
        _provider = provider;
        _options = options.Value;
    }

    public async Task Consume(ConsumeContext<AmadeusLiveCheckQueueElement> context)
    {
        context.Message.PaxConfiguration ??= PaxConfigurations.AdultChildInfant;
        context.Message.ProviderCode ??= ProviderCodeEnum.Amadeus;

        var timeout = (await _partnerSettings.GetPartnerSettingsAsync(context.Message.PartnerCode)).SpecialOccasionsSettings.GetCacheTimeOutConfiguration();

        foreach (var (query, _) in _searchFlightsQueryBuilder.BuildAll(context.Message))
        {
            if (context.CancellationToken.IsCancellationRequested)
            {
                return;
            }

            var flights = await GetFlights(context, query);
            if (flights.Count == 0)
            {
                Metrics.AmadeusLiveCheck.NoFlightsCounter.Add(1);
                continue;
            }

            var flightsList = _flightCacheConverter.Convert(flights, context.Message, timeout, query);

            var expirationDate = DateTime.UtcNow.AddHours(_options.CacheLifetimeInHours);
            foreach (var flight in flightsList) flight.ExpirationDate = expirationDate;
            
            var cacheRequest = _cacheRequestBuilder.Build(flightsList, query, context.Message, _requestBuildSettings);
            await context.Publish(cacheRequest, context.CancellationToken);
        }
    }

    private async Task<IReadOnlyCollection<FlightDto>> GetFlights(ConsumeContext<AmadeusLiveCheckQueueElement> context, SearchFlightsProviderQuery query)
    {
        var sourceName = context.Message.SourceName;
        var cacheApiRequest = ToCacheApiRequest(query, context.Message);
        var cacheSearchResponse = await _cacheApiClient.GetFlights(cacheApiRequest, context.CancellationToken);
        if (cacheSearchResponse.Data.Count != 0)
        {
            var singleTicketFlights = cacheSearchResponse.Data
                .Where(e => e.Length == 1)
                .Select(e => e[0])
                .ToList();

            if (singleTicketFlights.Count == 0)
            {
                Metrics.AmadeusLiveCheck.NoSingleTicketsCounter.Add(1);
                return [];
            }

            Metrics.AmadeusLiveCheck.ReportRouteExistsInCache(sourceName);
            return await _amadeusLiveCheckClient.Search(context.Message.OfficeId, singleTicketFlights, context.Message.PaxConfiguration, sourceName, context.CancellationToken);
        }

        LogNoFlightsInCache(context.Message);
        Metrics.AmadeusLiveCheck.ReportRouteNotExistsInCache(sourceName);
        
        context.Message.SourceName = "AmadeusLiveCheck-fallback";
        try
        {
            var searchResponse = await _provider.SearchAsync(
                new ProviderQuery
                {
                    ProviderCode = (int)context.Message.ProviderCode,
                    Query = query,
                    SourceName = sourceName,
                    FeatureName = "ALC"
                },
                context.CancellationToken
            );

            LogProviderSearch(context.Message, searchResponse.Data.Count);
            return searchResponse.Data;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Flights {departureCode}-{arrivalCode} on {departureDay}-{returnDay} not found in Cache, provider's search threw error",
                context.Message.DepartureCode,
                context.Message.ArrivalCode,
                context.Message.DepartureDay.ToString("yyyy-MM-dd"),
                context.Message.ReturnDepartureDay?.ToString("yyyy-MM-dd"));
            throw;
        }
    }

    private static SearchFlightsRequest ToCacheApiRequest(SearchFlightsProviderQuery query, AmadeusLiveCheckQueueElement element)
    {
        var legs = new SearchFlightsRequest.Leg[query.Legs.Count()];

        var firstLeg = query.Legs.First();
        legs[0] = new SearchFlightsRequest.Leg
        {
            DepartureLocation = new Location { Code = firstLeg.DepartureCode },
            ArrivalLocation = new Location { Code = firstLeg.ArrivalCode },
            DepartureDate = firstLeg.DepartureDate
        };

        var isRoundtrip = legs.Length == 2;
        if (isRoundtrip)
        {
            var secondLeg = query.Legs.Skip(1).First();
            legs[1] = new SearchFlightsRequest.Leg
            {
                DepartureLocation = new Location { Code = secondLeg.DepartureCode },
                ArrivalLocation = new Location { Code = secondLeg.ArrivalCode },
                DepartureDate = secondLeg.DepartureDate
            };
        }

        var request = new SearchFlightsRequest
        {
            Legs = legs,
            Providers = new List<Provider> { new() { Code = (int)ProviderCodeEnum.Amadeus, ExcludedAirlines = element.AlcExcludedAirlines, IncludedSuppliers = [element.OfficeId] } },
            PaxConfigurations = [CreatePaxConfiguration(element.PaxConfiguration.ToDefaultPaxConfigurationIfNullOrEmpty())],
            ReturnMultiTicketFlights = false,
            ExpiresAfter = DateTime.UtcNow.AddDays(-3), // use expired flights too for increased coverage
            LimitPerCell = 1000, // big enough to make sure we will get all available flights, eg. RT + 2xOW
        };

        return request;
    }
    private static Contract.PaxConfiguration CreatePaxConfiguration(Robots.PaxConfiguration paxConfiguration)
    {
        return new Contract.PaxConfiguration(paxConfiguration.Adult, paxConfiguration.Youth, paxConfiguration.Child, paxConfiguration.Infant);
    }

    /// <summary>
    /// used in Grafana charts https://grafanasre.eskyspace.com/d/hQGrIKZIk/amadeuslivecheck
    /// </summary>
    private void LogNoFlightsInCache(QueueElement message)
    {
        _logger.LogInformation("Flights {DepartureCode}-{ArrivalCode} on {DepartureDay}-{ReturnDepartureDay} not found in Cache",
            message.DepartureCode,
            message.ArrivalCode,
            message.DepartureDay.ToString("yyyy-MM-dd"),
            message.ReturnDepartureDay?.ToString("yyyy-MM-dd")
        );
    }

    private void LogProviderSearch(QueueElement message, int? flightsCount)
    {
        _logger.LogInformation("Flights {departureCode}-{arrivalCode} on {departureDay}-{returnDay} not found in Cache, provider's search returned {flightsCount} flights",
            message.DepartureCode,
            message.ArrivalCode,
            message.DepartureDay.ToString("yyyy-MM-dd"),
            message.ReturnDepartureDay?.ToString("yyyy-MM-dd"),
            flightsCount);
    }
}