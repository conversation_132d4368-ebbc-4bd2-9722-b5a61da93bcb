using Esky.FlightsCache.Contract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Polly;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

public static class AmadeusLiveCheckExtensions
{
    private const string AmadeusServerName = "noded1";
    private static readonly Regex AmadeusUrlRegex = new Regex(@"(https://)(test|production.*)", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public static IServiceCollection AddAmadeusLiveCheck(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AmadeusLiveCheckClient.Options>(configuration.GetSection("AmadeusLiveCheck"));
        services.AddHttpClient<IAmadeusLiveCheckClient, AmadeusLiveCheckClient>((sp, client) =>
            {
                var options = sp.GetRequiredService<IOptions<AmadeusLiveCheckClient.Options>>().Value;
                client.BaseAddress = new Uri(options.ApiUrl);
            })
            .AddTransientHttpErrorPolicy(builder => builder.WaitAndRetryAsync(3, retry => TimeSpan.FromSeconds(retry)));
        return services;
    }

    public static IEnumerable<AmadeusLiveCheckRequest> CreateAmadeusLiveCheckRequests(
        this IEnumerable<FlightDto> flights,
        Robots.PaxConfiguration paxConfiguration,
        Dictionary<string, OfficeSettings> settingsMap,
        string fallbackOfficeId,
        string fallbackUrl)
    {
        var grouped = flights.GroupBy(e => new
        {
            DepartureCode = e.Legs[0].Segments[0].OriginAirport,
            ArrivalCode = e.Legs[0].Segments.Last().DestinationAirport,
            DepartureDate = e.Legs[0].Segments[0].DepartureLocalTime.Date,
            ReturnDepartureDate = e.Legs.Count == 2
                ? e.Legs[1].Segments[0].DepartureLocalTime.Date
                : (DateTime?)null,
            OfficeId = e.Legs[0].Segments[0].FareDetails?.OfficeId,
            IsTourOperator = e.Legs.All(l => l.Segments.All(s => s.FareDetails?.OfferType is OfferType.TourOperator))
        });

        var alcRequests = from g in grouped
                let settings = g.Key.OfficeId is not null && settingsMap.TryGetValue(g.Key.OfficeId, out var s) ? s : settingsMap[fallbackOfficeId]
                // ALC allows up to 50 "options" in total per request, roundtrip is 2 options (outbound, inbound)
                from chunk in g.Chunk(g.Key.ReturnDepartureDate.HasValue ? 25 : 50)
                select chunk.CreateAmadeusLiveCheckRequest(paxConfiguration, settings, fallbackUrl, g.Key.IsTourOperator);

        return alcRequests
            .Where(request => request is not null);;
    }

    private static AmadeusLiveCheckRequest CreateAmadeusLiveCheckRequest(
        this IReadOnlyCollection<FlightDto> flights,
        Robots.PaxConfiguration paxConfiguration,
        OfficeSettings settings,
        string fallbackUrl,
        bool isTourOperator)
    {
        var request = new AmadeusLiveCheckRequest
        {
            Adults = paxConfiguration.Adult,
            Youths = paxConfiguration.Youth,
            Children = paxConfiguration.Child,
            Infants = paxConfiguration.Infant,
            ServiceClass = ServiceClass.Any,
            Context = new AmadeusLiveCheckContext
            {
                RuntimeSettings = new AmadeusRuntimeSettings
                {
                    Url = AmendUrl(settings.Url ?? settings.AlternativeUrl ?? fallbackUrl),
                    User = settings.User ?? settings.AlternativeUser,
                    Password = DecodePassword(settings.Password ?? settings.AlternativePassword),
                    AgencyCode = settings.OfficeId,
                    CurrencyCode = settings.CurrencyCode
                },
                TourOperatorFares = isTourOperator
            },
            CurrencyCode = flights.First().Prices[0].Currency,
            Legs = flights.ToAmadeusLiveCheckLegs().ToArray()
        };
        return request;
    }
    private static string AmendUrl(string url)
    {
        // Adds node name to amadeus server URL - to enable soap 4.0 header compatibility
        return AmadeusUrlRegex.Replace(url, $"$1{AmadeusServerName}.$2");
    }

    private static string DecodePassword(string password)
    {
        var data = Convert.FromBase64String(password);
        return Encoding.UTF8.GetString(data);
    }

    private static IEnumerable<AmadeusLiveCheckLeg> ToAmadeusLiveCheckLegs(this IReadOnlyCollection<FlightDto> flights)
    {
        var departureCode = flights.First().Legs[0].Segments[0].OriginAirport;
        var arrivalCode = flights.First().Legs[0].Segments.Last().DestinationAirport;

        yield return flights.ToAmadeusLiveCheckLeg(departureCode, arrivalCode, legIndex: 0);

        var isRoundTrip = flights.First().Legs.Count == 2;
        if (isRoundTrip)
        {
            yield return flights.ToAmadeusLiveCheckLeg(arrivalCode, departureCode, legIndex: 1);
        }
    }

    private static AmadeusLiveCheckLeg ToAmadeusLiveCheckLeg(
        this IReadOnlyCollection<FlightDto> flights,
        string departureCode,
        string arrivalCode,
        int legIndex)
    {
        var leg = new AmadeusLiveCheckLeg
        {
            Origin = departureCode,
            Destination = arrivalCode,
            DepartureDate = flights.First().Legs[legIndex].Segments.First().DepartureLocalTime,
            Options = flights
                .Select(e => new AmadeusLiveCheckOption
                {
                    Segments = e.Legs[legIndex]
                            .Segments
                            .Select(s => new AmadeusLiveCheckSegment
                            {
                                Origin = s.OriginAirport,
                                Destination = s.DestinationAirport,
                                DepartureLocal = s.DepartureLocalTime,
                                ArrivalLocal = s.ArrivalLocalTime,
                                FlightNo = s.FlightNumber,
                                Airline = s.AirlineCode,
                                Aircraft = s.AircraftCode
                            }
                            ).ToArray()
                }
                ).ToArray()
        };

        return leg;
    }
}