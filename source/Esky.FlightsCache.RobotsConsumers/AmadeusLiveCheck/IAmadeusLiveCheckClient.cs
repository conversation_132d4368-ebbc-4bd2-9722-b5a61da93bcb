using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

public interface IAmadeusLiveCheckClient
{
    Task<IReadOnlyCollection<FlightDto>> Search(
        string officeId,
        IReadOnlyCollection<Esky.FlightsCache.Contract.FlightDto> flights,
        PaxConfiguration paxConfiguration,
        string source,
        CancellationToken cancellationToken
    );
}