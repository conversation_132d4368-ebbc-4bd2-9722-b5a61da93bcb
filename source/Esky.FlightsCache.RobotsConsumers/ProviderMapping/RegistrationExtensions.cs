using Esky.FlightsCache.Database;
using Esky.FlightsCache.ProviderMapping;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.RobotsConsumers.ProviderMapping;

public static class RegistrationExtensions
{
    public static IServiceCollection AddProviderMapping(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration["DatabaseSettings:ConnectionString"] ?? "";
        services.AddSingleton<IProviderConfigurationDatabase>(_ =>
            new ProviderConfigurationDatabase(
                new ProviderConfigurationDatabase.Settings { ConnectionUrl = connectionString }));
        services.AddSingleton<IProviderMappingStorage, ProviderMappingStorage>();
        
        return services;
    }
}