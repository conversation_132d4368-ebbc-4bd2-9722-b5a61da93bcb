using Esky.FlightsCache.Database;
using Esky.FlightsCache.ProviderMapping;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.ProviderMapping;

public interface IProviderMappingStorage
{
    Task<string?> GetSupplier(int provider, string airline);
}

public class ProviderMappingStorage : IProviderMappingStorage
{
    private readonly IProviderConfigurationDatabase _database;
    private readonly MemoryCache _cache = new(new MemoryCacheOptions());

    public ProviderMappingStorage(IProviderConfigurationDatabase database)
    {
        _database = database;
    }
    
    public async Task<string?> GetSupplier(int provider, string airline)
    {
        var list = await _cache.GetOrCreateAsync("config", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
            return await _database.GetMappingList();
        });
        
        var config = list?
            .SelectMany(x => x.WriteConfigurations)
            .FirstOrDefault(x => x.ProviderCode == provider && x.AirlineCodes.Contains(airline));

        return config?.Supplier;
    }
}