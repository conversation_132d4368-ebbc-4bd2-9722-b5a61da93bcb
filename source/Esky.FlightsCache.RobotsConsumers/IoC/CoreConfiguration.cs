using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.OpenTelemetry;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots.BigQuery;
using Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;
using Esky.FlightsCache.RobotsConsumers.CacheApi;
using Esky.FlightsCache.RobotsConsumers.Configuration;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.DirectRyanair;
using Esky.FlightsCache.RobotsConsumers.HotelConsumers;
using Esky.FlightsCache.RobotsConsumers.Jet2;
using Esky.FlightsCache.RobotsConsumers.ProviderMapping;
using Esky.FlightsCache.RobotsConsumers.ProviderSearchRequest;
using Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsConsumers.SSC;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.FlightsCache.ServiceBus;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.BigQuery.V2;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Net.Http;

namespace Esky.FlightsCache.RobotsConsumers.IoC
{
    public static class CoreConfiguration
    {
        private const string _appName = "esky-flightscache-robotsconsumers";

        public static IServiceCollection ConfigureIoC(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddAmadeusLiveCheck(configuration);
            services.AddRyanairRouteFeeRefreshing(configuration);
            services.AddProviderMapping(configuration);

            services.AddScoped<ISearchService<SSCQueueElement>>(provider =>
            {
                var sscProvider = provider.GetRequiredService<ISSCProvider>();
                var queryBuilder = provider.GetRequiredService<ISearchFlightsQueryBuilder>();
                return new SSCSearchServiceAdapter(sscProvider, queryBuilder);
            });
            services.AddScoped(typeof(ISearchService<>), typeof(SearchService<>));
            services.AddSingleton<IStalenessEvaluator, StalenessEvaluator>();

            // SSC
            services.AddScoped<ISSCCacheRequestService, SSCCacheRequestService>();
            services.AddHttpClient<ISSCProvider, SSCProvider>(
                (provider, client) =>
                {
                    var url = provider.GetRequiredService<IConfiguration>()["SSCProvider:ApiUrl"] ?? throw new Exception("SSCProvider:ApiUrl cannot be null");
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("user-agent", _appName);
                    client.DefaultRequestHeaders.Add("feature", "SSCProvider");
                });
            services.AddHttpClient<ISSCCalendarProvider, SSCCalendarProvider>(
                (provider, client) =>
                {
                    var url = provider.GetRequiredService<IConfiguration>()["SSCProvider:ApiUrl"] ?? throw new Exception("SSCProvider:ApiUrl cannot be null");
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("user-agent", _appName);
                    client.DefaultRequestHeaders.Add("feature", "SSCCalendarProvider");
                });
            services.AddHttpClient(Options.DefaultName, client => client.DefaultRequestHeaders.UserAgent.ParseAdd(_appName));
            services.AddSingleton<ITimetableServiceClient, TimetableServiceClient>();
            services.Configure<TimetableServiceConfiguration>(configuration.GetSection("ExternalServices:TimeTableService"));
            services.AddHostedService<SSCSupplierConnector>();
            services.AddSingleton<ISSCSupplierConfiguration, AppSettingsSSCSupplierConfiguration>();
            services.AddScoped<ISingleDaySearchDateProvider, SingleDaySearchDateProvider>();

            services.Configure<AppSettingsProviderSettings.Config>(configuration.GetSection("ProviderSettings"));
            services.AddSingleton<IProviderSettings, AppSettingsProviderSettings>();

            services.ConfigurePartnerSettings(configuration);
            services.AddSingleton(configuration);
            services.ConfigureServiceBus(configuration);
            services.AddMemoryCache();
            services.AddHttpClient();
            services.ConfigureRateLimiter(configuration);
            services.AddHttpClient<IGenericApiFlightProvider, GenericApiFlightProvider>(client =>
                {
                    client.DefaultRequestHeaders.Add("user-agent", _appName);
                })
                .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(1),
                    PooledConnectionIdleTimeout = TimeSpan.FromMinutes(1)
                });
            services
                .AddHttpClient<ICacheApiClient, HttpCacheApiClient>((provider, client) =>
                {
                    var url = provider.GetRequiredService<IConfiguration>()["CacheApi:Url"] ?? throw new Exception("CacheApi:Url cannot be null");
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("user-agent", _appName);
                })
                .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(1),
                    PooledConnectionIdleTimeout = TimeSpan.FromMinutes(1)
                });

            services.ConfigureCurrencyProvider(configuration);

            services.AddAirportCurrencies(configuration);

            {
                services.AddScoped<TravelFusionQueueElementConsumer>();
                services.Configure<TravelFusionConsumerSettings>(configuration.GetSection("TravelFusion"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<TravelFusionConsumerSettings>>().Value);
                services.Configure<DirectRyanairConfiguration>(configuration.GetSection("DirectRyanair"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<DirectRyanairConfiguration>>().Value);
                services.Configure<SSCWizzairConfiguration>(configuration.GetSection("SSCWizzair"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<SSCWizzairConfiguration>>().Value);
                services.AddScoped<CacheDemandQueueElementConsumer>();
                services.AddScoped<ConnectionNetworkQueueElementConsumer>();
                services.AddScoped<RefreshCacheRobotQueueElementConsumer>();
                services.AddScoped<IRefreshCacheService, RefreshCacheService>();

                services.AddScoped<ISearchFlightsQueryBuilder, SearchFlightsQueryBuilder>();
                services.AddScoped<ICacheRequestBuilder, CacheRequestBuilder>();
                services.AddScoped<IAvailabilityChecker, AvailabilityChecker>();
                services.Configure<Jet2AvailabilityCheckerSettings>(configuration.GetSection("Jet2AvailabilityChecker"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<Jet2AvailabilityCheckerSettings>>().Value);
                services.AddScoped<IFlightCacheConverter, FlightCacheConverter>();
                services.AddScoped<ITimetableFlightCacheConverter, TimetableFlightCacheConverter>();
                services.AddScoped<IPriceCacheBuilder, PriceCacheBuilder>();
                services.AddScoped<IProviderSearchResultMerger, ProviderSearchResultMerger>();
                services.AddScoped<ISeparationOptionsResolver, SeparationOptionsResolver>();
                services.AddScoped<IRyanairCacheRequestService, RyanairCacheRequestService>();
                services.AddScoped<IRyanairSSCComparisonService, RyanairSscComparisonService>();
                services.AddScoped<ICacheRequestDiffStorage, ComparisonBigQueryStorage>();
                services.AddScoped<IStrategyFactory, StrategyFactory>();
                services.AddScoped<ICacheTimeoutConfiguration, CacheTimeoutConfiguration>();

                services.Configure<RyanairSSCComparisonSettings>(configuration.GetSection("RyanairSSCComparisonSettings"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<RyanairSSCComparisonSettings>>().Value);
                services.Configure<BigQuerySettings>(configuration.GetSection("BigQuerySettings"));
                services.AddSingleton(sp => sp.GetRequiredService<IOptions<BigQuerySettings>>().Value);

                services.AddSingleton<Lazy<BigQueryClient>>(provider =>
                {
                    var settings = provider.GetRequiredService<BigQuerySettings>();
                    return new Lazy<BigQueryClient>(() =>
                    {
                        var credentials = File.ReadAllText(settings.GoogleCredentialsPath);
                        return BigQueryClient.Create(settings.ProjectId, GoogleCredential.FromJson(credentials));
                    });
                });
            }

            //Hotels API
            services.AddScoped<HotelsApiQueueElementConsumer>();
            services.AddScoped<IHotelsApiRequestBuilder, HotelsApiRequestBuilder>();
            services.Configure<HotelsApiConfiguration>(configuration.GetSection("HotelsApi"));
            services.AddSingleton(sp => sp.GetRequiredService<IOptions<HotelsApiConfiguration>>().Value);
            services.AddHttpClient<IHotelsApiClient, HotelsApiClient>((sp, client) =>
            {
                var options = sp.GetRequiredService<IOptions<HotelsApiConfiguration>>().Value;
                client.BaseAddress = new Uri(options.Url);
                client.DefaultRequestHeaders.Add("user-agent", _appName);
            });

            services
                .ConfigureOpenTelemetry(configuration.GetSection("OpenTelemetry").Get<OpenTelemetryOptions>()!)
                .RegisterOpenTracingShim();

            return services;
        }

        private static void ConfigureServiceBus(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddLogging()
                .AddBusWithInstrumentation(
                    configuration.GetSection("RobotServiceBus").Get<BusSettings>(),
                    bus =>
                    {
                        bus.AddConsumers(typeof(Program).Assembly);
                        bus.Configure<MassTransitHostOptions>(opt => opt.WaitUntilStarted = true);
                        bus.ConfigureHealthCheckOptions(opt =>
                        {
                            opt.Tags.Add(HealthChecks.HealthChecks.Tags._readiness);
                            opt.Tags.Add(HealthChecks.HealthChecks.Tags._liveness);
                        });
                    },
                    RegisterEndpoints
                )
                .AddHostedService<RobotsReceiveEndpointConnector>();
        }

        private static void RegisterEndpoints(IBusRegistrationContext context, IRabbitMqBusFactoryConfigurator configurator)
        {
            configurator.ReceiveEndpoint("Robots.SearchFlightsToRefreshCacheCommandRobotConsumerMessages", endpoint =>
            {
                endpoint.PrefetchCount = 1;
                endpoint.UseConcurrencyLimit(1);
                endpoint.ConfigureConsumer<RefreshCacheRobotQueueElementConsumer>(context);
            });
            configurator.Message<SSCQueueElement>(c => c.SetEntityName(SSCQueueElement.RobotPrefix));
            configurator.Send<SSCQueueElement>(c => c.UseRoutingKeyFormatter(x => x.Message.Supplier.ToLowerInvariant()));
            configurator.Publish<SSCQueueElement>(
                c =>
                {
                    c.ExchangeType = RabbitMQ.Client.ExchangeType.Direct;
                    c.BindAlternateExchangeQueue(SSCQueueElement.QueueNameForUnmatched());
                }
            );
        }

        private static void AddAirportCurrencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<AirportCurrencySettings>(configuration.GetSection("AirportCurrencySettings"));
            services.AddSingleton<IAirportCurrencyRepository, AirportCurrencyRepository>();
            services.AddSingleton<IAirportCurrencyService, AirportCurrencyService>();
        }
    }
}