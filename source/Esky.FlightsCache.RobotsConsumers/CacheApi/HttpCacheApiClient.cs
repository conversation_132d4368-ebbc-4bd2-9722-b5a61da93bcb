using Esky.FlightsCache.Contract;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.CacheApi;

public class HttpCacheApiClient : ICacheApiClient
{
    private readonly HttpClient _httpClient;

    public HttpCacheApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<SearchResponse> GetFlights(SearchFlightsRequest request, CancellationToken cancellationToken)
    {
        var response = await _httpClient.PostAsJsonAsync("api/flights", request, cancellationToken);
        var searchResponse = await response.Content.ReadFromJsonAsync<SearchResponse>(cancellationToken: cancellationToken);
        return searchResponse ?? new SearchResponse();
    }
}