using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;

public class RyanairRefreshRouteFeeConsumer : IConsumer<RyanairRefreshRouteFee>
{
    private readonly ILogger<RyanairRefreshRouteFeeConsumer> _logger;
    private readonly ISearchFlightsQueryBuilder _queryBuilder;
    private readonly IGenericApiFlightProvider _providerApi;
    private readonly IRyanairFeeStorage _ryanairFeeStorage;

    public RyanairRefreshRouteFeeConsumer(
        ILogger<RyanairRefreshRouteFeeConsumer> logger,
        ISearchFlightsQueryBuilder queryBuilder,
        IGenericApiFlightProvider providerApi,
        IRyanairFeeStorage ryanairFeeStorage)
    {
        _logger = logger;
        _queryBuilder = queryBuilder;
        _providerApi = providerApi;
        _ryanairFeeStorage = ryanairFeeStorage;
    }

    public async Task Consume(ConsumeContext<RyanairRefreshRouteFee> context)
    {
        var searchResult = await GetPrices(
            context.Message,
            new PaxConfiguration(adult: 1, child: 1, infant: 1),
            context.CancellationToken
        );

        var route = new Route(context.Message.Departure, context.Message.Arrival);

        if (searchResult.Outbound.Prices.Any())
        {
            var fees = CalculateFees(searchResult.Outbound.Prices);
            await _ryanairFeeStorage.SetFees(searchResult.Outbound.Currency, route, fees);
        }

        if (searchResult.Inbound.Prices.Any())
        {
            var fees = CalculateFees(searchResult.Inbound.Prices);
            await _ryanairFeeStorage.SetFees(searchResult.Inbound.Currency, route.Reverse, fees);
        }
    }

    private async Task<FlightPrice> GetPrices(
        RyanairRefreshRouteFee route,
        PaxConfiguration paxConfiguration,
        CancellationToken cancellationToken)
    {
        var departureDate = route.DepartureDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Unspecified);
        var queueElement = new QueueElement
        {
            DepartureCode = route.Departure,
            ArrivalCode = route.Arrival,
            DepartureDay = departureDate,
            ReturnDepartureDay = departureDate,
            IsRoundTrip = true,
            ProviderCode = ProviderCodeEnum.DirectRyanair,
            PartnerCode = "ADMIN",
            PaxConfiguration = paxConfiguration,
            SourceName = "RyanairRefreshRouteFeeConsumer"
        };
        var query = _queryBuilder.BuildSingle(queueElement);
        var response = await _providerApi.SearchAsync(
            new ProviderQuery
            {
                ProviderCode = (int)queueElement.ProviderCode,
                Query = query,
                SourceName = queueElement.SourceName,
                FeatureName = "RyanairRefreshRouteFee"
            },
            cancellationToken
        );

        if (response.IsError)
        {
            _logger.LogError(
                "Could not retrieve flights for: [{DepartureDate}]{Departure}-{Arrival} ({PaxConfiguration}) with message: {ErrorMessage}",
                queueElement.DepartureDay.ToString("O"),
                route.Departure,
                route.Arrival,
                queueElement.PaxConfiguration,
                response.ErrorMessage
            );
            return FlightPrice.Empty;
        }

        var outbound = response.Data
            .Where(e => e.TripType == TripType.OWO)
            .MinBy(e => e.TotalAmount);
        var inbound = response.Data
            .Where(e => e.TripType == TripType.OWI)
            .MinBy(e => e.TotalAmount);

        return new FlightPrice
        {
            Outbound = outbound is null
                ? Flight.Empty
                : new Flight { Currency = outbound.Currency, Prices = outbound.Prices },
            Inbound = inbound is null
                ? Flight.Empty
                : new Flight { Currency = inbound.Currency, Prices = inbound.Prices }
        };
    }

    private static Fee CalculateFees(IReadOnlyCollection<PriceDto> adultChildInfantPrices)
    {
        var adultBasePrice = adultChildInfantPrices.GetPrice(PassengerTypeEnum.ADT);
        var childBasePrice = adultChildInfantPrices.GetPrice(PassengerTypeEnum.CHD);
        var childDiscount = adultBasePrice - childBasePrice;

        var mandatorySeatFee = adultChildInfantPrices.GetPrice(PassengerTypeEnum.ADT, PriceType.MandatoryService);

        var childAdditionalFee = mandatorySeatFee - childDiscount;
        var infantFee = adultChildInfantPrices.GetPrice(PassengerTypeEnum.INF);

        var fee = new Fee
        {
            ChildAdditionalFee = childAdditionalFee,
            InfantFee = infantFee,
            MandatorySeatFee = mandatorySeatFee,
            ChildDiscount = childDiscount,
        };
        return fee;
    }

    private class FlightPrice
    {
        public static readonly FlightPrice Empty = new() { Outbound = Flight.Empty, Inbound = Flight.Empty };
        public required Flight Outbound { get; init; }
        public required Flight Inbound { get; init; }
    }

    private class Flight
    {
        public static readonly Flight Empty = new() { Currency = string.Empty, Prices = [] };
        public required string Currency { get; init; }
        public required IReadOnlyCollection<PriceDto> Prices { get; init; }
    }
}

static file class Extensions
{
    public static decimal? GetPrice(this IEnumerable<PriceDto> prices, PassengerTypeEnum passenger, PriceType priceType = PriceType.Base)
    {
        return prices
            .Where(e => e.PassengerType == passenger && e.PriceType == priceType)
            .Sum(e => e.Amount);
    }
}