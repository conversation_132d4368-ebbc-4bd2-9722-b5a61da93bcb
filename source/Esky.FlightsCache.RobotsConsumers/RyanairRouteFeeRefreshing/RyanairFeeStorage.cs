using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;

public interface IRyanairFeeStorage
{
    Task SetFees(string currency, Route route, Fee fee);
}

public class MongoRyanairFeeStorage : IRyanairFeeStorage
{
    private readonly IMongoCollection<RouteFee> _feeCollection;

    public MongoRyanairFeeStorage(string connectionString, string collectionName)
    {
        var url = MongoUrl.Create(connectionString);
        _feeCollection = new MongoClient(url)
            .GetDatabase(url.DatabaseName)
            .GetCollection<RouteFee>(collectionName);
    }

    public async Task SetFees(string currency, Route route, Fee fee)
    {
        var key = new Key(route.Departure, route.Arrival, currency);
        await _feeCollection.UpdateOneAsync(
            e => e.Route.Equals(key),
            Builders<RouteFee>.Update.Set(e => e.Fee, fee),
            new UpdateOptions { IsUpsert = true }
        );
    }

    private record RouteFee
    {
        [BsonId] public required Key Route { get; init; }
        [BsonIgnoreIfDefault] public required Fee Fee { get; init; }
    }

    private record Key(string Departure, string Arrival, string Currency);
}

public record Route(string Departure, string Arrival)
{
    public Route Reverse => new(Arrival, Departure);
}

public record Fee
{
    [BsonRepresentation(BsonType.Decimal128), BsonIgnoreIfDefault]
    public decimal? ChildAdditionalFee { get; init; }

    [BsonRepresentation(BsonType.Decimal128), BsonIgnoreIfDefault]
    public decimal? InfantFee { get; init; }

    [BsonRepresentation(BsonType.Decimal128), BsonIgnoreIfDefault]
    public decimal? MandatorySeatFee { get; init; }

    [BsonRepresentation(BsonType.Decimal128), BsonIgnoreIfDefault]
    public decimal? ChildDiscount { get; init; }
}