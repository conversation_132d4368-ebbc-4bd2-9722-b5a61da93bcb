using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;

public static class RegistrationExtensions
{
    public static IServiceCollection AddRyanairRouteFeeRefreshing(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration["DatabaseSettings:ConnectionString"] ?? "";
        services.AddSingleton<IRyanairFeeStorage>(_ => new MongoRyanairFeeStorage(connectionString, "ryanairRouteFees"));
        return services;
    }
}