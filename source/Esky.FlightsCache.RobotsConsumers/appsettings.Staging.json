{"ExternalServices": {"TimeTableService": {"Url": "http://esky-timetables-api.query.consul./"}, "CurrencyService": {"Url": "http://esky-currencyservice-api-green.query.consul./"}}, "ProviderSearch": {"GenericApiFlightProviderUrl": "http://esky-flightsintegration-api.service.consul./api/flights/"}, "PartnerSettings": {"Environment": "staging"}, "BigQuerySettings": {"ProjectId": "esky-ets-logs-ci", "GoogleCredentialsPath": "/creds/account.json"}, "RobotServiceBus": {"Url": "rabbitmq://staging.rabbitmq-logs.service.local-staging.consul./ets", "Login": "ets", "Password": "5c71e08b3d5a3f4d9eda01a5e9ecc614"}, "FlightCacheServiceBus": {"Url": "rabbitmq://staging.rabbitmq-logs.service.local-staging.consul./ets"}, "NLog": {"rules": [{"logger": "Microsoft.AspNetCore.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "System.*", "finalMinLevel": "<PERSON><PERSON>"}, {"logger": "<PERSON><PERSON>*", "minLevel": "<PERSON><PERSON>", "writeTo": "rabbit", "final": true}, {"logger": "<PERSON>", "finalMinLevel": "Error"}, {"logger": "*", "minLevel": "Info", "writeTo": "rabbit", "final": true}]}}