using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Esky.FlightsCache.RobotsConsumers.HealthChecks
{
    public static class HealthChecks
    {
        internal static class Tags
        {
            public const string _readiness = "readiness";
            public const string _liveness = "liveness";
            public const string _externalService = "external";
        }

        public static IServiceCollection AddCustomHealthCheck(this IServiceCollection services)
        {
            var hcBuilder = services.AddHealthChecks();

            hcBuilder.AddCheck("self", () => HealthCheckResult.Healthy(), new[] { Tags._liveness });

            return services;
        }

        public static IApplicationBuilder UseCustomHealthChecks(this IApplicationBuilder app)
        {
            return app
                .UseHealthChecks("/health", new HealthCheckOptions
                {
                    Predicate = _ => true,
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                })
                .UseHealthChecks("/health/ready", new HealthCheckOptions
                {
                    Predicate = _ => true
                })
                .UseHealthChecks("/health/live", new HealthCheckOptions
                {
                    Predicate = r => r.Tags.Contains(Tags._liveness)
                })
                .UseHealthChecks("/health/external", new HealthCheckOptions
                {
                    Predicate = r => r.Tags.Contains(Tags._externalService),
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                });
        }
    }
}