<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <ServerGarbageCollection>false</ServerGarbageCollection>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Messages\**" />
    <Content Remove="Messages\**" />
    <EmbeddedResource Remove="Messages\**" />
    <None Remove="Messages\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="appsettings.Development.json" />
    <Content Remove="appsettings.Staging.json" />
    <Content Remove="appsettings.Production.json" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0" />
    <PackageReference Include="Esky.FlightsCache.Contract" Version="0.47.0" />
    <PackageReference Include="Esky.FlightsCache.OpenTelemetry" Version="1.7.0-rc.1" />
    <PackageReference Include="Esky.FlightsCache.ServiceBus" Version="2.0.1" />
    <PackageReference Include="Esky.NLog.RabbitMQ.Target" Version="1.1.3">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Esky.Providers.Content.Contract" Version="0.10.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.0">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.5">
      <TreatAsUsed>true</TreatAsUsed>
    </PackageReference>
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Polly.Extensions" Version="8.3.1" />
    <PackageReference Include="Polly.RateLimiting" Version="8.3.1" />
    <PackageReference Include="System.Threading.RateLimiting" Version="8.0.0" />
  </ItemGroup>

  

  <ItemGroup>
        <None Include="appsettings.Production.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Include="appsettings.Staging.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Include="appsettings.Development.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
          <None Include="appsettings.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
    <ProjectReference Include="..\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots.Messages\Esky.FlightsCache.Robots.Messages.csproj" />
    <ProjectReference Include="..\Esky.FlightsCache.Robots\Esky.FlightsCache.Robots.csproj" />
  </ItemGroup>

</Project>
