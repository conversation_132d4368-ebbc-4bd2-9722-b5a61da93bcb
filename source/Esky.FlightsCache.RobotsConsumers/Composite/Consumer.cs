using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Composite;

public class Consumer : IConsumer<CompositeQueueElement>
{
    private const string _featureName = "CompositeConsumer";
    
    private readonly ISearchService<CompositeQueueElement> _searchService;
    private readonly ISearchService<SSCQueueElement> _sscSearchService;
    private readonly ICacheRequestBuilder _cacheRequestBuilder;
    private readonly IAirportCurrencyService _airportCurrencyService;
    private readonly IFlightCacheConverter _flightCacheConverter;
    private readonly ICacheTimeoutConfiguration _cacheTimeoutConfiguration;
    private readonly IStalenessEvaluator _stalenessEvaluator;
    private readonly ILogger<Consumer> _logger;

    private readonly RequestBuildSettings _requestBuildSettings = new();

    public Consumer(
        ISearchService<CompositeQueueElement> searchService,
        ISearchService<SSCQueueElement> sscSearchService,
        ICacheRequestBuilder cacheRequestBuilder,
        IAirportCurrencyService airportCurrencyService,
        IFlightCacheConverter flightCacheConverter,
        ICacheTimeoutConfiguration cacheTimeoutConfiguration,
        IStalenessEvaluator stalenessEvaluator,
        ILogger<Consumer> logger
        )
    {
        _searchService = searchService;
        _sscSearchService = sscSearchService;
        _cacheRequestBuilder = cacheRequestBuilder;
        _airportCurrencyService = airportCurrencyService;
        _flightCacheConverter = flightCacheConverter;
        _cacheTimeoutConfiguration = cacheTimeoutConfiguration;
        _stalenessEvaluator = stalenessEvaluator;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<CompositeQueueElement> context)
    {
        var token = context.CancellationToken;

        var cacheRequests = await GetCacheRequests(context.Message, token);

        await context.PublishBatch(cacheRequests, token);
    }

    private async Task<IEnumerable<CacheRequest>> GetCacheRequests(CompositeQueueElement message, CancellationToken token)
    {
        var (cacheRequests, isSuccess) = await ProcessQueueElement(message, token);

        if (!message.NextQueueElements.Any() || isSuccess != message.ProceedNextOnSuccess)
        {
            return cacheRequests;
        }

        var nextLayerResults = await Task.WhenAll(message.NextQueueElements
            .Select(x => GetCacheRequests(x, token)));

        return nextLayerResults.SelectMany(x => x);
    }

    private sealed record SingleMessageResult(IEnumerable<CacheRequest> CacheRequests, bool IsSuccess);
        
    private async Task<SingleMessageResult> ProcessQueueElement(CompositeQueueElement compositeQueueElement, CancellationToken cancellationToken)
    {
        try
        {
            var cacheRequestsMatrix = new List<CacheRequest[]>();
            var hasErrors = false;
            foreach (var paxConfiguration in compositeQueueElement.PaxConfigurations)
            {
                var (queueElement, query, response) = await Search(paxConfiguration);
                if (response == null || (!hasErrors && response.IsError))
                {
                    hasErrors = true;
                    continue;
                }

                if (_stalenessEvaluator.IsStale(compositeQueueElement.ProviderCode!.Value, response))
                {
                    return new SingleMessageResult([], false);
                }
                UpdateAirportCurrency(queueElement, response.Currency);
                cacheRequestsMatrix.Add(CreateCacheRequests(response, query, queueElement).ToArray());
            }

            if (hasErrors && cacheRequestsMatrix.Count == 0)
            {
                return new SingleMessageResult([], false);
            }
        
            var cacheRequests = cacheRequestsMatrix
                .MergeCacheRequests()
                .OverrideProvider(compositeQueueElement.OverrideSettings);
            return new SingleMessageResult(cacheRequests, true);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occured while processing {MessageType}", nameof(CompositeQueueElement));
            return new SingleMessageResult([], false);
        }

        void UpdateAirportCurrency(QueueElement queueElement, string? currency)
        {
            if (string.IsNullOrWhiteSpace(queueElement.Currency) && !string.IsNullOrWhiteSpace(currency))
                _ = _airportCurrencyService.Update(queueElement.DepartureCode, queueElement.ProviderCode.ToString() ?? "",
                    currency, cancellationToken);
        }

        async Task<(QueueElement queueElement, SearchFlightsProviderQuery query, SearchResponse? response)> Search(string paxConfiguration)
        {
            if (compositeQueueElement.ProviderCode == ProviderCodeEnum.SSCProvider)
            {
                var queueElement = new SSCQueueElement { InnerElement = compositeQueueElement, PaxConfiguration = paxConfiguration };
                var (query, response) = await _sscSearchService.Search(queueElement, _featureName, cancellationToken);
                return (queueElement, query, response);
            }
            else
            {
                var queueElement = new CompositeQueueElement { InnerElement = compositeQueueElement, PaxConfiguration = paxConfiguration };
                var (query, response) = await _searchService.Search(queueElement, _featureName, cancellationToken);
                return (queueElement, query, response);
            }
        }
    }

    private IEnumerable<CacheRequest> CreateCacheRequests(SearchResponse searchResponse, SearchFlightsProviderQuery query, QueueElement message)
    {
        var timeoutConfig = _cacheTimeoutConfiguration.GetConfiguration.Result;
        var flightsList = _flightCacheConverter.Convert(searchResponse.Data, message, timeoutConfig, query);
        yield return _cacheRequestBuilder.Build(flightsList, query, message, _requestBuildSettings);
    }
}