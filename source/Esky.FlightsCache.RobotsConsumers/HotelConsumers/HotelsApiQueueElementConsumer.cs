using Esky.FlightsCache.Robots.Messages;
using MassTransit;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.HotelConsumers;

public class HotelsApiQueueElementConsumer : IConsumer<HotelsApiQueueElement>
{
    private readonly IHotelsApiRequestBuilder _hotelsApiRequestBuilder;
    private readonly IHotelsApiClient _hotelsApiClient;

    public HotelsApiQueueElementConsumer(IHotelsApiRequestBuilder hotelsApiRequestBuilder, IHotelsApiClient hotelsApiClient)
    {
        _hotelsApiRequestBuilder = hotelsApiRequestBuilder;
        _hotelsApiClient = hotelsApiClient;
    }

    public async Task Consume(ConsumeContext<HotelsApiQueueElement> context)
    {
        var providerCode = context.Message.ProviderCode ?? throw new ArgumentException("Missing provider code");
        var request = _hotelsApiRequestBuilder.CreateRequest(context.Message);
        await _hotelsApiClient.Search(providerCode, request, context.CancellationToken);
    }
}
