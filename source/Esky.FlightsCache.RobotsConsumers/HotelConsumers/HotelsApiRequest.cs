using System;

namespace Esky.FlightsCache.RobotsConsumers.HotelConsumers;

public class HotelsApiRequest
{
    public required int[] HotelMetaCodes { get; set; }
    public required string PartnerCode { get; set; }
    public required StayInformation StayInformation { get; set; }
    public required RoomConfiguration[] Occupancy { get; set; }
    public string? ProviderConfigurationId { get; set; }
}

public class StayInformation
{
    public DateOnly CheckInDate { get; set; }
    public DateOnly CheckOutDate { get; set; }
}

public class RoomConfiguration
{
    public int Adults { get; set; }
    public int[] ChildrenAges { get; set; } = [];
}
