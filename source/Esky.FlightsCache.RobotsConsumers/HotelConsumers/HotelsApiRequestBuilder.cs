using Esky.FlightsCache.Robots.Messages;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.HotelConsumers;

public interface IHotelsApiRequestBuilder
{
    HotelsApiRequest CreateRequest(HotelsApiQueueElement element);
}

public class HotelsApiRequestBuilder : IHotelsApiRequestBuilder
{
    public HotelsApiRequest CreateRequest(HotelsApiQueueElement element)
    {
        return new HotelsApiRequest
        {
            HotelMetaCodes = element.HotelMetaCodes.ToArray(),
            PartnerCode = element.PartnerCode,
            ProviderConfigurationId = element.ProviderConfigurationId,
            StayInformation = new StayInformation
            {
                CheckInDate = element.CheckInDate, CheckOutDate = element.CheckOutDate,
            },
            Occupancy = [
                new RoomConfiguration
                {
                    Adults = element.Adults, 
                    ChildrenAges = element.ChildrenAges.ToArray()
                }
            ]
        };
    }
}
