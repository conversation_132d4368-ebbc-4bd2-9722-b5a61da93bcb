using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.HotelConsumers;

public interface IHotelsApiClient
{
    Task Search(ProviderCodeEnum provider, HotelsApiRequest request, CancellationToken cancellationToken);
}

public class HotelsApiClient : IHotelsApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<HotelsApiClient> _logger;

    public HotelsApiClient(HttpClient httpClient, ILogger<HotelsApiClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task Search(ProviderCodeEnum provider, HotelsApiRequest request, CancellationToken cancellationToken)
    {
        //hotels API performs search in provider system and saves the results in hotel cache
        //the robot only has to send the request
        var response = await _httpClient.PostAsJsonAsync($"/hapi/Provider/{provider}/Search?includeResponseDetails=false", request, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var requestContent = JsonSerializer.Serialize(request, new JsonSerializerOptions(JsonSerializerDefaults.Web));
            _logger.LogWarning("Hotel provider {Provider} error code: {Code}, message: '{Error}', request: `{Request}`", provider, response.StatusCode, errorContent, requestContent);
        }

        Metrics.Hotels.ReportSearch((int)provider, response.IsSuccessStatusCode);
    }
}
