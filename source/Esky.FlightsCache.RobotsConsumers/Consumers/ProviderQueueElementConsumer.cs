using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public abstract class ProviderQueueElementConsumer<T> : IConsumer<T> where T : QueueElement
    {
        private readonly ICacheRequestBuilder _cacheRequestBuilder;
        private readonly IAirportCurrencyService _airportCurrencyService;
        private readonly IFlightCacheConverter _flightCacheConverter;
        private readonly ILogger<ProviderQueueElementConsumer<T>> _logger;
        private readonly ISearchService<T> _searchService;
        private readonly RequestBuildSettings _requestBuildSettings;
        
        protected readonly ICacheTimeoutConfiguration CacheTimeoutConfiguration;

        protected ProviderQueueElementConsumer(
            ICacheRequestBuilder cacheRequestBuilder,
            IAirportCurrencyService airportCurrencyService,
            IFlightCacheConverter flightCacheConverter,
            ILogger<ProviderQueueElementConsumer<T>> logger,
            ISearchService<T> searchService,
            ICacheTimeoutConfiguration cacheTimeoutConfiguration)
        {
            _cacheRequestBuilder = cacheRequestBuilder;
            _airportCurrencyService = airportCurrencyService;
            _flightCacheConverter = flightCacheConverter;
            _logger = logger;
            _searchService = searchService;
            CacheTimeoutConfiguration = cacheTimeoutConfiguration;
            _requestBuildSettings = new RequestBuildSettings();
        }

        public virtual async Task Consume(ConsumeContext<T> context)
        {
            using var source = new CancellationTokenSource();
            var token = source.Token;

            try
            {
                var (query, searchResponse) = await _searchService.Search(context.Message, FeatureName, token);

                var queueElement = context.Message;
                if (searchResponse?.Data is not null && RetrySearch(searchResponse))
                {
                    queueElement = CreateQueueElementForRetry(context.Message);
                    (query, searchResponse) = await _searchService.Search(queueElement, FeatureName, token);
                }

                if (searchResponse?.Data is null || !searchResponse.Data.Any())
                {
                    return;
                }

                _ = _airportCurrencyService.Update(queueElement.DepartureCode,
                    context.Message.ProviderCode.ToString() ?? "", searchResponse.Currency, token);

                var cacheRequests = CreateCacheRequests(searchResponse, query, queueElement);
                await Task.WhenAll(cacheRequests.Select(cacheRequest => context.Publish(cacheRequest, token)));
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Error while consuming robot element {Name}", typeof(T).Name);
            }
        }

        protected virtual IEnumerable<CacheRequest> CreateCacheRequests(SearchResponse searchResponse, SearchFlightsProviderQuery query, T message)
        {
            var timeoutConfig = CacheTimeoutConfiguration.GetConfiguration.Result;
            var flightsList = _flightCacheConverter.Convert(searchResponse.Data, message, timeoutConfig, query);
            yield return _cacheRequestBuilder.Build(flightsList, query, message, _requestBuildSettings);
        }

        protected virtual bool RetrySearch(SearchResponse response) => false;
        protected virtual T CreateQueueElementForRetry(T element) => element;

        public virtual string QueueNameSuffix { get { return ""; } }
        public virtual ProviderCodeEnum ProviderCodeEnum => ProviderCodeEnum.None;
        private protected abstract string FeatureName { get; }
    }
}