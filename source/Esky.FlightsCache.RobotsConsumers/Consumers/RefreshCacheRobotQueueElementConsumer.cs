using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.DirectRyanair;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using Esky.IBE.Robots.Queue.Messages;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class RefreshCacheRobotQueueElementConsumer : IConsumer<RefreshCacheRobotQueueElement>
    {
        private readonly IRyanairCacheRequestService _ryanairCacheRequestService;
        private readonly ILogger<RefreshCacheRobotQueueElementConsumer> _logger;
        private readonly IRefreshCacheService _refreshCacheService;

        public RefreshCacheRobotQueueElementConsumer(
            ILogger<RefreshCacheRobotQueueElementConsumer> logger,
            IRyanairCacheRequestService ryanairCacheRequestService,
            IRefreshCacheService refreshCacheService)
        {
            _logger = logger;
            _ryanairCacheRequestService = ryanairCacheRequestService;
            _refreshCacheService = refreshCacheService;
        }

        public async Task Consume(ConsumeContext<RefreshCacheRobotQueueElement> context)
        {
            var payload = context.Message;

            if (payload.ProviderCode == ProviderCodeEnum.DirectRyanair)
            {
                var searchTasks = CreateRyanAirDirectQueueElement(payload)
                    .Select(r => _ryanairCacheRequestService.SearchFlights(r, context.CancellationToken));
                var requests = (await Task.WhenAll(searchTasks)).SelectMany(x => x);
                await context.PublishBatch(requests, context.CancellationToken);
                return;
            }

            if (payload.SourceType == RefreshCacheRobotQueueElement.RefreshCacheSourceType.FlightNotAvailable)
            {
                var sourceIds = payload.CacheSourceIds?.Select(s => s).ToList() ?? [];

                _logger.LogWarning("Flight not available message received. Source ids: {sourceIds}", string.Join(", ", sourceIds));

                if (sourceIds.Count != 0)
                {
                    await context.Publish(new RemoveFromCacheBySourceIdQueueElement
                    {
                        CacheSourceIds = sourceIds,
                        SessionId = payload.SessionId,
                        RequestId = payload.RequestId
                    });
                }
            }

            if (payload.DepartureDay < DateTime.Now)
            {
                return;
            }

            //Search for selected dates
            payload.PartnerCode = "ADMIN";
            payload.SourceName = "RefreshCacheRobotQueueElement";
            payload.PaxConfiguration = "2.0.0.0";
            var cacheRequests = await _refreshCacheService.SearchFlights(payload, context.CancellationToken);
            await context.PublishBatch(cacheRequests, context.CancellationToken);
        }

        private static IEnumerable<RyanAirDirectQueueElement> CreateRyanAirDirectQueueElement(RefreshCacheRobotQueueElement queueElement)
        {
            yield return new RyanAirDirectQueueElement
            {
                DepartureCode = queueElement.DepartureCode,
                ArrivalCode = queueElement.ArrivalCode,
                DepartureDay = DateTime.SpecifyKind(queueElement.DepartureDay, DateTimeKind.Unspecified),
                PartnerCode = "ADMIN",
                DeleteDepartureDayFrom = queueElement.DepartureDay,
                DeleteDepartureDayTo = queueElement.DepartureDay,
                ProviderCode = ProviderCodeEnum.DirectRyanair,
                SourceName = $"DirectRyanairRefresh_{queueElement.SourceType}"
            };

            if (queueElement.ReturnDepartureDay is null) yield break;
            yield return new RyanAirDirectQueueElement
            {
                DepartureCode = queueElement.ArrivalCode,
                ArrivalCode = queueElement.DepartureCode,
                DepartureDay = DateTime.SpecifyKind(queueElement.ReturnDepartureDay.Value, DateTimeKind.Unspecified),
                PartnerCode = "ADMIN",
                DeleteDepartureDayFrom = queueElement.ReturnDepartureDay.Value,
                DeleteDepartureDayTo = queueElement.ReturnDepartureDay.Value,
                ProviderCode = ProviderCodeEnum.DirectRyanair,
                SourceName = $"DirectRyanairRefresh_{queueElement.SourceType}"
            };
        }
    }

    public interface IRefreshCacheService
    {
        Task<IEnumerable<CacheRequest>> SearchFlights(RefreshCacheRobotQueueElement queueElement, CancellationToken ct);
    }

    public class RefreshCacheService : IRefreshCacheService
    {
        private readonly ICacheRequestBuilder _cacheRequestBuilder;
        private readonly IAirportCurrencyService _airportCurrencyService;
        private readonly IFlightCacheConverter _flightCacheConverter;
        private readonly ICacheTimeoutConfiguration _cacheTimeoutConfiguration;
        private readonly ISearchService<RefreshCacheRobotQueueElement> _searchService;

        public RefreshCacheService(
            ICacheRequestBuilder cacheRequestBuilder,
            IAirportCurrencyService airportCurrencyService,
            IFlightCacheConverter flightCacheConverter,
            ICacheTimeoutConfiguration cacheTimeoutConfiguration,
            ISearchService<RefreshCacheRobotQueueElement> searchService)
        {
            _cacheRequestBuilder = cacheRequestBuilder;
            _airportCurrencyService = airportCurrencyService;
            _flightCacheConverter = flightCacheConverter;
            _cacheTimeoutConfiguration = cacheTimeoutConfiguration;
            _searchService = searchService;
        }

        public async Task<IEnumerable<CacheRequest>> SearchFlights(RefreshCacheRobotQueueElement queueElement, CancellationToken ct)
        {
            var (query, searchResponse) = await _searchService.Search(queueElement, "RefreshFlight", ct);
            if (searchResponse is null)
            {
                return [];
            }

            var timeoutConfig = await _cacheTimeoutConfiguration.GetConfiguration;
            var flights = _flightCacheConverter.Convert(searchResponse.Data, queueElement, timeoutConfig, query);

            if (flights.Count != 0)
            {
                _ = _airportCurrencyService.Update(queueElement.DepartureCode, queueElement.ProviderCode.ToString() ?? "", searchResponse.Currency, ct);
            }

            var cacheRequest = _cacheRequestBuilder.Build(flights, query, queueElement, new RequestBuildSettings());
            return [cacheRequest];
        }
    }
}
