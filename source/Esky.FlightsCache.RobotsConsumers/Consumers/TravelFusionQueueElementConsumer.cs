using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class TravelFusionQueueElementConsumer : ProviderQueueElementConsumer<TravelFusionQueueElement>
    {
        private readonly TravelFusionConsumerSettings _settings;
        private readonly IStalenessEvaluator _stalenessEvaluator;

        public TravelFusionQueueElementConsumer(
            ICacheRequestBuilder cacheRequestBuilder,
            IAirportCurrencyService airportCurrencyService,
            IFlightCacheConverter flightCacheConverter,
            ILogger<TravelFusionQueueElementConsumer> logger,
            TravelFusionConsumerSettings settings,
            ISearchService<TravelFusionQueueElement> searchService,
            ICacheTimeoutConfiguration cacheTimeoutConfiguration,
            IStalenessEvaluator stalenessEvaluator)
            : base(cacheRequestBuilder, airportCurrencyService, flightCacheConverter, logger, searchService, cacheTimeoutConfiguration)
        {
            _settings = settings;
            _stalenessEvaluator = stalenessEvaluator;
        }

        protected override bool RetrySearch(SearchResponse response) =>
            _stalenessEvaluator.IsStale(ProviderCodeEnum.TravelFusion, response);

        protected override TravelFusionQueueElement CreateQueueElementForRetry(TravelFusionQueueElement element)
        {
            var queueElement = new TravelFusionQueueElement
            {
                InnerElement = element,
                PartnerCode = _settings.Retry.PartnerCode,
                SourceName = $"{element.SourceName}_PRO"
            };
            return queueElement;
        }

        private protected override string FeatureName => "TravelFusion";
    }

    public class TravelFusionConsumerSettings
    {
        public required RetrySettings Retry { get; init; }
        public class RetrySettings
        {
            public required Condition[] Conditions { get; init; }
            public required string PartnerCode { get; init; }
            public class Condition
            {
                public required string[] Suppliers { get; init; }
                public int MaxAgeInMinutes { get; init; }
                public int DepartureDaysTo { get; init; }
            }
        }
    }
}