using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Consumers;

public static class FlightDtoExtensions
{
    public static string GetDepartureAirportCode(this FlightDto flight)
    {
        return flight.Legs.First().Segments.First().OriginAirport;
    }

    public static PaxConfiguration ToPaxConfiguration(this IEnumerable<SearchFlightsProviderQuery.Passenger> passengers)
    {
        var countByType = passengers
            .GroupBy(p => p.Code)
            .ToDictionary(g => g.Key, g => g.Sum(p => p.Count));

        var paxConfiguration = new PaxConfiguration(
            countByType.GetValueOrDefault(PassengerTypeEnum.ADT),
            countByType.GetValueOrDefault(PassengerTypeEnum.YTH),
            countByType.GetValueOrDefault(PassengerTypeEnum.CHD),
            countByType.GetValueOrDefault(PassengerTypeEnum.INF)
        );
        return paxConfiguration;
    }

    public static bool Is(this IEnumerable<SearchFlightsProviderQuery.Passenger> passengers, PaxConfiguration paxConfiguration)
    {
        return passengers.ToPaxConfiguration() == paxConfiguration;
    }
}