using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Consumers;

public static class CacheRequestExtensions
{
    public static CacheRequest ApplyGroupName(this CacheRequest request, string groupName)
    {
        request.CommandOptions.GroupName = groupName;
        return request;
    }

    public static CacheRequest ApplyPaxCounts(this CacheRequest cacheRequest, QueueElement queueElement,
        SearchFlightsProviderQuery query)
    {
        var numberOfPaxes = query.Passengers.Where(p => p.Code != PassengerTypeEnum.INF).Sum(p => p.Count);
        cacheRequest.Flights.ForEach(f =>
            f.Legs.ForEach(l => l.AdultPrices?.ForEach(p => p.MinimumNumberOfPaxes = numberOfPaxes)));

        return cacheRequest;
    }
}