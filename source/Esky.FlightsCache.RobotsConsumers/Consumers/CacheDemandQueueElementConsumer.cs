using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class CacheDemandQueueElementConsumer : IConsumer<CacheDemandQueueElement>
    {
        private readonly IPartnerSettingsService _partnerSettings;
        private readonly ISearchFlightsQueryBuilder _searchFlightsQueryBuilder;
        private readonly IGenericApiFlightProvider _provider;
        private readonly IAirportCurrencyService _airportCurrencyService;
        private readonly IProviderSearchResultMerger _providerSearchResultMerger;
        private readonly IFlightCacheConverter _flightCacheConverter;
        private readonly ICacheRequestBuilder _cacheRequestBuilder;
        private readonly ILogger<CacheDemandQueueElementConsumer> _logger;
        private readonly RequestBuildSettings _requestBuildSettings;

        private readonly IDictionary<string, CacheTimeOutConfiguration> _cacheTimeOutConfiguration = new Dictionary<string, CacheTimeOutConfiguration>();

        public CacheDemandQueueElementConsumer(
            IPartnerSettingsService partnerSettings,
            ISearchFlightsQueryBuilder queryBuilder,
            IGenericApiFlightProvider provider,
            IAirportCurrencyService airportCurrencyService,
            IProviderSearchResultMerger providerSearchResultMerger,
            IFlightCacheConverter flightCacheConverter,
            ICacheRequestBuilder cacheRequestBuilder,
            ILogger<CacheDemandQueueElementConsumer> logger)
        {
            _partnerSettings = partnerSettings;
            _searchFlightsQueryBuilder = queryBuilder;
            _provider = provider;
            _airportCurrencyService = airportCurrencyService;
            _providerSearchResultMerger = providerSearchResultMerger;
            _flightCacheConverter = flightCacheConverter;
            _cacheRequestBuilder = cacheRequestBuilder;
            _logger = logger;

            _requestBuildSettings = new RequestBuildSettings();
        }

        public async Task Consume(ConsumeContext<CacheDemandQueueElement> context)
        {
            var source = new CancellationTokenSource();
            var token = source.Token;

            try
            {
                var isSeparable = false;

                foreach (var (query, isRequired) in _searchFlightsQueryBuilder.BuildAll(context.Message))
                {
                    if (isSeparable && !isRequired)
                        return;

                    var searchResponse = await _provider.SearchAsync(
                        new ProviderQuery
                        {
                            ProviderCode = (int)(context.Message.ProviderCode ?? 0),
                            Query = query,
                            SourceName = context.Message.SourceName,
                            FeatureName = "CacheDemand"
                        },
                        token
                    );

                    if ((searchResponse.Data?.Count ?? 0) == 0)
                    {
                        _logger.LogInformation(
                            "CacheDemandConsumer[{ProviderCode}] Found {FlightsCount} flight for {FlightType} {DepartureCode}-{ArrivalCode} @{DepartureDay}-{ReturnDay}",
                            context.Message.ProviderCode,
                            searchResponse.Data?.Count ?? 0,
                            context.Message.IsRoundTrip ? "RT" : "OW",
                            context.Message.DepartureCode,
                            context.Message.ArrivalCode,
                            context.Message.DepartureDay.ToString("yyyy-MM-dd"),
                            query.Legs.Count() == 2 ? query.Legs.Last().DepartureDate.ToString("yyyy-MM-dd") : null
                        );

                        continue;
                    }

                    _ = _airportCurrencyService.Update(context.Message.DepartureCode,
                        context.Message.ProviderCode.ToString() ?? "", searchResponse.Currency, token);

                    var timeout = await GetCacheTimeOutConfiguration(context.Message.PartnerCode);

                    var cacheRequest = CreateCacheRequest(searchResponse, query, context.Message, timeout);
                    await context.Publish(cacheRequest, token);
                    isSeparable = cacheRequest.Flights
                        .TrueForAll(f => f.LegsCanBeUseSeparately 
                            && f.Legs.TrueForAll(l => l.AdultPrices?.Any() == true));
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Error while consuming robot element {Name}", nameof(CacheDemandQueueElement));
            }
        }

        private CacheRequest CreateCacheRequest(SearchResponse searchResponse, SearchFlightsProviderQuery query, QueueElement message, CacheTimeOutConfiguration timeout)
        {
            var flights = _providerSearchResultMerger.Map(query, searchResponse.Data);
            var flightsList = _flightCacheConverter.Convert(flights, message, timeout, query);
            return _cacheRequestBuilder.Build(flightsList, query, message, _requestBuildSettings);
        }

        private async Task<CacheTimeOutConfiguration> GetCacheTimeOutConfiguration(string partnerCode)
        {
            if (_cacheTimeOutConfiguration.TryGetValue(partnerCode, out var config))
            {
                return config;
            }

            var ps = await _partnerSettings.GetPartnerSettingsAsync(partnerCode);
            return _cacheTimeOutConfiguration[partnerCode] = ps.SpecialOccasionsSettings.GetCacheTimeOutConfiguration();
        }
    }
}
