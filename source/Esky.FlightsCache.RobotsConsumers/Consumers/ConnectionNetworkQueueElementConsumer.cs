using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.DirectRyanair;
using Esky.FlightsCache.RobotsConsumers.ProviderMapping;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using System;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class ConnectionNetworkQueueElementConsumer : IConsumer<ConnectionNetworkQueueElement>
    {
        private readonly IRyanairCacheRequestService _ryanairCacheRequestService;
        private readonly IProviderMappingStorage _providerMappingStorage;

        public ConnectionNetworkQueueElementConsumer(
            IRyanairCacheRequestService ryanairCacheRequestService,
            IProviderMappingStorage providerMappingStorage)
        {
            _ryanairCacheRequestService = ryanairCacheRequestService;
            _providerMappingStorage = providerMappingStorage;
        }

        public async Task Consume(ConsumeContext<ConnectionNetworkQueueElement> context)
        {
            if (context.Message is { ProviderCode: (int)ProviderCodeEnum.DirectRyanair, Type: ConnectionNetworkType.Added })
            {
                var requests = await _ryanairCacheRequestService.SearchFlights(CreateRyanAirDirectQueueElement(context.Message), context.CancellationToken);
                await context.PublishBatch(requests, context.CancellationToken);
            }

            if (context.Message.Type == ConnectionNetworkType.Removed)
            {
                await context.Publish(await CreateEmptyCacheRequest(context.Message), context.CancellationToken);
            }
        }

        private static RyanAirDirectQueueElement CreateRyanAirDirectQueueElement(ConnectionNetworkQueueElement queueElement)
        {
            return new RyanAirDirectQueueElement
            {
                DepartureCode = queueElement.Departure,
                ArrivalCode = queueElement.Arrival,
                DepartureDay = DateTime.SpecifyKind(queueElement.Date, DateTimeKind.Unspecified),
                PartnerCode = "ADMIN",
                DeleteDepartureDayFrom = queueElement.Date,
                DeleteDepartureDayTo = queueElement.Date,
                ProviderCode = ProviderCodeEnum.DirectRyanair,
                SourceName = "DirectRyanairConnectionNetwork"
            };
        }

        private async Task<CacheRequest> CreateEmptyCacheRequest(ConnectionNetworkQueueElement queueElement)
        {
            var supplier = await _providerMappingStorage.GetSupplier(queueElement.ProviderCode, queueElement.Airline);
            
            return new CacheRequest
            {
                SourceDescription = new SourceDescription
                {
                    Name = $"{(ProviderCodeEnum)queueElement.ProviderCode}ConnectionNetwork_Empty",
                    SearchDepartureCode = queueElement.Departure,
                    SearchArrivalCode = queueElement.Arrival,
                    SearchDepartureDate = queueElement.Date,
                    SendDate = DateTime.UtcNow,
                    MachineName = Environment.MachineName,
                    Provider = queueElement.ProviderCode.ToString(),
                    Supplier = supplier,
                    PartnerCode = "ADMIN",
                    SessionId = "0",
                    PaxConfiguration = "0.0.0.0"
                },
                CommandOptions = new CommandDataOptions
                {
                    GroupName = Guid.NewGuid().ToString(),
                    SkipDataFiltering = false,
                    DeleteOptions = new DeleteOptions
                    {
                        AirlineCodes = [],
                        ProviderCodes = [queueElement.ProviderCode],
                        DeleteDepartureDayFrom = queueElement.Date,
                        DeleteDepartureDayTo = queueElement.Date,
                        IsEnabled = true
                    },
                    SkipSQLSave = true
                },
                Flights = [],
            };
        }
    }
}
