using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;
using Esky.FlightsCache.RobotsConsumers.DirectRyanair;
using Esky.FlightsCache.RobotsConsumers.HotelConsumers;
using Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Consumers
{
    public class RobotsReceiveEndpointConnector : IHostedService
    {
        private const string _queueNameForRobotsPrefix = "Robots.";
        private const string _robotsPartnerCode = "ADMIN";
        
        private readonly IPartnerSettingsService _partnerSettings;
        private readonly IReceiveEndpointConnector _connector;
        private readonly IConfiguration _configuration;

        public RobotsReceiveEndpointConnector(
            IPartnerSettingsService partnerSettings,
            IReceiveEndpointConnector connector,
            IConfiguration configuration)
        {
            _partnerSettings = partnerSettings;
            _connector = connector;
            _configuration = configuration;
        }
        
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            var partnerSettings = await _partnerSettings.GetPartnerSettingsAsync(_robotsPartnerCode);
            
            var cacheDemandHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}CacheDemandSpecialOfferRobotMessages",
                (context, endpoint) =>
                {
                    endpoint.ConfigureConsumer<CacheDemandQueueElementConsumer>(context);
                    ApplyRobotSettings(
                        endpoint,
                        GetRobotsSettings(partnerSettings, ProviderCodeEnum.TravelFusion)
                    );
                });
            
            var amadeusLiveCheckHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}AmadeusLiveCheckSpecialOfferRobotMessages",
                (context, endpoint) =>
                {
                    endpoint.ConfigureConsumer<AmadeusLiveCheckQueueElementConsumer>(context);
                    ApplyRobotSettings(
                        endpoint,
                        GetRobotsSettings(partnerSettings, ProviderCodeEnum.Amadeus)
                    );
                });
            
            var connectionNetworkHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}ConnectionNetworkMessages",
                (context, endpoint) => endpoint.ConfigureConsumer<ConnectionNetworkQueueElementConsumer>(context)
            );

            var ryanairRefreshRouteFeeHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}RyanairRefreshRouteFee",
                (context, endpoint) =>
                {
                    endpoint.ConfigureConsumer<RyanairRefreshRouteFeeConsumer>(context);
                    endpoint.UseRateLimit(1, TimeSpan.FromSeconds(5));
                }
            );
            var hotelsApiHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}HotelsApiMessages",
                (context, endpoint) =>
                {
                    endpoint.ConfigureConsumer<HotelsApiQueueElementConsumer>(context);
                });

            var jet2Handle = _connector.ConnectReceiveEndpoint($"{_queueNameForRobotsPrefix}Jet2", (context, endpoint) => endpoint.ConfigureConsumer<Jet2.Consumer>(context));
            
            var compositeHandle = _connector.ConnectReceiveEndpoint($"{_queueNameForRobotsPrefix}Composite", (context, endpoint) => endpoint.ConfigureConsumer<Composite.Consumer>(context));

            var directRyanairHandle = _connector.ConnectReceiveEndpoint(
                $"{_queueNameForRobotsPrefix}{ProviderCodeEnum.DirectRyanair:g}SpecialOfferRobotMessages",
                (context, endpoint) =>
                {
                    endpoint.ConfigureConsumer<RyanAirDirectQueueElementConsumer>(context);
                    endpoint.PrefetchCount = _configuration.GetValue<int?>("DirectRyanair:PrefetchCount") ?? 10;
                    endpoint.ConcurrentMessageLimit = _configuration.GetValue<int?>("DirectRyanair:ConcurrentMessageLimit") ?? 10;
                }
            );

            var tasks = new[]
            {
                GetConsumerHandle<TravelFusionQueueElementConsumer>(partnerSettings, ProviderCodeEnum.TravelFusion),
                directRyanairHandle.Ready,
                cacheDemandHandle.Ready,
                amadeusLiveCheckHandle.Ready,
                connectionNetworkHandle.Ready,
                ryanairRefreshRouteFeeHandle.Ready,
                jet2Handle.Ready,
                hotelsApiHandle.Ready,
                compositeHandle.Ready,
            };

            // block and wait until up and running
            await Task.WhenAll(tasks);

            Task GetConsumerHandle<T>(PartnerSettingsModel ps, ProviderCodeEnum providerCode) where T : class, IConsumer
            {
                var robotSettings = GetRobotsSettings(ps,providerCode);

                if (!robotSettings.IsActive)
                {
                    return Task.CompletedTask;
                }

                var handle = _connector.ConnectReceiveEndpoint(
                    $"{_queueNameForRobotsPrefix}{providerCode:g}SpecialOfferRobotMessages",
                    (context, endpoint) =>
                    {
                        endpoint.ConfigureConsumer<T>(context);
                        ApplyRobotSettings(endpoint, robotSettings);
                    }
                );

                return handle.Ready;
            }
            
            static ProviderRobotsSettings GetRobotsSettings(PartnerSettingsModel partnerSettings, ProviderCodeEnum providerCode) => partnerSettings
                .SpecialOccasionsSettings
                .RobotsConfiguration
                .ProviderConfigurations
                .Single(x => x.Value.RobotProviderCode == (int)providerCode).Value;
            
            static void ApplyRobotSettings(IReceiveEndpointConfigurator s, BaseProviderRobotsSettings robotSettings)
            {
                if (robotSettings.RetryLimit > 0)
                {
                    s.UseRetry(r =>
                    {
                        r.Incremental(robotSettings.RetryLimit,
                            TimeSpan.FromSeconds(robotSettings.RetryInitialIntervalSeconds),
                            TimeSpan.FromSeconds(robotSettings.RetryIntervalIncrement)
                        );
                    });
                }

                if (robotSettings.CircuitBreakerActiveThreshold > 0)
                {
                    s.UseCircuitBreaker(c =>
                    {
                        c.ActiveThreshold = robotSettings.CircuitBreakerActiveThreshold;
                        c.ResetInterval = TimeSpan.FromSeconds(robotSettings.CircuitBreakerResetIntervalSeconds);
                        c.TrackingPeriod = TimeSpan.FromSeconds(robotSettings.CircuitBreakerTrackingPeriodSeconds);
                        c.TripThreshold = robotSettings.CircuitBreakerTripThreshold;
                    });
                }

                s.PrefetchCount = robotSettings.NumberOfProcessingThreads;
                s.ConcurrentMessageLimit = robotSettings.NumberOfProcessingThreads;
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            // all receive endpoints are stopped along with the bus
            return Task.CompletedTask;
        }
    }
}