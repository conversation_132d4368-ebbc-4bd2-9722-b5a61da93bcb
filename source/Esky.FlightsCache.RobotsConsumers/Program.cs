using Esky.FlightsCache.OpenTelemetry;
using Esky.FlightsCache.RobotsConsumers.HealthChecks;
using Esky.FlightsCache.RobotsConsumers.IoC;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var services = builder.Services;

services.AddMvc();
services.AddControllers();
services.AddCustomHealthCheck();

services.ConfigureIoC(configuration);

builder.Logging
    .ClearProviders()
    .AddNLog();

builder.Host.UseDefaultServiceProvider(opt =>
{
    opt.ValidateScopes = !builder.Environment.IsProduction() || opt.ValidateScopes;
    opt.ValidateOnBuild = !builder.Environment.IsProduction() || opt.ValidateOnBuild;
});

var app = builder.Build();

app.UseOpenTelemetryDefaults();
app.UseRouting();
app.UseCors("AllDomainAllowed");
app.MapControllers();
app.UseCustomHealthChecks();

app.Run();