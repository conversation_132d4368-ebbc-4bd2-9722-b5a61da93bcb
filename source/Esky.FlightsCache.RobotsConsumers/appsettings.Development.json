{
  "ExternalServices": {
    "TimeTableService": {
      "Url": "http://host.docker.internal:1090/"
    },
    "CurrencyService": {
      "Url": "http://host.docker.internal:1090/"
    }
  },
  "ProviderSearch": {
    "GenericApiFlightProviderUrl": "http://host.docker.internal:1090"
  },
  "PartnerSettings": {
    "Environment": "pro",
    "System": "ets",
    "Url": "http://host.docker.internal:1090/"
  },
  "AirportCurrencySettings": {
    "ConnectionString": "mongodb://host.docker.internal:27002/RobotsProducers?appname=esky-flightscache-robotsproducers"
  },
  "BigQuerySettings": {
    "ProjectId": "esky-ets-logs-ci",
    "GoogleCredentialsPath": "googleKey.json"
  },
  "RobotServiceBus": {
    "Url": "rabbitmq://host.docker.internal",
    "Login": "robotuser",
    "Password": "robotpassword"
  },
  "DatabaseSettings": {
    "ConnectionString": "mongodb://host.docker.internal:27002/FlightsCacheConfiguration"
  },
  "AmadeusLiveCheck": {
    "ApiUrl": "http://esky-content-amadeus-api.service.gcp-staging.consul./api/"
  },
  "SSCProvider":{
    "ApiUrl": "http://esky-content-ssc-api.service.gcp-staging.consul/api/"
  },
  "ServiceBus_Login": "robotuser",
  "ServiceBus_Password": "robotpassword",
  "ServiceBus_Clustermembers": "host.docker.internal",
  "ServiceBus_VHost": "/",
  "NLog": {
    "targets": {
      "async": false,
      "console": { "type": "Console" }
    },
    "rules": [
      { "logger": "*", "minLevel": "Info", "writeTo": "console", "final": true },
      { "logger": "*", "minLevel": "Info", "writeTo": "rabbit" } // to avoid Warn: Unused target detected
    ]
  }
}