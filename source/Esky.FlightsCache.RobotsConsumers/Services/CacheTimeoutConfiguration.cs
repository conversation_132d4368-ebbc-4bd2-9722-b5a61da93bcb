using Esky.FlightsCache.PartnerSettings;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Services;

public interface ICacheTimeoutConfiguration
{
    ValueTask<CacheTimeOutConfiguration> GetConfiguration { get; }
}

public class CacheTimeoutConfiguration : ICacheTimeoutConfiguration
{
    private const string _robotsPartnerCode = "ADMIN";

    private readonly IPartnerSettingsService _partnerSettingsService;

    public CacheTimeoutConfiguration(IPartnerSettingsService partnerSettingsService)
    {
        _partnerSettingsService = partnerSettingsService;
    }

    public ValueTask<CacheTimeOutConfiguration> GetConfiguration => new(GetConfig());

    private async Task<CacheTimeOutConfiguration> GetConfig()
    {
        return (await _partnerSettingsService.GetPartnerSettingsAsync(_robotsPartnerCode))
            .SpecialOccasionsSettings
            .GetCacheTimeOutConfiguration();
    }
}