using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using Polly.Registry;
using Polly.Timeout;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.RateLimiting;

namespace Esky.FlightsCache.RobotsConsumers.Services;

public interface IRateLimiter
{
    ResiliencePipeline<HttpResponseMessage> GetPipeline(string providerCode);
}

public class RateLimiter : IRateLimiter
{
    private readonly ResiliencePipelineProvider<string> _provider;

    public RateLimiter(ResiliencePipelineProvider<string> provider)
    {
        _provider = provider;
    }

    public ResiliencePipeline<HttpResponseMessage> GetPipeline(string providerCode)
    {
        try
        {
            return _provider.GetPipeline<HttpResponseMessage>(providerCode);
        }
        catch (Exception)
        {
            return _provider.GetPipeline<HttpResponseMessage>("0");
        }
    }
}

public class RateConfig
{
    public int Limit { get; set; }
    public int WindowInMilliseconds { get; set; }
    public int QueueLimit { get; set; }
    public bool UseRetry { get; set; }
}

public class RetryConfig
{
    public int MaxRetryAttempts { get; set; }
    public int InitialDelayInMilliseconds { get; set; }
}

public static class RateLimiterRegistration
{
    public static void ConfigureRateLimiter(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IRateLimiter, RateLimiter>();
        var retryConfig = configuration.GetSection("RetrySettings").Get<RetryConfig>();
        var retryOptions = new Polly.Retry.RetryStrategyOptions<HttpResponseMessage>
        {
            MaxRetryAttempts = retryConfig?.MaxRetryAttempts ?? 0,
            BackoffType = DelayBackoffType.Exponential,
            Delay = TimeSpan.FromMilliseconds(retryConfig?.InitialDelayInMilliseconds ?? 2000),
            UseJitter = true,
            ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                        .HandleResult(response => !response.IsSuccessStatusCode)
                        .Handle<HttpRequestException>()
                        .Handle<TimeoutRejectedException>()
        };
        services.AddResiliencePipeline<string, HttpResponseMessage>("0", (builder, _) =>
        {
            builder.AddRetry(retryOptions);
        });

        var configs = configuration.GetSection("RateLimiterSettings").Get<Dictionary<string, RateConfig>>();

        foreach (var (providerCode, config) in configs ?? [])
        {
            services.AddResiliencePipeline<string, HttpResponseMessage>(providerCode, (b, context) =>
            {
                var limiter = new SlidingWindowRateLimiter(
                    new SlidingWindowRateLimiterOptions
                    {
                        PermitLimit = config.Limit,
                        Window = TimeSpan.FromMilliseconds(config.WindowInMilliseconds),
                        SegmentsPerWindow = config.Limit,
                        QueueLimit = config.QueueLimit
                    });
                if (config.UseRetry)
                {
                    b.AddRetry(retryOptions);
                }
                b.AddRateLimiter(limiter);

                context.OnPipelineDisposed(() => limiter.Dispose());
            });
        }
    }
}