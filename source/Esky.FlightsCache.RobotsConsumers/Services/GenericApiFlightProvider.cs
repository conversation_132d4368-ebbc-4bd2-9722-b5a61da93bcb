using Esky.Flights.Integration.Providers.Contract;
using Esky.Flights.Integration.Providers.Contract.Caller;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static MassTransit.ValidationResultExtensions;

namespace Esky.FlightsCache.RobotsConsumers.Services;

public interface IGenericApiFlightProvider
{
    Task<SearchResponse> SearchAsync(ProviderQuery query, CancellationToken ct);
}

public record ProviderQuery
{
    public required int ProviderCode { get; init; }
    public required SearchFlightsProviderQuery Query { get; init; }
    public required string SourceName { get; init; }
    public required string FeatureName { get; init; }
}

public class GenericApiFlightProvider : IGenericApiFlightProvider
{
    private readonly HttpClient _client;
    private readonly IRateLimiter _rateLimiter;
    private readonly string _apiUrl;
    private readonly ILogger<GenericApiFlightProvider> _logger;

    public GenericApiFlightProvider(HttpClient client, IConfiguration configuration, IRateLimiter rateLimiter, ILogger<GenericApiFlightProvider> logger)
    {
        _client = client;
        _rateLimiter = rateLimiter;
        _apiUrl = configuration.GetSection("ProviderSearch")["GenericApiFlightProviderUrl"]!;
        _logger = logger;
    }

    public async Task<SearchResponse> SearchAsync(ProviderQuery query, CancellationToken ct)
    {
        var providerCode = query.ProviderCode;
        var request = query.Query;
        var url = new Uri(new Uri(_apiUrl), providerCode.ToString());
        var departureCode = query.Query.Legs.First().DepartureCode;
        var arrivalCode = query.Query.Legs.First().ArrivalCode;
        var date = query.Query.Legs.First().DepartureDate;

        var pipeline = _rateLimiter.GetPipeline(query.ProviderCode.ToString());

        try
        {
            var jsonRequest = JsonRequest(request);
            var result = await pipeline.ExecuteAsync(async token =>
            {
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = jsonRequest,
                    Headers = { { HeaderNames.RequestSource, CreateRequestSourceHeader(query) } }
                };

                return await SendRequest(httpRequest, providerCode, token);
            }, ct);

            var resultContent = await result.Content.ReadAsStringAsync(ct).ConfigureAwait(false);

            if (!result.IsSuccessStatusCode)
            {
                _logger.LogWarning(
                    "Error getting flights from GenericProviderApi<{ProviderCode}>: {DepartureCode}-{ArrivalCode} {Date}. Status code {StatusCode}. Message: {ResultContent}",
                    providerCode, departureCode, arrivalCode, date, result.StatusCode, resultContent);
                return new SearchResponse { IsError = true };
            }
            return JsonConvert.DeserializeObject<SearchResponse>(resultContent) ?? new SearchResponse { IsError = true };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex,
                "Error getting flights from GenericProviderApi<{ProviderCode}>: {DepartureCode}-{ArrivalCode} {Date}",
                providerCode, departureCode, arrivalCode, date);
            return new SearchResponse { IsError = true };
        }
    }

    private string CreateRequestSourceHeader(ProviderQuery query)
    {
        var source = new RequestSource
        {
            SourceName = "Cache",
            FeatureName = query.FeatureName,
            FeatureDetails = query.SourceName
        };

        return source.Serialize();
    }

    private async Task<HttpResponseMessage> SendRequest(HttpRequestMessage httpRequest, int providerCode, CancellationToken token)
    {
        var result = await _client.SendAsync(httpRequest, token);
        Metrics.Provider.ReportSearch(providerCode, result.IsSuccessStatusCode);
        return result;
    }

    private static HttpContent JsonRequest(SearchFlightsProviderQuery request)
    {
        var serializedRequest = JsonConvert.SerializeObject(request);

        return new StringContent(serializedRequest, Encoding.UTF8, "application/json");
    }
}