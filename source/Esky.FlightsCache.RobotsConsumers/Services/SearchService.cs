using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsProducers.Messages;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Services;

public interface ISearchService<in T> where T : QueueElement
{
    Task<(SearchFlightsProviderQuery, SearchResponse?)> Search(T message, string featureName, CancellationToken token) ;
}

public class SearchService<T> : ISearchService<T> where T : QueueElement
{
    private readonly ISearchFlightsQueryBuilder _searchFlightsQueryBuilder;
    private readonly IGenericApiFlightProvider _apiFlightProvider;
    private readonly ILogger<SearchService<T>> _logger;

    public SearchService(
        ISearchFlightsQueryBuilder searchFlightsQueryBuilder,
        IGenericApiFlightProvider apiFlightProvider,
        ILogger<SearchService<T>> logger)
    {
        _searchFlightsQueryBuilder = searchFlightsQueryBuilder;
        _apiFlightProvider = apiFlightProvider;
        _logger = logger;
    }

    public async Task<(SearchFlightsProviderQuery, SearchResponse?)> Search(T message, string featureName, CancellationToken token)
    {
        var query = _searchFlightsQueryBuilder.BuildSingle(message);
        try
        {
            var searchResponse = await _apiFlightProvider.SearchAsync(
                new ProviderQuery
                {
                    ProviderCode = (int)(message.ProviderCode ?? 0),
                    Query = query,
                    SourceName = message.SourceName,
                    FeatureName = featureName
                },
                token
            );
            
            if (_logger.IsEnabled(LogLevel.Trace))
            {
                _logger.LogTrace(
                    "RobotsConsumer[{ProviderCode}] Found {FlightsCount} flight for {FlightType} {DepartureCode}-{ArrivalCode} @{DepartureDay}-{ReturnDay}",
                    message.ProviderCode,
                    searchResponse.Data?.Count ?? 0,
                    message.IsRoundTrip ? "RT" : "OW",
                    message.DepartureCode,
                    message.ArrivalCode,
                    message.DepartureDay.ToString("yyyy-MM-dd"),
                    message.ReturnDepartureDay?.ToString("yyyy-MM-dd")
                );
            }
            return (query, searchResponse);
        }
        catch (Exception exception)
        {
            _logger.LogWarning(exception, "Provider search failed while consuming robot element {Name}", typeof(T).Name);
            return (query,  null);
        }
    }
}