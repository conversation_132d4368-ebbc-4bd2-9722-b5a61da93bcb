using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.Services;

public interface IStalenessEvaluator
{
    bool IsStale(ProviderCodeEnum providerCode, SearchResponse response);
}

public class StalenessEvaluator : IStalenessEvaluator
{
    private readonly TravelFusionConsumerSettings _travelFusionConsumerSettings;

    public StalenessEvaluator(TravelFusionConsumerSettings travelFusionConsumerSettings)
    {
        _travelFusionConsumerSettings = travelFusionConsumerSettings;
    }

    public bool IsStale(ProviderCodeEnum providerCode, SearchResponse response)
    {
        if (providerCode != ProviderCodeEnum.TravelFusion)
        {
            return false;
        }

        foreach (var condition in _travelFusionConsumerSettings.Retry.Conditions)
        {
            var maxDepartureDate = DateTime.UtcNow.Date.AddDays(condition.DepartureDaysTo);
            var maxRefreshDate = DateTime.UtcNow.AddMinutes(-condition.MaxAgeInMinutes);
            if (response.Data.Exists(f =>
                    condition.Suppliers.Contains(f.Supplier)
                    && f.Legs[0].Segments[0].DepartureLocalTime.Date <= maxDepartureDate
                    && (f.Legs.Min(l => l.DataTimestamp) ?? DateTime.UtcNow) < maxRefreshDate))
            {
                return true;
            }
        }
        return false;
    }
}