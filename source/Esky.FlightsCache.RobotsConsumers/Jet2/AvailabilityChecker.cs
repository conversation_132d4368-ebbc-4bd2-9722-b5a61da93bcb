using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsConsumers.Services;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Jet2;

public interface IAvailabilityChecker
{
    Task<int?> GetTotalFaresLeft(SegmentDto segment, bool isRoundTrip, int maxSeats, CancellationToken cancellationToken);
}

public class AvailabilityChecker(ISearchService<Jet2QueueElement> searchService) : IAvailabilityChecker
{
    public async Task<int?> GetTotalFaresLeft(SegmentDto segment, bool isRoundTrip, int maxSeats, CancellationToken cancellationToken)
    {
        if (await IsFlightAvailable(segment, isRoundTrip, maxSeats, cancellationToken))
        {
            return null;
        }

        var low = 1;
        var high = maxSeats - 1;
        var result = 1;

        while (low <= high)
        {
            var mid = low + (high - low) / 2;

            if (await IsFlightAvailable(segment, isRoundTrip, mid, cancellationToken))
            {
                result = mid;
                low = mid + 1;
            }
            else
            {
                high = mid - 1;
            }
        }

        return result;
    }

    private async Task<bool> IsFlightAvailable(SegmentDto segment, bool isRoundTrip, int adults, CancellationToken cancellationToken)
    {
        var message = Jet2QueueElement.ForAvailabilityCheck(segment.OriginAirport, segment.DestinationAirport, segment.DepartureLocalTime.Date, isRoundTrip, adults);
        var (_, response) = await searchService.Search(message, featureName: message.SourceName, cancellationToken);

        return response is { Data.Count: > 0 } && response.Data.Any(x => x.Legs[0].Segments[0].FlightNumber == segment.FlightNumber);
    }
}