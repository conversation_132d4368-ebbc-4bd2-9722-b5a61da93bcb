using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.Jet2;

public class Consumer : IConsumer<Jet2QueueElement>
{
    private readonly ICacheRequestBuilder _cacheRequestBuilder;
    private readonly IAirportCurrencyService _airportCurrencyService;
    private readonly IFlightCacheConverter _flightCacheConverter;
    private readonly ISearchService<Jet2QueueElement> _searchService;
    private readonly ICacheTimeoutConfiguration _cacheTimeoutConfiguration;
    private readonly IAvailabilityChecker _availabilityChecker;
    private readonly Jet2AvailabilityCheckerSettings _settings;

    public Consumer(
        ICacheRequestBuilder cacheRequestBuilder,
        IAirportCurrencyService airportCurrencyService,
        IFlightCacheConverter flightCacheConverter,
        ISearchService<Jet2QueueElement> searchService,
        ICacheTimeoutConfiguration cacheTimeoutConfiguration,
        IAvailabilityChecker availabilityChecker,
        Jet2AvailabilityCheckerSettings settings)
    {
        _cacheRequestBuilder = cacheRequestBuilder;
        _airportCurrencyService = airportCurrencyService;
        _flightCacheConverter = flightCacheConverter;
        _searchService = searchService;
        _cacheTimeoutConfiguration = cacheTimeoutConfiguration;
        _availabilityChecker = availabilityChecker;
        _settings = settings;
    }

    public async Task Consume(ConsumeContext<Jet2QueueElement> context)
    {
        var message = context.Message;
        var (query, response) = await _searchService.Search(message, featureName: message.SourceName, context.CancellationToken);
        if (response is not { Data.Count: > 0 })
        {
            return;
        }

        var closeFlights = response.Data.Where(x => x.Legs[0].Segments[0].DepartureLocalTime.Date <= DateTime.UtcNow.AddDays(_settings.DaysForward));

        foreach (var flight in closeFlights)
        {
            flight.Legs[0].TotalFaresLeft = await _availabilityChecker.GetTotalFaresLeft(flight.Legs[0].Segments[0], message.IsRoundTrip, _settings.MaxSeats, context.CancellationToken);
        }

        _ = _airportCurrencyService.Update(message.DepartureCode, (message.ProviderCode ?? ProviderCodeEnum.Jet2).ToString(), response.Currency);

        var flights = _flightCacheConverter.Convert(
            response.Data,
            message,
            await _cacheTimeoutConfiguration.GetConfiguration,
            query
        );
        var cacheRequest = _cacheRequestBuilder.Build(flights, query, message, new RequestBuildSettings());
        await context.Publish(cacheRequest);
    }
}

public class Jet2AvailabilityCheckerSettings
{
    public int MaxSeats { get; init; }
    public int DaysForward { get; init; }
}