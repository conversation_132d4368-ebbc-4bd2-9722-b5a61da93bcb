using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ISSCProvider
{
    Task<SearchResponse?> Search(SSCQueueElement query, CancellationToken cancellationToken);
}

public class SSCProvider : ISSCProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SSCProvider> _logger;

    public SSCProvider(HttpClient httpClient, ILogger<SSCProvider> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<SearchResponse?> Search(SSCQueueElement query, CancellationToken cancellationToken)
    {
        _httpClient.DefaultRequestHeaders.Add("source", query.SourceName);

        PaxConfiguration pax = query.PaxConfiguration;

        var queryString = QueryString.Create(
            new KeyValuePair<string, string?>[]
            {
                new("config", GetSettingsName(query.Supplier)),
                new("origin", query.DepartureCode),
                new("destination", query.ArrivalCode),
                new("dd", query.DepartureDay.ToString("yyyy-MM-dd")),
                new("rd", query.ReturnDepartureDay?.ToString("yyyy-MM-dd")),
                new("flex", query.Flex.ToString()),
                new("adt", pax.Adult.ToString()),
                new("yth", pax.Youth.ToString()),
                new("chd", pax.Child.ToString()),
                new("inf", pax.Infant.ToString())
            }
        );

        var response = await SearchFlights(query.Supplier, queryString, cancellationToken);

        if (response.StatusCode == HttpStatusCode.Conflict) // no settings
        {
            await ConfigureSettings(query.Supplier, cancellationToken);
            response = await SearchFlights(query.Supplier, queryString, cancellationToken);
        }

        var stringContent = await response.Content.ReadAsStringAsync(cancellationToken);

        if (response.StatusCode == HttpStatusCode.OK)
        {
            return JsonConvert.DeserializeObject<SearchResponse>(stringContent);
        }

        _logger.LogInformation(
            "[{StatusCode}] {Departure}-{Arrival} {Date}|{ReturnDate}, Content: {Content}",
            response.StatusCode,
            query.DepartureCode,
            query.ArrivalCode,
            query.DepartureDay.ToString("yyyy-MM-dd"),
            query.ReturnDepartureDay?.ToString("yyyy-MM-dd"),
            stringContent
        );

        if (response.StatusCode == HttpStatusCode.NoContent)
        {
            return new SearchResponse { Data = [] };
        }

        return null;
    }

    private async Task<HttpResponseMessage> SearchFlights(string supplier, QueryString queryString, CancellationToken cancellationToken)
    {
        var sscProviderCode = (int)ProviderCodeEnum.SSCProvider;
        var result = await _httpClient.GetAsync($"flights{queryString}", cancellationToken);
        Metrics.Provider.ReportSearch(sscProviderCode, result.IsSuccessStatusCode, supplier);
        return result;
    }

    private async Task ConfigureSettings(string supplier, CancellationToken cancellationToken)
    {
        var settings = new { Suppliers = new[] { supplier } };
        var response = await _httpClient.PutAsJsonAsync($"Settings/{GetSettingsName(supplier)}", settings, cancellationToken: cancellationToken);
        response.EnsureSuccessStatusCode();
    }

    private static string GetSettingsName(string supplier) => $"{SSCQueueElement.RobotPrefix}.{supplier}";
}