using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ISSCSupplierConfiguration
{
    Config? Get(string supplierName);
    Task<IEnumerable<Config>> GetConfigurations(CancellationToken cancellationToken);

    public record Config
    {
        public static Config Default(string supplier) => new() { Name = supplier };
        public required string Name { get; init; }
        public int? PrefetchCount { get; init; }
        public int? ConcurrencyLimit { get; init; }
        public int InitialSeatsCountToCheck { get; init; } = 2;
        public int MaxSeatsCountToCheck { get; init; } = 2;
        public bool UseSeparateQueueForCalendar { get; set; }
    }
}

public class AppSettingsSSCSupplierConfiguration : ISSCSupplierConfiguration
{
    private readonly Dictionary<string, ISSCSupplierConfiguration.Config>? _suppliers;

    public AppSettingsSSCSupplierConfiguration(IConfiguration configuration)
    {
        _suppliers = configuration
            .GetSection("SSCSupplierConfiguration")
            .Get<Dictionary<string, ISSCSupplierConfiguration.Config>>()
            ?.ToDictionary(x => x.Key, x => x.Value with { Name = x.Key });
    }

    public ISSCSupplierConfiguration.Config? Get(string supplierName)
    {
        return _suppliers?.GetValueOrDefault(supplierName);
    }

    public Task<IEnumerable<ISSCSupplierConfiguration.Config>> GetConfigurations(CancellationToken cancellationToken)
    {
        return Task.FromResult(_suppliers?.Select(kp => kp.Value) ?? []);
    }
}