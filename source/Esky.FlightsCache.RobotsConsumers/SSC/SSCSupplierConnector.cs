using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using Microsoft.Extensions.Hosting;
using RabbitMQ.Client;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public class SSCSupplierConnector : IHostedService
{
    private const int _defaultPrefetchCount = 4;
    private readonly IReceiveEndpointConnector _connector;
    private readonly ISSCSupplierConfiguration _suppliers;

    public SSCSupplierConnector(IReceiveEndpointConnector connector, ISSCSupplierConfiguration suppliers)
    {
        _connector = connector;
        _suppliers = suppliers;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        var unmatched = _connector.ConnectReceiveEndpoint(
            SSCQueueElement.QueueNameForUnmatched(),
            (context, endpoint) =>
            {
                endpoint.ConfigureConsumeTopology = false;
                endpoint.PrefetchCount = _defaultPrefetchCount;
                endpoint.ConfigureConsumer<SSCQueueElementConsumer>(context);
            }
        );
        await unmatched.Ready;

        var unmatchedCalendar = _connector.ConnectReceiveEndpoint(
            SSCCalendarQueueElement.QueueNameForUnmatched(),
            (context, endpoint) =>
            {
                endpoint.ConfigureConsumeTopology = false;
                endpoint.PrefetchCount = _defaultPrefetchCount;
                endpoint.ConfigureConsumer<SSCCalendarQueueConsumer>(context);
            }
        );
        await unmatchedCalendar.Ready;

        var suppliers = await _suppliers.GetConfigurations(cancellationToken);
        foreach (var supplier in suppliers)
        {
            await Connect<SSCQueueElementConsumer>(supplier, SSCQueueElement.RobotPrefix, SSCQueueElement.QueueNameForUnmatched());
            if(supplier.UseSeparateQueueForCalendar)
            {
                await Connect<SSCCalendarQueueConsumer>(supplier, SSCCalendarQueueElement.RobotPrefix, SSCCalendarQueueElement.QueueNameForUnmatched());
            }
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    private Task Connect<T>(ISSCSupplierConfiguration.Config supplier, string robotPrefix, string unmatchedQueueName) where T : class, IConsumer
    {
        var routingKey = supplier.Name.ToLowerInvariant();
        var handle = _connector.ConnectReceiveEndpoint(
            $"{robotPrefix}.{routingKey}",
            (context, endpoint) =>
            {
                endpoint.ConfigureConsumeTopology = false;
                endpoint.PrefetchCount = supplier.PrefetchCount ?? _defaultPrefetchCount;
                endpoint.ConcurrentMessageLimit = supplier.ConcurrencyLimit;
                endpoint.ConfigureConsumer<T>(context);

                switch (endpoint)
                {
                    case IRabbitMqReceiveEndpointConfigurator rabbit:
                        rabbit.Bind(
                            robotPrefix,
                            c =>
                            {
                                c.RoutingKey = routingKey;
                                c.ExchangeType = ExchangeType.Direct;
                                c.SetExchangeArgument(RabbitMQ.Client.Headers.AlternateExchange, unmatchedQueueName);
                            }
                        );
                        return;
                    case IInMemoryReceiveEndpointConfigurator inMemory:
                        inMemory.Bind( // in memory transport is used in some tests
                            robotPrefix,
                            MassTransit.Transports.Fabric.ExchangeType.Direct,
                            routingKey
                        );
                        return;
                    default:
                        throw new Exception("Only available on InMemory and RabbitMQ endpoint");
                }
            }
        );

        return handle.Ready;
    }
}