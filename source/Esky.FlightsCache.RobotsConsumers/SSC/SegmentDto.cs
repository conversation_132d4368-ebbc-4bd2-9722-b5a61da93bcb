using Esky.Flights.Integration.Providers.Contract;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public class SegmentDto
{
    [JsonProperty("airline")] public string AirlineCode { get; set; }
    [JsonProperty("flightNo")] public string FlightNumber { get; set; }
    [JsonProperty("origin")] public string OriginAirport { get; set; }
    [JsonProperty("destination")] public string DestinationAirport { get; set; }
    [JsonProperty("departureLocal")] public DateTime DepartureLocalTime { get; set; }
    [JsonProperty("arrivalLocal")] public DateTime ArrivalLocalTime { get; set; }
    [JsonProperty("departureUtc")] public DateTime? DepartureUtc { get; set; }
    [JsonProperty("arrivalUtc")] public DateTime? ArrivalUtc { get; set; }
    [JsonProperty("operatedBy")] public string OperatedBy { get; set; }
    [JsonProperty("operatedFlightNo")] public string OperatedByFlightNumber { get; set; }
    [JsonProperty("aircraft")] public string AircraftCode { get; set; }
    public bool IsSelfTransfer { get; set; }
    public bool? IsProtected { get; set; }
    public int? SeatsRemaining { get; set; }
    public string FareCode { get; set; }
    public string BookingClass { get; set; }
    public int? Duration { get; set; }
}

public class LegDto
{
    public List<SegmentDto> Segments { get; set; }
    public int? Duration { get; set; }
    public SeparationOptions SeparationOptions { get; set; }
    /// <summary>
    /// Additional field to group legs on search results.
    /// If some flight should be in one group you can use this field.
    /// Sample: NDC Provider, we define offer id for each first segment.
    /// </summary>
    public string GroupingKey { get; set; }
    public DateTime? DataTimestamp { get; set; }
    public int? TotalFaresLeft { get; set; }
}

public class FlightDto
{
    /// <summary>
    /// a unique itinerary identifier. It is made up of flight IDs and is divided by |
    /// </summary>
    public string Id { get; set; }
    public List<LegDto> Legs { get; set; }
    public string BookingToken { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public int PnrCount { get; set; }
    public AvailabilityEnum Baggage { get; set; }
    public List<PriceDto> Prices { get; set; }
    /// <summary>
    /// If currency is common for all prices in search response, it should be provided in SearchResponse.Currency property
    /// </summary>
    public string Currency { get; set; }
    public string OfficeId { get; set; }
    public string Supplier { get; set; }
    public bool? IsReturn { get; set; }
    public bool IsAdditionalFlight { get; set; }
    public string FareFamilyCode { get; set; }
    public string ValidatingCarrier { get; set; }
}
public class PriceDto : SimplePriceDto
{
    public PassengerTypeEnum? PassengerType { get; set; }
    public PriceTypeEnum PriceType { get; set; }
    public int[] Segments { get; set; }
}

public class SimplePriceDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; }
}

public enum PriceTypeEnum
{
    Base,
    Tax,
    DU,
    MandatoryService
}

public class TimetableResponseDto
{
    public MonthlySearchResultsDto DepartureMonth { get; set; }

    public MonthlySearchResultsDto? ReturnMonth { get; set; }

    [JsonIgnore]
    public static TimetableResponseDto Empty { get; } = new TimetableResponseDto();
}

public class MonthlySearchResultsDto
{
    public MonthlySearchResultsDto()
    {
        Days = new List<DayOfMonthResultsDto>();
    }

    [DataType(DataType.Date)]
    public DateTime Month { get; set; }
    public List<DayOfMonthResultsDto> Days { get; set; }
}

public class DayOfMonthResultsDto
{
    public DayOfMonthResultsDto()
    {
        Flights = new List<FlightDto>();
        Availability = DayOfMonthFlightsAvailabilityDto.NotAvailable;
    }

    /// <summary>
    /// Departure day of month
    /// </summary>
    [DataType(DataType.Date)]
    public DateTime DayOfMonth { get; set; }

    /// <summary>
    /// Departure airport with provided min price
    /// </summary>
    public string DepartureCode { get; set; }

    /// <summary>
    /// Arrival airport with provided min price
    /// </summary>
    public string ArrivalCode { get; set; }

    /// <summary>
    /// List of available departure date times for selected day
    /// </summary>
    public List<DateTime> DepartureDates { get; set; }

    /// <summary>
    /// Min price for selected departure day
    /// </summary>
    public SimplePriceDto MinPrice { get; set; }

    /// <summary>
    /// Day of month availability
    /// </summary>
    public DayOfMonthFlightsAvailabilityDto Availability { get; set; }

    /// <summary>
    /// Flight details. Can be empty if flights not available or details can't be fetched
    /// </summary>
    public List<FlightDto> Flights { get; set; }
}

public enum DayOfMonthFlightsAvailabilityDto
{
    /// <summary>
    /// No flights available for selected day
    /// </summary>
    NotAvailable = 0,
    /// <summary>
    /// Flights available for selected day and all details fetched
    /// </summary>
    Available = 1,
    /// <summary>
    /// Flights available for selected day but can't get details information about flights. Required separate search request for this date.
    /// </summary>
    FlightsAvailableButDetailsShouldBeFetched = 2
}