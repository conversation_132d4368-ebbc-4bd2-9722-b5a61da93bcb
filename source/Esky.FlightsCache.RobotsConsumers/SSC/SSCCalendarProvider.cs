using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Net.Http;
using System.Net;
using System.Threading.Tasks;
using System.Threading;
using Newtonsoft.Json;
using Esky.FlightsCache.Robots;
using System.Net.Http.Json;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ISSCCalendarProvider
{
    Task<TimetableResponseDto?> Search(SSCCalendarQueueElement query, CancellationToken cancellationToken);
}

public class SSCCalendarProvider : ISSCCalendarProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SSCCalendarProvider> _logger;

    public SSCCalendarProvider(HttpClient httpClient, ILogger<SSCCalendarProvider> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<TimetableResponseDto?> Search(SSCCalendarQueueElement query, CancellationToken cancellationToken)
    {
        _httpClient.DefaultRequestHeaders.Add("source", query.SourceName);

        PaxConfiguration pax = query.PaxConfiguration;

        var queryString = QueryString.Create(
            new KeyValuePair<string, string?>[]
            {
                new("config", GetSettingsName(query.Supplier)),
                new("origin", query.DepartureCode),
                new("destination", query.ArrivalCode),
                new("dm", query.DepartureDay.ToString("yyyy-MM-dd")),
                new("rm", query.ReturnDepartureDay?.ToString("yyyy-MM-dd")),
                new("adt", pax.Adult.ToString()),
                new("yth", pax.Youth.ToString()),
                new("chd", pax.Child.ToString()),
                new("inf", pax.Infant.ToString())
            }
        );

        var response = await SearchFlights(query.Supplier, queryString, cancellationToken);

        if (response.StatusCode == HttpStatusCode.Conflict) // no settings
        {
            await ConfigureSettings(query.Supplier, cancellationToken);
            response = await SearchFlights(query.Supplier, queryString, cancellationToken);
        }

        var stringContent = await response.Content.ReadAsStringAsync(cancellationToken);

        if (response.StatusCode == HttpStatusCode.OK)
        {
            var searchResponse = JsonConvert.DeserializeObject<TimetableResponseDto>(stringContent);
            return searchResponse;
        }
        else
        {
            _logger.LogWarning("SSC Timetable error. Query: {Query}, Response: {Response}, Status: {Status}", queryString, stringContent, response.StatusCode);
            return null;
        }
    }

    private async Task<HttpResponseMessage> SearchFlights(string supplier, QueryString queryString, CancellationToken cancellationToken)
    {
        var sscProviderCode = (int)ProviderCodeEnum.SSCProvider;
        var result = await _httpClient.GetAsync($"Timetable{queryString}", cancellationToken);
        Metrics.Provider.ReportSearch(sscProviderCode, result.IsSuccessStatusCode, $"{supplier}-timetable");
        return result;
    }

    private async Task ConfigureSettings(string supplier, CancellationToken cancellationToken)
    {
        var settings = new { Suppliers = new[] { supplier } };
        var response = await _httpClient.PutAsJsonAsync($"Settings/{GetSettingsName(supplier)}", settings, cancellationToken: cancellationToken);
        response.EnsureSuccessStatusCode();
    }

    private static string GetSettingsName(string supplier) => $"{SSCCalendarQueueElement.RobotPrefix}.{supplier}";
}
