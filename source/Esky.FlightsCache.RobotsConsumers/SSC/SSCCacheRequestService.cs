using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ISSCCacheRequestService
{
    Task<IEnumerable<CacheRequest>> SearchFlights(SSCQueueElement queueElement, CancellationToken ct);
}

public class SSCCacheRequestService : ISSCCacheRequestService
{
    private readonly IStrategyFactory _strategyFactory;
    private readonly ISSCSupplierConfiguration _supplierConfiguration;
    private readonly ISearchService<SSCQueueElement> _searchService;

    public SSCCacheRequestService(
        IStrategyFactory strategyFactory,
        ISSCSupplierConfiguration supplierConfiguration,
        ISearchService<SSCQueueElement> searchService)
    {
        _strategyFactory = strategyFactory;
        _supplierConfiguration = supplierConfiguration;
        _searchService = searchService;
    }

    public async Task<IEnumerable<CacheRequest>> SearchFlights(SSCQueueElement queueElement, CancellationToken ct)
    {
        var cfg = _supplierConfiguration.Get(queueElement.Supplier) ?? ISSCSupplierConfiguration.Config.Default(queueElement.Supplier);
        var ctx = new Context(cfg.InitialSeatsCountToCheck, cfg.MaxSeatsCountToCheck);
        var strategy = _strategyFactory.IncrementalAdultSeats<SSCQueueElement>(_searchService, ctx);
        var cacheRequests = await strategy.Execute(queueElement, ct);
        return cacheRequests;
    }
}