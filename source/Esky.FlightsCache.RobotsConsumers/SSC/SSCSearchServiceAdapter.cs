using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public class SSCSearchServiceAdapter : ISearchService<SSCQueueElement>
{
    private readonly ISSCProvider _sscProvider;
    private readonly ISearchFlightsQueryBuilder _searchFlightsQueryBuilder;

    public SSCSearchServiceAdapter(ISSCProvider sscProvider, ISearchFlightsQueryBuilder searchFlightsQueryBuilder)
    {
        _sscProvider = sscProvider;
        _searchFlightsQueryBuilder = searchFlightsQueryBuilder;
    }

    public async Task<(SearchFlightsProviderQuery, SearchResponse?)> Search(SSCQueueElement message, string featureName, CancellationToken token)
    {
        var query = _searchFlightsQueryBuilder.BuildSingle(message);
        var response = await _sscProvider.Search(message, token);
        return (query, response);
    }
}