using MassTransit;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ISingleDaySearchDateProvider
{
    (List<DateTime> OutboundOnlyDates, List<(DateTime Departure, DateTime Return)> ReturnDates) GetSingleDaySearchDates(IEnumerable<DateTime> outboundDatesToCheck, IEnumerable<DateTime> allOutboundDates, IEnumerable<DateTime> inboundDatesToCheck, IEnumerable<DateTime> allInboundDates);
}
public class SingleDaySearchDateProvider : ISingleDaySearchDateProvider
{
    public (List<DateTime> OutboundOnlyDates, List<(DateTime Departure, DateTime Return)> ReturnDates) GetSingleDaySearchDates(IEnumerable<DateTime> outboundDatesToCheck, IEnumerable<DateTime> allOutboundDates, IEnumerable<DateTime> inboundDatesToCheck, IEnumerable<DateTime> allInboundDates)
    {
        var datePairs = new List<(DateTime Departure, DateTime Return)>();
        var singleDates = new List<DateTime>();
        foreach (var departureDate in outboundDatesToCheck)
        {
            if (!inboundDatesToCheck.Any())
            {
                singleDates.Add(departureDate);
            }
            else
            {
                var returnDate = inboundDatesToCheck.FirstOrDefault(x => x >= departureDate && datePairs.All(p => p.Return != x));

                if (returnDate == default)
                {
                    singleDates.Add(departureDate);
                }
                else
                {
                    datePairs.Add((departureDate, returnDate));
                }
            }
        }
        allOutboundDates = allOutboundDates.OrderByDescending(x => x).ToList();
        foreach (var returnDate in inboundDatesToCheck.Except(datePairs.Select(x => x.Return)))
        {
            var departureDate = allOutboundDates.FirstOrDefault(x => x <= returnDate);
            datePairs.Add((departureDate, returnDate));
        }

        return (singleDates, datePairs);
    }
}