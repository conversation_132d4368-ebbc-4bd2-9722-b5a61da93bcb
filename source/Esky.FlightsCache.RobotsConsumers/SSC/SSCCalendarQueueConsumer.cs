using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.TimetableServiceClient;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public class SSCCalendarQueueConsumer : IConsumer<SSCCalendarQueueElement>
{
    private readonly ISSCCalendarProvider _sscTimetableProvider;
    private readonly ICacheRequestBuilder _cacheRequestBuilder;
    private readonly ITimetableFlightCacheConverter _flightCacheConverter;
    private readonly ICacheTimeoutConfiguration _cacheTimeoutConfiguration;
    private readonly ISearchFlightsQueryBuilder _searchFlightsQueryBuilder;
    private readonly ITimetableServiceClient _timetableClient;
    private readonly ISingleDaySearchDateProvider _singleDaySearchDateProvider;
    private readonly ILogger<SSCCalendarQueueConsumer> _logger;

    private readonly RequestBuildSettings _outboundRequestBuildSettings = new();
    private readonly RequestBuildSettings _inboundRequestBuildSettings = new() { SkipSQLSave = true };

    private readonly Dictionary<string, string[]> _suppliersToAirlines = new()
    {
        ["wizzair"] = ["W6", "W9", "W4", "5W"]
    };

    public SSCCalendarQueueConsumer(ISSCCalendarProvider sscTimetableProvider, ICacheRequestBuilder cacheRequestBuilder, ITimetableFlightCacheConverter flightCacheConverter, ICacheTimeoutConfiguration cacheTimeoutConfiguration, ISearchFlightsQueryBuilder searchFlightsQueryBuilder, ITimetableServiceClient timetableClient, ISingleDaySearchDateProvider singleDaySearch, ILogger<SSCCalendarQueueConsumer> logger)
    {
        _sscTimetableProvider = sscTimetableProvider;
        _cacheRequestBuilder = cacheRequestBuilder;
        _flightCacheConverter = flightCacheConverter;
        _cacheTimeoutConfiguration = cacheTimeoutConfiguration;
        _searchFlightsQueryBuilder = searchFlightsQueryBuilder;
        _timetableClient = timetableClient;
        _singleDaySearchDateProvider = singleDaySearch;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SSCCalendarQueueElement> context)
    {
        var message = context.Message;
        var response = await GetCalendar(context);

        if (response is null)
        {
            await PublishAllSingleDayMessages(message, context);
            return;
        }

        var timeOutConfiguration = await _cacheTimeoutConfiguration.GetConfiguration;
        var outboundSeparationOptions = SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound;

        var outboundCalendar = response?.DepartureMonth.Days ?? [];
        var outboundDatesToCheck = outboundCalendar
            .Where(x => x.Availability == DayOfMonthFlightsAvailabilityDto.FlightsAvailableButDetailsShouldBeFetched)
            .Select(x => x.DayOfMonth);

        var outboundFlyingDates = await GetTimetable(message);

        foreach (var item in outboundCalendar
            .Where(x => x.Availability != DayOfMonthFlightsAvailabilityDto.FlightsAvailableButDetailsShouldBeFetched
                    && outboundFlyingDates.Contains(x.DayOfMonth)))
        {
            await PublishCacheRequest(item, context, timeOutConfiguration, outboundSeparationOptions, message.ForOutbound(), _outboundRequestBuildSettings);
        }

        var inboundCalendar = response?.ReturnMonth?.Days;
        var inboundDatesToCheck = Enumerable.Empty<DateTime>();
        var inboundFlyingDates = Enumerable.Empty<DateTime>();
        if (inboundCalendar != null)
        {
            inboundDatesToCheck = inboundCalendar
                .Where(x => x.Availability == DayOfMonthFlightsAvailabilityDto.FlightsAvailableButDetailsShouldBeFetched)
                .Select(x => x.DayOfMonth);
            var inboundSeparationOptions = MessageContract.SeparationOptionEnum.RoundTripInbound;
            var inboundQueueElement = message.ForInbound();
            inboundFlyingDates = await GetTimetable(inboundQueueElement);
            foreach (var item in inboundCalendar
                .Where(x => x.Availability != DayOfMonthFlightsAvailabilityDto.FlightsAvailableButDetailsShouldBeFetched
                    && inboundFlyingDates.Contains(x.DayOfMonth)))
            {
                await PublishCacheRequest(item, context, timeOutConfiguration, inboundSeparationOptions, inboundQueueElement, _inboundRequestBuildSettings);
            }
        }

        await PublishSingleDaySearchMessages(outboundDatesToCheck, outboundFlyingDates, inboundDatesToCheck, inboundFlyingDates, message, context);
    }

    private async Task<TimetableResponseDto?> GetCalendar(ConsumeContext<SSCCalendarQueueElement> context)
    {
        try
        {
            return await _sscTimetableProvider.Search(context.Message, context.CancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting calendar for SSC supplier {Supplier}", context.Message.Supplier);
            return null;
        }
    }

    private async Task PublishCacheRequest(DayOfMonthResultsDto item, ConsumeContext<SSCCalendarQueueElement> context, CacheTimeOutConfiguration timeOutConfiguration, MessageContract.SeparationOptionEnum separationOptions, SSCCalendarQueueElement queueElement, RequestBuildSettings requestBuildSettings)
    {
        var singleDayQueueElement = queueElement.ForDate(item.DayOfMonth);
        var flights = item.Flights
            .Where(x => x.Legs.Single().Segments.First().OriginAirport == singleDayQueueElement.DepartureCode
                && x.Legs.Single().Segments.Last().DestinationAirport == singleDayQueueElement.ArrivalCode);
        var cacheRequest = CreateCacheRequest(flights, singleDayQueueElement, separationOptions, timeOutConfiguration, requestBuildSettings);
        await context.Publish(cacheRequest, context.CancellationToken);
    }

    private async Task PublishAllSingleDayMessages(SSCCalendarQueueElement queueElement, ConsumeContext<SSCCalendarQueueElement> context)
    {
        var outboundFlyingDates = await GetTimetable(queueElement);
        var inboundQueueElement = queueElement.ForInbound();
        var inboundFlyingDates = await GetTimetable(inboundQueueElement);

        var outboundDatesToCheck = outboundFlyingDates.Where(x => x.Year == queueElement.DepartureDay.Year && x.Month == queueElement.DepartureDay.Month);

        var inboundDatesToCheck = queueElement.ReturnDepartureDay.HasValue
            ? inboundFlyingDates.Where(x => x.Year == queueElement.ReturnDepartureDay.Value.Year && x.Month == queueElement.ReturnDepartureDay.Value.Month)
            : [];

        await PublishSingleDaySearchMessages(outboundDatesToCheck, outboundFlyingDates, inboundDatesToCheck, inboundFlyingDates, queueElement, context);
    }

    private async Task<IList<DateTime>> GetTimetable(SSCCalendarQueueElement queueElement)
    {
        var airlineCodes = queueElement.AirlineCode is not null ? [queueElement.AirlineCode] : _suppliersToAirlines[queueElement.Supplier];
        var allDepartureDays = await _timetableClient.GetFlyingDays(queueElement.DepartureCode, queueElement.ArrivalCode, airlineCodes);
        var minDepartureDate = queueElement.DepartureDay.AddMonths(-1);
        var maxDepartureDate = queueElement.DepartureDay.AddMonths(2);
        return allDepartureDays.Where(x => minDepartureDate < x && x < maxDepartureDate).ToList();
    }

    private async Task PublishSingleDaySearchMessages(IEnumerable<DateTime> outboundDatesToCheck, IEnumerable<DateTime> allOutboundDates, IEnumerable<DateTime> inboundDatesToCheck, IEnumerable<DateTime> allInboundDates, SSCCalendarQueueElement message, ConsumeContext<SSCCalendarQueueElement> context)
    {
        var sscQueueElementTemplate = message.To<SSCQueueElement>();
        sscQueueElementTemplate.SourceName = $"SSCRobot_{sscQueueElementTemplate.Supplier}";

        var (singleDates, datePairs) = _singleDaySearchDateProvider.GetSingleDaySearchDates(outboundDatesToCheck, allOutboundDates, inboundDatesToCheck, allInboundDates);

        var returnQueueElements = datePairs.Select(x => sscQueueElementTemplate.ForDates(x.Departure, x.Return));
        var oneWayQueueElements = singleDates.Select(x => sscQueueElementTemplate.ForDate(x));

        await context.PublishBatch(returnQueueElements, context.CancellationToken);
        await context.PublishBatch(oneWayQueueElements, context.CancellationToken);
    }

    private CacheRequest CreateCacheRequest(IEnumerable<FlightDto> flights, SSCCalendarQueueElement message, MessageContract.SeparationOptionEnum separationOptions, CacheTimeOutConfiguration timeOutConfiguration, RequestBuildSettings requestBuildSettings)
    {
        var query = _searchFlightsQueryBuilder.BuildSingle(message);
        var cacheFlights = _flightCacheConverter.Convert(flights, separationOptions, message, timeOutConfiguration);
        var cacheRequest = _cacheRequestBuilder.Build(cacheFlights, query, message, requestBuildSettings);

        return cacheRequest;
    }
}
