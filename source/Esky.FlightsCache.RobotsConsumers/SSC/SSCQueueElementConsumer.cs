using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public class SSCQueueElementConsumer : IConsumer<SSCQueueElement>
{
    private readonly ISSCCacheRequestService _cacheRequestService;

    public SSCQueueElementConsumer(ISSCCacheRequestService cacheRequestService)
    {
        _cacheRequestService = cacheRequestService;
    }

    public async Task Consume(ConsumeContext<SSCQueueElement> context)
    {
        var requests = await _cacheRequestService.SearchFlights(context.Message, context.CancellationToken);
        await context.PublishBatch(requests, context.CancellationToken);
    }
}