using Esky.FlightsCache.Contract;
using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.RobotsConsumers.SSC;

public interface ITimetableFlightCacheConverter
{
    List<FlightCache> Convert(IEnumerable<FlightDto> flights, MessageContract.SeparationOptionEnum separationOptions, QueueElement queueElement, CacheTimeOutConfiguration timeOutConfiguration);
}

public class TimetableFlightCacheConverter : ITimetableFlightCacheConverter
{
    private readonly ICurrencyRatioProvider _currencyRatioProvider;

    public TimetableFlightCacheConverter(ICurrencyRatioProvider ratioProvider)
    {
        _currencyRatioProvider = ratioProvider;
    }

    public List<FlightCache> Convert(IEnumerable<FlightDto> flights, MessageContract.SeparationOptionEnum separationOptions, QueueElement queueElement, CacheTimeOutConfiguration timeOutConfiguration)
    {
        var paxConfiguration = new PaxConfiguration(queueElement.PaxConfiguration);
        var resList = flights.Select(flight =>
        {
            var providerCode = queueElement.ProviderCode;
            var expirationHours = timeOutConfiguration.GetTimeOutInHoursForProvider((int)(providerCode ?? 0), flight.Legs.First().Segments.First().DepartureLocalTime);
            var startExpirationDate = flight.Legs.Min(x => x.DataTimestamp) ?? DateTime.UtcNow;
            var expirationDate = startExpirationDate.AddHours(expirationHours);

            var legs = flight.Legs.Select((leg, i) =>
                new FlightCacheLeg
                {
                    ArrivalCode = leg.Segments.Last().DestinationAirport,
                    ArrivalDate = leg.Segments.Last().ArrivalLocalTime,
                    DepartureCode = leg.Segments.First().OriginAirport,
                    DepartureDate = leg.Segments.First().DepartureLocalTime,
                    AirlineCode = leg.Segments.First().AirlineCode,
                    CurrencyCode = flight.Currency,
                    ConversionRatioToReferenceCurrency = _currencyRatioProvider.GetRatio(flight.Currency, "EUR"),
                    DepartureSearchCodes = [leg.Segments.First().OriginAirport],
                    ArrivalSearchCodes = [leg.Segments.Last().DestinationAirport],
                    DepartureAirportDetails = new AirportDetails(),
                    ArrivalAirportDetails = new AirportDetails(),
                    SeparationOptions = new MessageContract.SeparationOptions { Options = separationOptions },
                    TotalFaresLeft = leg.TotalFaresLeft,
                    Segments = leg.Segments.Select(s => new FlightCacheSegment
                    {
                        ArrivalCode = s.DestinationAirport,
                        ArrivalDate = s.ArrivalLocalTime,
                        DepartureCode = s.OriginAirport,
                        DepartureDate = s.DepartureLocalTime,
                        AirlineCode = s.AirlineCode,
                        FlightNumber = s.FlightNumber,
                        OperatingAirlineCode = s.OperatedBy,
                        OperatingFlightNumber = s.OperatedByFlightNumber,
                        BookingClass = s.BookingClass,
                        FareDetails = new FareDetails
                        {
                            FareCode = s.FareCode
                        },
                        AircraftCode = s.AircraftCode
                    }).ToList(),
                    AdultPrices = LegPrices(flight, paxConfiguration.GetRequestedSeatsCount()),
                    ChildPrices = [],
                    InfantPrices = [],
                    DataTimestamp = leg.DataTimestamp
                }).ToList();

            return new FlightCache
            {
                ExpirationDate = expirationDate,
                LegsCanBeUseSeparately = true,
                ProviderCode = (int)(providerCode ?? 0),
                ValidatingCarrier = flight.Legs.First().Segments.First().AirlineCode,
                Supplier = queueElement.Supplier,
                Legs = legs
            };
        }).ToList();

        return resList;
    }

    private List<PriceCacheEntry> LegPrices(FlightDto flight, int numberOfPaxes)
    {
        var price = new PriceCacheEntry
        {
            BasePrice = flight.Prices.FirstOrDefault(p => p.PriceType == PriceTypeEnum.Base)?.Amount / numberOfPaxes ?? 0,
            TaxPrice = flight.Prices.FirstOrDefault(p => p.PriceType == PriceTypeEnum.Tax)?.Amount / numberOfPaxes ?? 0,
        };
        return [price];
    }
}