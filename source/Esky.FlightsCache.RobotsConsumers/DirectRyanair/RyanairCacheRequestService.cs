using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Configuration;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.DirectRyanair;

public interface IRyanairCacheRequestService
{
    Task<IEnumerable<CacheRequest>> SearchFlights(RyanAirDirectQueueElement queueElement, CancellationToken ct);
}

public class RyanairCacheRequestService : IRyanairCacheRequestService
{
    private const string _featureName = "RyanairDirect";

    private readonly DirectRyanairConfiguration _settings;
    private readonly ISearchService<RyanAirDirectQueueElement> _searchService;
    private readonly IStrategyFactory _strategyFactory;

    public RyanairCacheRequestService(
        DirectRyanairConfiguration settings,
        ISearchService<RyanAirDirectQueueElement> searchService,
        IStrategyFactory strategyFactory)
    {
        _settings = settings;
        _searchService = searchService;
        _strategyFactory = strategyFactory;
    }

    public async Task<IEnumerable<CacheRequest>> SearchFlights(RyanAirDirectQueueElement queueElement, CancellationToken ct)
    {
        if (queueElement.DepartureDay < DateTime.Today)
        {
            return [];
        }

        queueElement.ProviderCode ??= ProviderCodeEnum.DirectRyanair;
        queueElement.FeatureName ??= _featureName;

        var context = new Context(_settings.InitialSeatsCountToCheck, _settings.MaxSeatsCountToCheck);
        var incrementalAdultSeats = _strategyFactory.IncrementalAdultSeats(_searchService, context);
        var strategy = queueElement.IsRoundTrip
            ? _strategyFactory.RoundTripAsTwoOneWayWithDepartureAirportCurrency(incrementalAdultSeats, RyanairExtension.Instance)
            : _strategyFactory.DiffCurrencyOneWayBothCurrenciesStrategy(incrementalAdultSeats, RyanairExtension.Instance);

        var cacheRequests = await strategy.Execute(queueElement, ct);
        return cacheRequests;
    }
}