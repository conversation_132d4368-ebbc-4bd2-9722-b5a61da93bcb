using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using System.Collections.Generic;

namespace Esky.FlightsCache.RobotsConsumers.DirectRyanair;

public class RyanairExtension : IExtension
{
    public static readonly RyanairExtension Instance = new();

    public void OnSameCurrency(IReadOnlyCollection<CacheRequest> cacheRequests)
    {
        cacheRequests.AddInboundSeparationOption();
    }

    public void OnDifferentCurrency(IReadOnlyCollection<CacheRequest> cacheRequestsDifferentCurrency)
    {
        cacheRequestsDifferentCurrency.SetInboundSeparationOption();
    }
}