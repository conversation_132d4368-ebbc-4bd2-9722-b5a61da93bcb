using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Robots.BigQuery;
using Esky.FlightsCache.RobotsConsumers.SSC;
using Esky.FlightsCache.RobotsProducers.Messages;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.DirectRyanair;

public interface IRyanairSSCComparisonService
{
    Task<IEnumerable<CacheRequest>> SearchFlights(RyanAirDirectQueueElement queueElement, CancellationToken ct);
}

public class RyanairSscComparisonService(
    IRyanairCacheRequestService ryanairCacheRequestService,
    ISSCCacheRequestService sscCacheRequestService,
    RyanairSSCComparisonSettings settings,
    ICacheRequestDiffStorage bqStorage,
    ILogger<RyanairSscComparisonService> logger) : IRyanairSSCComparisonService
{
    public async Task<IEnumerable<CacheRequest>> SearchFlights(RyanAirDirectQueueElement queueElement, CancellationToken ct)
    {
        if (Random.Shared.Next(settings.SamplingOneOf) != 1)
        {
            return await ryanairCacheRequestService.SearchFlights(queueElement, ct);
        }

        var sscRequests = new List<CacheRequest>();
        var ryanairRequests = new List<CacheRequest>();

        var ryanairTask = ryanairCacheRequestService.SearchFlights(queueElement, ct);
        var sscTask = sscCacheRequestService.SearchFlights(CreateSSCQueueElement(queueElement), ct);

        try
        {
            ryanairRequests = (await ryanairTask).ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during ryanair search");
        }

        _ = Task.Run(async () =>
        {
            try
            {
                sscRequests = (await sscTask).ToList();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during ssc search");
            }

            await bqStorage.SaveDiff(ryanairRequests, sscRequests);
        }, ct);

        return ryanairRequests;
    }

    private static SSCQueueElement CreateSSCQueueElement(RyanAirDirectQueueElement message)
    {
        return SSCQueueElement.Create(
            message.DepartureCode,
            message.ArrivalCode,
            message.DepartureDay,
            message.ReturnDepartureDay,
            "ryanair-new",
            pax: "1.0.0.0");
    }
}

public record RyanairSSCComparisonSettings
{
    public required int SamplingOneOf { get; init; } = 1000;
}