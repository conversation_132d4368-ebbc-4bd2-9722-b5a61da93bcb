using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using System.Threading.Tasks;

namespace Esky.FlightsCache.RobotsConsumers.DirectRyanair;

public class RyanAirDirectQueueElementConsumer : IConsumer<RyanAirDirectQueueElement>
{
    private readonly IRyanairSSCComparisonService _ryanairCacheRequestService;

    public RyanAirDirectQueueElementConsumer(IRyanairSSCComparisonService ryanairCacheRequestService)
    {
        _ryanairCacheRequestService = ryanairCacheRequestService;
    }

    public async Task Consume(ConsumeContext<RyanAirDirectQueueElement> context)
    {
        var requests = await _ryanairCacheRequestService.SearchFlights(context.Message, context.CancellationToken);
        await context.PublishBatch(requests, context.CancellationToken);
    }
}