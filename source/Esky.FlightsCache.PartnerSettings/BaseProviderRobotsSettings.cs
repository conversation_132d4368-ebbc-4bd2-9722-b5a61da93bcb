namespace Esky.FlightsCache.PartnerSettings
{

    public class BaseProviderRobotsSettings
    {
        /// <summary>
        /// Number of threads per consument/per machine.
        /// </summary>
        public int NumberOfProcessingThreads { get; set; }

        public bool IsActive { get; set; }

        public int CircuitBreakerActiveThreshold { get; set; }
        public int CircuitBreakerResetIntervalSeconds { get; set; }
        public int CircuitBreakerTrackingPeriodSeconds { get; set; }
        public int CircuitBreakerTripThreshold { get; set; }
        public int RetryIntervalIncrement { get; set; }
        public int RetryInitialIntervalSeconds { get; set; }
        public int RetryLimit { get; set; }
    }

}