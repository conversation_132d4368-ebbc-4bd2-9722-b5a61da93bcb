using Esky.Framework.PartnerSettings;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Esky.FlightsCache.PartnerSettings
{
    internal class PartnerSettingsService : IPartnerSettingsService
    {
        private const string _masterPartnerCode = "MASTER";
        private readonly Framework.PartnerSettings.IPartnerSettingsService _service;

        public PartnerSettingsService(Framework.PartnerSettings.IPartnerSettingsService service)
        {
            _service = service;
        }

        public async Task<PartnerSettingsModel> GetPartnerSettingsAsync(string partnerCode)
        {
            return await _service.GetSettingAsync("PartnerSettingsModel", x => x.GetPartnerSettings<PartnerSettingsModel>(partnerCode), partnerCode);
        }

        public async Task<OfficeSettings> GetOfficeSettingsAsync(string officeId)
        {
            var officeIds = await _service.GetSettingAsync("OfficeSettings", x => x.GetOfficeSettings(_masterPartnerCode), _masterPartnerCode);
            return officeIds.FirstOrDefault(e => e.OfficeId == officeId);
        }
    }

    internal static class PartnerSettingsExtensions
    {
        public static async Task<IReadOnlyCollection<OfficeSettings>> GetOfficeSettings(this IPartnerSettingsRepository repo, string partnerCode)
        {
            const string key = "OfficeSettings_PartnerSettings/GDSConfiguration/OfficeSettings";
            var result = await repo.GetCustomSettings<OfficeSettings>(key, partnerCode);
            return result.Where(x => x.IsEnabled).ToList();
        }
    }
}