using System.Collections.Generic;

namespace Esky.FlightsCache.PartnerSettings
{
    public class RobotsConfiguration
    {
        /// <summary>
        /// Konfiguracja per provider
        /// </summary>
        public IDictionary<string, ProviderRobotsSettings> ProviderConfigurations { get; set; }

        /// <summary>
        /// Liczba dni do przeszukania
        /// </summary>
        public int NumberOfDaysForward { get; set; }
    }
}