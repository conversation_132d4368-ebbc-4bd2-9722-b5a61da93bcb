{"users": [{"name": "robotuser", "password": "robotpassword", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "robotuser", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "exchanges": [{"name": "Esky.FlightsCache.MessageContract:CacheRequest", "vhost": "/", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "queues": [{"name": "WorkerServiceStubQueue", "vhost": "/", "durable": true, "auto_delete": false}], "bindings": [{"source": "Esky.FlightsCache.MessageContract:CacheRequest", "vhost": "/", "destination": "WorkerServiceStubQueue", "destination_type": "queue", "routing_key": "", "arguments": {}}]}