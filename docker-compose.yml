services:

    robot.rabbitmq:
        image: masstransit/rabbitmq
        environment:
            RABBITMQ_DEFAULT_USER: robotuser
            RABBITMQ_DEFAULT_PASS: robotpassword
        ports:
            # AMQP protocol port
            - "5672:5672"
            # HTTP management UI
            - "15672:15672"
        extra_hosts:
            - "host.docker.internal:host-gateway"
        healthcheck:
            test: ["CMD", "bash", "-c", "echo hi > /dev/tcp/localhost/5672"]
            interval: 5s
            timeout: 25s
            retries: 5
        volumes:
          - "./rabbitmq.conf:/etc/rabbitmq/conf.d/rabbitmq.conf"
          - "./definitions.json:/etc/rabbitmq/definitions.json"

    mongo.objectdirectory: 
        image: mongo:7.0
        ports:
            - "27018:27017"
        volumes:
            - objectdirectorydb:/data/db
        extra_hosts:
            - "host.docker.internal:host-gateway"

    mongo.robotproducers: 
        image: mongo:7.0
        ports:
            - "27002:27017"
        volumes:
            - robotproducersdb:/data/db
        extra_hosts:
            - "host.docker.internal:host-gateway"

    mongo.promocache: 
        image: mongo:7.0
        ports:
            - "27003:27017"
        volumes:
            - promocachedb:/data/db
        extra_hosts:
            - "host.docker.internal:host-gateway"

    redis:
         image: redis:6.2-alpine
         ports:
            - "6379:6379"
         command: redis-server --loglevel warning --requirepass k8rBZeXosd
         volumes: 
            - redis:/data/redis
         extra_hosts:
            - "host.docker.internal:host-gateway"

    robotsproducer:
        image: robotsproducer
        build:
            context: .
            dockerfile: source/Esky.FlightsCache.RobotsProducers/Dockerfile
        ports:
            - "58799:58799"
        environment:
            GOOGLE_APPLICATION_CREDENTIALS: ./googleKey.json
            ASPNETCORE_URLS: http://+:58799
            ASPNETCORE_ENVIRONMENT: Development
        extra_hosts:
            - "host.docker.internal:host-gateway"
        volumes:
            -  ./googleKey.json:/app/googleKey.json 
        depends_on:
            - robot.rabbitmq
            - redis
            - mongo.objectdirectory
            - mongo.robotproducers
            - mongo.promocache
        healthcheck:
            test: bash -c 'while [[ "$$(curl --connect-timeout 2 -s -o /dev/null -w ''%{http_code}'' robot.rabbitmq:15672)" != "200" ]]; do echo ..; sleep 5; done; echo rabbit is up
            interval: 5s
            timeout: 15s
            retries: 3

    robotsconsumer:
        image: robotsconsumer
        build:
            context: .
            dockerfile: source/Esky.FlightsCache.RobotsConsumers/Dockerfile
        ports:
            - "58798:58798"
        environment:
            GOOGLE_APPLICATION_CREDENTIALS: ./googleKey.json
            ASPNETCORE_URLS: http://+:58798
            ASPNETCORE_ENVIRONMENT: Development
        extra_hosts:
            - "host.docker.internal:host-gateway"
        volumes:
            -  ./googleKey.json:/app/googleKey.json 
        depends_on:
            - robotsproducer

    mockserver:
        image: mockserver/mockserver
        ports:
          - "1090:1090"
        environment:
            LOG_LEVEL: "DEBUG"
            SERVER_PORT: 1090
            MOCKSERVER_PROPERTY_FILE: /config/mockserver.properties
            MOCKSERVER_INITIALIZATION_JSON_PATH: /config/initializerJson.json
            MOCKSERVER_LIVENESS_HTTP_GET_PATH: /health
        volumes:
            -  ./initializerJson.json:/config/initializerJson.json 
        extra_hosts:
            - "host.docker.internal:host-gateway"

volumes:
  objectdirectorydb:
  robotproducersdb:
  promocachedb:
  redis: