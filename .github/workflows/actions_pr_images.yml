name: PR-IMAGE-BUILD
run-name: BUILD PR IMAGE by @${{ github.actor }}
on:
  workflow_dispatch:

permissions:
  checks: write
  contents: write
  pull-requests: write

jobs:
  discover-affected-apps:
    runs-on: k8s-runner
    name: discover affected apps
    permissions:
      actions: read
      contents: read
    outputs:
      matrix-json: ${{ steps.apps.outputs.matrix-json }}
    env:
      DOTNET_INSTALL_DIR: ./.dotnet
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Discover affected docker apps
        id: apps
        uses: eskygroup/esky-flights-cache/.github/actions/discover-affected-docker-apps@master
        with:
          workflow-id: 'actions_master.yml'
          branch: ${{ github.base_ref }}

  build-and-publish-docker-images:
    runs-on: k8s-runner
    if: needs.discover-affected-apps.outputs.matrix-json != '{}'
    needs: discover-affected-apps
    strategy:
      matrix: ${{ fromJson(needs.discover-affected-apps.outputs.matrix-json) }}
    name: ${{ matrix.dockerImageName }}
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4

      - name: Login to docker
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}

      - name: Build docker image
        uses: eskygroup/github-actions/.github/actions/docker-build@master
        with:
          imageVendor: esky-ets-flightscontent-pro
          dockerfile_path: ${{ matrix.dockerfile }}
          appName: ${{ matrix.dockerImageName }}

      - name: Publish docker image
        uses: eskygroup/github-actions/.github/actions/docker-push@master
        with:
          imageVendor: esky-ets-flightscontent-pro
          appList: ${{ matrix.dockerImageName }}
          tagSuffix: "pr-spin"