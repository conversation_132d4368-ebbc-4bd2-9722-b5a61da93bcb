name: MASTER-BUILD
run-name: ${{ github.event_name == 'pull_request' && 'BUILD PR' || 'BUILD master' }}
on:
  push:
    branches:
      - master
  workflow_dispatch:

jobs:
  build-and-test:
    permissions: write-all
    runs-on: k8s-runner 
    env:
      DOTNET_INSTALL_DIR: ./.dotnet
    steps:
      - uses: actions/checkout@v4
        
      - name: Login to docker
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}

      - name: setup dotnet
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 8.0
          cache: false

      - name: Build solution
        run: dotnet build Esky.FlightsCache.Robots.sln

      - name: Test solution
        run: dotnet test Esky.FlightsCache.Robots.sln --no-build --verbosity normal --logger "trx;LogFileName=test-results.trx"

      - name: Generate test report
        if: always()
        uses: dorny/test-reporter@v1
        with:
          name: DotNET Tests
          path: "**/test-results.trx"
          reporter: dotnet-trx
          fail-on-error: true

  discover-affected-apps:
    runs-on: k8s-runner
    needs: build-and-test
    name: discover affected apps
    permissions:
      actions: read
      contents: read
    outputs:
      matrix-json: ${{ steps.apps.outputs.matrix-json }}
    env:
      DOTNET_INSTALL_DIR: ./.dotnet
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Discover affected docker apps
        id: apps
        uses: eskygroup/esky-flights-cache/.github/actions/discover-affected-docker-apps@master
        with:
          workflow-id: 'actions_master.yml'
          branch: 'master'
          
  build-and-publish-docker-images:
    runs-on: k8s-runner
    if: needs.discover-affected-apps.outputs.matrix-json != '{}'
    needs: discover-affected-apps
    strategy:
      matrix: ${{ fromJson(needs.discover-affected-apps.outputs.matrix-json) }}
    name: ${{ matrix.dockerImageName }}
    permissions: 
      contents: write
    steps:
      - uses: actions/checkout@v4

      - name: Login to docker
        uses: eskygroup/github-actions/.github/actions/docker-login@master
        with:
          registry_json_key: ${{ secrets.GCP_GITHUB_IMAGE_REGISTRY_RW }}

      - name: Build docker image
        uses: eskygroup/github-actions/.github/actions/docker-build@master
        with:
          imageVendor: esky-ets-flightscontent-pro
          dockerfile_path: ${{ matrix.dockerfile }}
          appName: ${{ matrix.dockerImageName }}

      - name: Publish docker image
        uses: eskygroup/github-actions/.github/actions/docker-push@master
        with:
          imageVendor: esky-ets-flightscontent-pro
          appList: ${{ matrix.dockerImageName }}

      - name: Git tag
        run: |
          git tag release/${{matrix.dockerImageName}}/gh-b${{github.run_number}}-spin HEAD
          git push origin --tags