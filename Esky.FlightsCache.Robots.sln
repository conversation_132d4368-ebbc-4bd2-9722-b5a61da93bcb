
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.ProviderMapping", "source\Esky.FlightsCache.ProviderMapping\Esky.FlightsCache.ProviderMapping.csproj", "{811069D2-6D56-45BE-89E3-A81FCD451AD3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Database", "source\Esky.FlightsCache.Database\Esky.FlightsCache.Database.csproj", "{224C7A7C-0D61-4BDB-932B-491CA222A880}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{E80F53A9-5C1B-4BFF-8932-E707620B9D6F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{09D25282-CBEB-4C98-9163-AE68B7CC5F4E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.CurrencyProvider", "source\Esky.FlightsCache.CurrencyProvider\Esky.FlightsCache.CurrencyProvider.csproj", "{7A1BA265-4414-4F94-B59E-1E51B6207EF1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.PartnerSettings", "source\Esky.FlightsCache.PartnerSettings\Esky.FlightsCache.PartnerSettings.csproj", "{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalServices", "ExternalServices", "{58BF9DC9-D47D-4DB0-AAEA-417863D6A373}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.RobotsProducers", "source\Esky.FlightsCache.RobotsProducers\Esky.FlightsCache.RobotsProducers.csproj", "{402BB7DA-CE72-4419-B6DC-48AAD7AB76CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.RobotsConsumers", "source\Esky.FlightsCache.RobotsConsumers\Esky.FlightsCache.RobotsConsumers.csproj", "{526B4A38-93D4-4CFC-8990-A5F221ECA37D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Tests", "source\Esky.FlightsCache.Tests\Esky.FlightsCache.Tests.csproj", "{32DCACAA-6559-4901-A1AA-6D5704FD25C0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Robots", "source\Esky.FlightsCache.Robots\Esky.FlightsCache.Robots.csproj", "{DC94C318-16DD-4E8A-8E7F-5C9408ED56D0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Robots.Messages", "source\Esky.FlightsCache.Robots.Messages\Esky.FlightsCache.Robots.Messages.csproj", "{F16F0164-417E-4799-9ADE-0E21F556DC4F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A4B116C9-38E3-40C5-8A82-FEC484C67A94}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		definitions.json = definitions.json
		JenkinsfileSpinnaker = JenkinsfileSpinnaker
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{A8BEADA7-EE1B-44D5-BDFD-8EDF03146AE4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Esky.FlightsCache.Robots.Integration.Tests", "source\Esky.FlightsCache.Robots.Integration.Tests\Esky.FlightsCache.Robots.Integration.Tests.csproj", "{1A8EC29D-512B-41D6-A115-B076FFF24D10}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Esky.FlightsCache.RobotsConsumers.Tests", "Esky.FlightsCache.RobotsConsumers.Tests\Esky.FlightsCache.RobotsConsumers.Tests.csproj", "{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{811069D2-6D56-45BE-89E3-A81FCD451AD3}.Release|Any CPU.Build.0 = Release|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{224C7A7C-0D61-4BDB-932B-491CA222A880}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{402BB7DA-CE72-4419-B6DC-48AAD7AB76CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{402BB7DA-CE72-4419-B6DC-48AAD7AB76CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{402BB7DA-CE72-4419-B6DC-48AAD7AB76CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{402BB7DA-CE72-4419-B6DC-48AAD7AB76CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{526B4A38-93D4-4CFC-8990-A5F221ECA37D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{526B4A38-93D4-4CFC-8990-A5F221ECA37D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{526B4A38-93D4-4CFC-8990-A5F221ECA37D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{526B4A38-93D4-4CFC-8990-A5F221ECA37D}.Release|Any CPU.Build.0 = Release|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC94C318-16DD-4E8A-8E7F-5C9408ED56D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC94C318-16DD-4E8A-8E7F-5C9408ED56D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC94C318-16DD-4E8A-8E7F-5C9408ED56D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC94C318-16DD-4E8A-8E7F-5C9408ED56D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{F16F0164-417E-4799-9ADE-0E21F556DC4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F16F0164-417E-4799-9ADE-0E21F556DC4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F16F0164-417E-4799-9ADE-0E21F556DC4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F16F0164-417E-4799-9ADE-0E21F556DC4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8BEADA7-EE1B-44D5-BDFD-8EDF03146AE4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8BEADA7-EE1B-44D5-BDFD-8EDF03146AE4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8BEADA7-EE1B-44D5-BDFD-8EDF03146AE4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8BEADA7-EE1B-44D5-BDFD-8EDF03146AE4}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A8EC29D-512B-41D6-A115-B076FFF24D10}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A8EC29D-512B-41D6-A115-B076FFF24D10}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A8EC29D-512B-41D6-A115-B076FFF24D10}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A8EC29D-512B-41D6-A115-B076FFF24D10}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{811069D2-6D56-45BE-89E3-A81FCD451AD3} = {E80F53A9-5C1B-4BFF-8932-E707620B9D6F}
		{224C7A7C-0D61-4BDB-932B-491CA222A880} = {E80F53A9-5C1B-4BFF-8932-E707620B9D6F}
		{7A1BA265-4414-4F94-B59E-1E51B6207EF1} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{AD2C0EE5-30C7-4827-9CA2-6CB2AE06B2E8} = {58BF9DC9-D47D-4DB0-AAEA-417863D6A373}
		{32DCACAA-6559-4901-A1AA-6D5704FD25C0} = {09D25282-CBEB-4C98-9163-AE68B7CC5F4E}
		{1A8EC29D-512B-41D6-A115-B076FFF24D10} = {09D25282-CBEB-4C98-9163-AE68B7CC5F4E}
		{6C74FCF8-19E6-41BE-8641-9EED8AAC4EC7} = {09D25282-CBEB-4C98-9163-AE68B7CC5F4E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6387F566-3849-45CA-8C9B-7596A1111BB1}
	EndGlobalSection
EndGlobal
