[
  {
    "httpRequest": {
      "method": "GET",
      "path": "/api/connectionnetworks/airline/RK"
    },
    "httpResponse": {
      "body": []
    }
  },
  {
    "httpRequest": {
      "method": "GET",
      "path": "/api/PartnerSettings/MASTER/CustomSettings"
    },
    "httpResponse": {
      "body": {
        "OfficeSettings_PartnerSettings/GDSConfiguration/OfficeSettings": [
          {
            "IsEnabled": true,
            "PartnerCode": null,
            "OfficeId": "TESTOFFICEID",
            "User": "Tester",
            "Password": "cGFzcw==",
            "Url": null,
            "AlternativeUser": null,
            "AlternativePassword": null,
            "AlternativeUrl": null,
            "Organization": "LATAM",
            "EndpointName": "AmadeusWebServicesPort",
            "ContactData": "CO +57 **********-ESKY-A",
            "CountryCode": "PL",
            "CurrencyCode": "PLN",
            "DataLength": 8,
            "IsConsolidator":,
            "TimeZoneId": "E. South America Standard Time",
            "IATACode": null,
            "Trace": null,
            "Providers": "58",
            "ProviderCodes": [
              58
            ],
            "OwnerCode": "MASTER",
            "EnvironmentCode": "PRO",
            "KeyId": 4242,
            "Rank": 1,
            "Level": 0
          }
        ]
      }
    }
  },
  {
    "httpRequest": {
      "method": "GET",
      "path": "/api/PartnerSettings/.*"
    },
    "httpResponse": {
      "body": {
        "SpecialOccasionsSettings": {
          "BasePriceCurrencies": [
            "BRL"
          ],
          "CacheTimeOutConfiguration": "<CacheTimeOutConfiguration>\n\t<ProvidersConfiguration>\n\t\t<CacheTimeOutProviderItem>\n\t\t\t<ProviderCodes>\n\t\t\t\t<ProviderCodeEnum>6</ProviderCodeEnum>\n\t\t\t</ProviderCodes>\n\t\t\t<TimeOutConfigurations>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>0</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>56</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>12</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>57</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>90</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>24</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>91</MinDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>36</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t</TimeOutConfigurations>\n\t\t</CacheTimeOutProviderItem>\n\t\t<CacheTimeOutProviderItem>\n\t\t\t<ProviderCodes>\n\t\t\t\t<ProviderCodeEnum>35</ProviderCodeEnum>\n\t\t\t\t<ProviderCodeEnum>81</ProviderCodeEnum>\n\t\t\t</ProviderCodes>\n\t\t\t<TimeOutConfigurations>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>0</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>14</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>4</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>15</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>28</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>6</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>29</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>90</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>12</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>91</MinDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>36</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t</TimeOutConfigurations>\n\t\t</CacheTimeOutProviderItem>\n\t\t<CacheTimeOutProviderItem>\n\t\t\t<ProviderCodes>\n\t\t\t\t<ProviderCodeEnum>11</ProviderCodeEnum>\n\t\t\t\t<ProviderCodeEnum>80</ProviderCodeEnum>\n\t\t\t</ProviderCodes>\n\t\t\t<TimeOutConfigurations>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>0</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>14</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>4</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>15</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>28</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>6</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>29</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>90</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>8</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>91</MinDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>36</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t</TimeOutConfigurations>\n\t\t</CacheTimeOutProviderItem>\n\t\t<CacheTimeOutProviderItem>\n\t\t\t<ProviderCodes>\n\t\t\t\t<ProviderCodeEnum>29</ProviderCodeEnum>\n\t\t\t</ProviderCodes>\n\t\t\t<TimeOutConfigurations>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>0</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>14</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>1</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>15</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>90</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>2</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>91</MinDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>36</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t</TimeOutConfigurations>\n\t\t</CacheTimeOutProviderItem>\n\t\t<CacheTimeOutProviderItem>\n\t\t\t<ProviderCodes>\n\t\t\t\t<ProviderCodeEnum>58</ProviderCodeEnum>\n\t\t\t</ProviderCodes>\n\t\t\t<TimeOutConfigurations>\n\t\t\t    <TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>0</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>10</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>7</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>11</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>45</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>10</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>46</MinDaysToDeparture>\n\t\t\t\t\t<MaxDaysToDeparture>90</MaxDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>24</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t\t<TimeOutFlightConfiguration>\n\t\t\t\t\t<MinDaysToDeparture>91</MinDaysToDeparture>\n\t\t\t\t\t<TimeOutInHours>36</TimeOutInHours>\n\t\t\t\t</TimeOutFlightConfiguration>\n\t\t\t</TimeOutConfigurations>\n\t\t</CacheTimeOutProviderItem>\n\t</ProvidersConfiguration>\n</CacheTimeOutConfiguration>",
          "DataFeed": {
            "BlueAir": {
              "FTPUrl": "89.149.8.172",
              "FTPUserName": "BlueAirFTP",
              "FTPPassword": "w3flyGreen!1",
              "BatchSize": 1000,
              "ConnectionNetworkFile": "ScheduledJourneyRoute.csv",
              "DomesticSurcharge": "5.5",
              "InternationalSurcharge": "4",
              "DomesticAirports": [
                "BCM",
                "CRN",
                "XHV",
                "IAS",
                "OTP",
                "OMR",
                "CLJ",
                "SBZ",
                "CND",
                "TSR"
              ],
              "Files": [
                "Fares_0_30.csv",
                "Fares_31_60.csv",
                "Fares_61_90.csv",
                "Fares_91_120.csv",
                "Fares_121_150.csv",
                "Fares_151_365.csv"
              ]
            }
          },
          "IsTFForSpecialOffersEnabled": false,
          "MinHoursToDepartureForFlightExpiration": 3,
          "NewTestSection": {
            "Test2": {
              "Test2Key": ""
            }
          },
          "NumberOfCheapestRecordsToSaveInCache": 2,
          "PartnerCodes": [
            "ESKY",
            "ESKYRO",
            "ESKYBG",
            "EDESTINOS",
            "EDESTINOSPE",
            "ESKYCZ",
            "ESKYMD",
            "ESKYTR",
            "ESKYCOM",
            "ESKYSK",
            "ESKYHU",
            "EDESTINOSCO",
            "ESKYLT",
            "ESKYGE",
            "EDESTINOSDO",
            "EDESTINOSSV",
            "EDESTINOSPA",
            "EDESTINOSNI",
            "EDESTINOSGT",
            "EDESTINOSHN",
            "EDESTINOSPY",
            "EDESTINOSPR",
            "EDESTINOSCR",
            "EDESTINOSBO",
            "ESKYIE",
            "ESKYBA",
            "ESKYRS",
            "ESKYHR",
            "ESKYUK",
            "ESKYES",
            "EDESTINOSCOM",
            "ESKYEU",
            "ESKYGR",
            "ESKYFR",
            "ESKYPT",
            "ESKYTRAVELDE",
            "ESKYTRAVELIT",
            "EDESTINOSMX",
            "EDESTINOSAR",
            "EDESTINOSCL",
            "ESKYAT",
            "ESKYNO",
            "ESKYSE",
            "ESKYDK",
            "ESKYFI",
            "ESKYAU",
            "ESKYBE",
            "ESKYNL",
            "ESKYCH",
            "ESKYNZ",
            "ESKYZA",
            "ESKYRU",
            "ESKYBY",
            "ESKYAZ",
            "ESKYKZ",
            "ESKYUZ",
            "ESKYEE",
            "ESKYLV",
            "ESKYMA",
            "ESKYNG",
            "ESKYEG",
            "ESKYMY",
            "ESKYSG",
            "ESKYKE",
            "ESKYHK"
          ],
          "PriceAlertSettings": {
            "MaxNumberOfFlights": 50,
            "MonthsCountToInclude": 11,
            "MaxDaysForReturn": 21,
            "NumberOfPriceGroupsToReturn": 1
          },
          "PriceChangeMonitorSettings": {
            "MinPercentageChange": -5,
            "DomainName": "http://www2.esky.pl/okazje"
          },
          "RobotsConfiguration": {
            "ProviderConfigurations": {
              "RyanAirDirectRobotsSettings": {
                "NumberOfProcessingThreads": 2,
                "RetryLimit": 2,
                "IsActive": true,
                "RobotProviderCode": 78,
                "CircuitBreakerResetIntervalSeconds": 0,
                "RetryIntervalIncrement": 5,
                "RetryInitialIntervalSeconds": 2,
                "CircuitBreakerTrackingPeriodSeconds": 0,
                "CircuitBreakerTripThreshold": 0,
                "CircuitBreakerActiveThreshold": 0
              },
              "RyanAirRobotsSettings": {
                "TorPercentage": 0,
                "LuminatiProxyPercentage": 0,
                "RegularProxiesPercentage": 1,
                "UseAngularApi": true,
                "FilterFlightsByCalendar": true,
                "ShouldScanEntireConnectionNetwork": true,
                "PriceValidationThreshold": "0.3",
                "ValidateFlights": true,
                "ForceSSCRobotSearchIfAirportCurrenciesAreDifferent": false,
                "AllowCalendarFlowForSSCRobot": false,
                "NumberOfProcessingThreads": 2,
                "RetryLimit": 2,
                "IsActive": true,
                "ValidateWithReturnFlights": true,
                "RobotProviderCode": 6,
                "ProcessingNodeNames": [
                  "IBE-08",
                  "IBE-07",
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-23",
                  "IBE-24",
                  "IBE-25",
                  "IBE-26",
                  "IBE-27",
                  "IBE-28",
                  "IBE-29",
                  "IBE-30",
                  "IBE-31",
                  "IBE-32"
                ],
                "CircuitBreakerResetIntervalSeconds": 0,
                "RetryIntervalIncrement": 5,
                "UseMobileApiForSearching": false,
                "RetryInitialIntervalSeconds": 2,
                "CircuitBreakerTrackingPeriodSeconds": 0,
                "CircuitBreakerTripThreshold": 0,
                "CircuitBreakerActiveThreshold": 0
              },
              "WizzAirRobotsSettings": {
                "TorPercentage": 10,
                "LuminatiProxyPercentage": 0,
                "NumberOfPaxToGet": 1,
                "MaxNumberOfSearchesInParallel": 1,
                "UseMobileApiForSearching": false,
                "NumberOfMonthsToGet": 5,
                "StoreToSSCCache": true,
                "UseExtendedSearchLogger": false,
                "NumberOfProcessingThreads": 1,
                "UseWebApiForGettingTimetable": true,
                "UseWebApiForSearching": true,
                "IsActive": true,
                "UseTimetableService": true,
                "BrowserRobotsEnabled": true,
                "BrowserRobotsDaysToSearch": 155,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03"
                ],
                "ShouldScanEntireConnectionNetwork": false
              },
              "EasyJetRobotsSettings": {
                "TorPercentage": 100,
                "LuminatiProxyPercentage": 0,
                "NumberOfPaxToGet": 4,
                "StoreToSSCCache": true,
                "UseMobileApiForSearching": true,
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "ShouldScanEntireConnectionNetwork": true,
                "RetryLimit": 1,
                "ProcessingNodeNames": [
                  "IBE-06",
                  "IBE-07",
                  "IBE-08",
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05"
                ],
                "UseExtendedSearchLogger": false
              },
              "GermanWingsRobotsSettings": {
                "TorPercentage": 100,
                "LuminatiProxyPercentage": 0,
                "NumberOfProcessingThreads": 2,
                "IsActive": true,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "TamRobotsSettings": {
                "TorPercentage": 60,
                "RegularProxiesPercentage": 40,
                "LuminatiProxyPercentage": 0,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ],
                "MaxRequestsPerDay": 0,
                "NumberOfProcessingThreads": 0,
                "IsActive": false
              },
              "AviancaRobotsSettings": {
                "TorPercentage": 60,
                "RegularProxiesPercentage": 40,
                "LuminatiProxyPercentage": 0,
                "NumberOfProcessingThreads": 4,
                "IsActive": true,
                "RetryLimit": 1,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ],
                "ShouldScanDefinedRoutes": false,
                "RetryInitialIntervalSeconds": 0,
                "ShouldScanEntireConnectionNetwork": false
              },
              "AzulRobotsSettings": {
                "TorPercentage": 60,
                "RegularProxiesPercentage": 20,
                "LuminatiProxyPercentage": 0,
                "ShouldScanDefinedRoutes": false,
                "ProcessingNodeNames": [],
                "IsActive": false,
                "NumberOfProcessingThreads": 0
              },
              "GolRobotsSettings": {
                "TorPercentage": 60,
                "RegularProxiesPercentage": 40,
                "LuminatiProxyPercentage": 0,
                "IsActive": true,
                "NumberOfProcessingThreads": 10,
                "RetryLimit": 1,
                "ProcessingNodeNames": [
                  "IBE-08",
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07"
                ],
                "ShouldScanDefinedRoutes": false
              },
              "BlueAirRobotsSettings": {
                "TorPercentage": 60,
                "RegularProxiesPercentage": 40,
                "LuminatiProxyPercentage": 0,
                "ShouldScanEntireConnectionNetwork": true,
                "IsActive": true,
                "NumberOfProcessingThreads": 5,
                "NumberOfMonthsToGet": 6,
                "CalendarPriceDifferencePerLeg": "3",
                "UseAllTimelineRouteTypes": true,
                "NumberOfAdultsToSearch": 3,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "AmadeusRobotsSettings": {
                "RobotProviderCode": 35,
                "MaxRequestsPerDay": 100000,
                "UseLiveRuntime": true,
                "ShouldScanDefinedRoutes": true,
                "NumberOfProcessingThreads": 2,
                "IsActive": true,
                "ProcessingNodeNames": [
                  "IBE-0"
                ]
              },
              "AzulNavitareSettings": {
                "MaxRequestsPerDay": 10000,
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ],
                "CurrencyCode": "",
                "ShouldScanDefinedRoutes": false,
                "UseLiveRuntime": false
              },
              "MdsChartersRobotsSettings": {
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "MaxRequestsPerDay": 4800,
                "UseLiveRuntime": true,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "TamAmadeusSettings": {
                "NumberOfProcessingThreads": 0,
                "ProcessingNodeNames": [],
                "IsActive": false,
                "CurrencyCode": "",
                "UseLiveRuntime": false,
                "MaxRequestsPerDay": 0
              },
              "VivaAirPeRobotsSettings": {
                "ShouldScanEntireConnectionNetwork": true,
                "NumberOfProcessingThreads": 2,
                "IsActive": true,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "VivaAirCoRobotsSettings": {
                "ShouldScanEntireConnectionNetwork": true,
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "GdxTravelRobotsSettings": {
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "ProcessingNodeNames": [
                  "IBE-03",
                  "IBE-01",
                  "IBE-02",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ],
                "AirlineCodes": [
                  "H2",
                  "OB.",
                  "OB",
                  "7P",
                  "4O",
                  "VE",
                  "8J",
                  "7M",
                  "9R",
                  "VB",
                  "Y4",
                  "1D"
                ]
              },
              "VuelingRobotsSettings": {
                "NumberOfProcessingThreads": 2,
                "ShouldScanEntireConnectionNetwork": true,
                "NumberOfMonthsToGet": 6,
                "FilterFlightsByCalendar": true,
                "IsActive": true,
                "RetryLimit": 1,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "NorwegianRobotsSettings": {
                "IsActive": true,
                "NumberOfProcessingThreads": 2,
                "ShouldScanEntireConnectionNetwork": true,
                "NumberOfMonthsToGet": 9,
                "ProcessingNodeNames": [
                  "IBE-07",
                  "IBE-08",
                  "IBE-06",
                  "IBE-03",
                  "IBE-01",
                  "IBE-02",
                  "IBE-04",
                  "IBE-05"
                ]
              },
              "TransaviaRobotsSettings": {
                "IsActive": true,
                "NumberOfMonthsToGet": 6,
                "NumberOfProcessingThreads": 2,
                "ShouldScanEntireConnectionNetwork": true,
                "ProcessingNodeNames": [
                  "IBE-07",
                  "IBE-08",
                  "IBE-03",
                  "IBE-06"
                ]
              },
              "WowAirRobotsSettings": {
                "NumberOfProcessingThreads": 2,
                "NumberOfMonthsToGet": 9,
                "RobotMargin": 10,
                "RetryLimit": 1,
                "ProcessingNodeNames": [
                  "IBE-06",
                  "IBE-07",
                  "IBE-08",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-01",
                  "IBE-02"
                ],
                "IsActive": false
              },
              "MomondoRobotsSettings": {
                "NumberOfProcessingThreads": 1,
                "IsActive": true,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02"
                ]
              },
              "KiwiRobotsSettings": {
                "IsActive": true,
                "NumberOfProcessingThreads": 5,
                "RobotProviderCode": 64,
                "ProcessingNodeNames": [
                  "IBE-02",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ],
                "HPAirlineCodes": [
                  "NK",
                  "VY",
                  "VV",
                  "VH",
                  "JA",
                  "G4",
                  "U2",
                  "EW",
                  "V7"
                ],
                "AirlineCodes": [
                  "XQ",
                  "FZ",
                  "LV",
                  "DD",
                  "HV",
                  "TO",
                  "GQ",
                  "FO",
                  "JT",
                  "LM",
                  "DP",
                  "XY",
                  "LS",
                  "FD",
                  "FA",
                  "JE",
                  "IX",
                  "G9",
                  "JQ",
                  "DX",
                  "EI",
                  "VJ",
                  "G8",
                  "8Q",
                  "WN"
                ],
                "NumberOfMonthsToGet": 12
              },
              "SkyscannerRobotsSettings": {
                "IsActive": true,
                "NumberOfProcessingThreads": 1,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "LcPeruRobotsSettings": {
                "IsActive": true,
                "ShouldScanEntireConnectionNetwork": true,
                "NumberOfProcessingThreads": 6,
                "FareCodePostfix": "LIM6C28BD",
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "StarPeruRobotsSettings": {
                "IsActive": true,
                "ShouldScanEntireConnectionNetwork": true,
                "NumberOfProcessingThreads": 6,
                "ProcessingNodeNames": [
                  "IBE-01",
                  "IBE-02",
                  "IBE-03",
                  "IBE-04",
                  "IBE-05",
                  "IBE-06",
                  "IBE-07",
                  "IBE-08"
                ]
              },
              "SearchRobotsSettings": {
                "ProcessingNodeNames": [
                  "IBE-34",
                  "IBE-35",
                  "IBE-0"
                ],
                "AirlineCodes": [],
                "MaxNumberOfRoutes": 0,
                "NumberOfProcessingThreads": 2,
                "ProviderCodes": [],
                "NumberOfHubs": 0,
                "IsActive": true
              },
              "TravelFusionRobotsSettings": {
                "RobotProviderCode": 58,
                "NumberOfProcessingThreads": 6,
                "AirlineCodes": [
                  "3O",
                  "Y4",
                  "VB",
                  "AK",
                  "X3",
                  "XC",
                  "TR",
                  "UO"
                ],
                "HPAirlineCodes": [
                  "W6",
                  "W9",
                  "PC",
                  "F9"
                ],
                "IsActive": true
              }
            },
            "LogResponseFromProvider": true,
            "IsEnable": true,
            "NumberOfDaysForwardWithHigherPriority": 1,
            "NumberOfDaysForward": 1,
            "VolatileOffersToCheckPercent": 1,
            "VolatileOffersToCheckMinCount": 10,
            "DefaultNumberOfProcessingThreads": 12,
            "DefaultProcessingNodeNames": [
              "IBE-02",
              "IBE-06",
              "IBE-07",
              "IBE-08"
            ],
            "NumberOfReturnDaysForEachDepartureDay": 14,
            "SendToCachePackageSize": 0
          },
          "SpecialOccasionsCalendarCurrencyCode": "PLN",
          "SpecialOccasionsFromCacheSettings": {
            "CacheTimeOutInMinutes": 1,
            "NumberOfCheapestFlightsPerGroup": 70,
            "MaxDepartureDateInMonths": 12,
            "MinDepartureDateInDays": 1,
            "MaxReturnDepartureDateInMonths": 12,
            "TakeOnlyBasePrice": false,
            "MaxDaysForReturn": 30,
            "MaxDepartureDateInMonthsForPriceAlert": 12,
            "UseMixedFlightsInCalendars": true,
            "ReadCalendarsFromMongoDB": true,
            "RangeInDaysForViOffersGeneratedByWorkerService": 0,
            "NumberOfVIFlightsPerRoute": 10,
            "ApplyDiscountsInCalendars": true,
            "StoreFlightSegmentHashesInMongo": true,
            "ProvidersToSelfMixForRoundTripFlights": []
          },
          "SpecialOccasionsFromDataProviderSettings": {
            "SaveFlightsToPromoCache": false,
            "NumberOfFlightsToSaveInPromoCache": 200,
            "MonthsCountToIncludeInCalendarHeader": 6,
            "TakeOnlyBasePrice": false
          },
          "TotalPriceCurrencies": [
            "PLN",
            "EUR",
            "CZK",
            "TRY",
            "HUF",
            "BRL"
          ],
          "VerifierConfiguration": {
            "TripDatesScenariosOneWayTrips": "<TripDatesScenariosOneWayTrips>\n\t<MinDaysToDeparture>1</MinDaysToDeparture>\n\t<MaxDaysToDeparture>31</MaxDaysToDeparture>\n\t<NumberOfDaysToPick>10</NumberOfDaysToPick>\n</TripDatesScenariosOneWayTrips>",
            "TripDatesScenariosRoundTrips": "<TripDatesScenariosRoundTrips>\n\t<MinDaysToDeparture>1</MinDaysToDeparture>\n\t<MaxDaysToDeparture>31</MaxDaysToDeparture>\n\t<NumberOfDaysToPick>10</NumberOfDaysToPick>\n\t<MinDaysToReturn>1</MinDaysToReturn>\n\t<MaxDaysToReturn>14</MaxDaysToReturn>\n\t<ReturnNumberOfDaysToPick>2</ReturnNumberOfDaysToPick>\n</TripDatesScenariosRoundTrips>",
            "IsEnabled": true,
            "PercentOfDestinationsToVerify": 100,
            "CustomRequestServiceAddress": "http://***********/IBE_2.0/RequestsService.asmx"
          },
          "GDSConfiguration": {
            "OfficeSettings": {
              "OfficeId": "TestOfficeId",
              "IsEnabled": true
            }
          },
          "WriteToCache": false
        }
      }

    }
  }
]