#addin "nuget:?package=Cake.Docker&version=1.1.2"

// ARGUMENTS (passed to build.ps1)
var target = Argument("target", "Default");
var configuration = Argument("configuration", "Release");
var containerRegistry = Argument("containerRegistry", "eu.gcr.io/esky-ets-flightscontent-pro");
var imageTag = Argument("imageTag", $"local-{DateTime.Now:yyyMMdd-HHmm}");
var nugetSource = Argument("nugetSource", "https://artifactory.eskyspace.com/api/nuget/nuget-local/");
var nugetApiKey = Argument("nugetApiKey", "a67c4b2b-8fe6-4fff-8ca4-fee6d58a54e8");
var nugetVersionSuffix = Argument<string>("versionSuffix", null);


// PREPARATION
var sln = File("Esky.FlightsCache.Robots.sln");


(string project, string dockerProject)[] definedProjects = new []{
    ("RobotsProducers", "RobotsProducers"),
    ("RobotsConsumers", "RobotsConsumers")
};


public void DockerBuildMyProject(string project, string dockerProject)
{
    string projectFolder = $"Esky.FlightsCache.{project}";
    string dockerProjectFolder = $"Esky.FlightsCache.{dockerProject}";

    var dockerBuildSettings = new DockerImageBuildSettings
    {
        Tag = new[]{ GetFullImagePath(dockerProject) }
    };

    DotNetCorePublish($"./source/{projectFolder}/{projectFolder}.csproj",  
        new DotNetCorePublishSettings 
        {
            Configuration=configuration,
            OutputDirectory=$"./source/{projectFolder}/obj/Docker/publish"
        }
    );         

    DockerBuild(dockerBuildSettings, $"./source/{dockerProjectFolder}");
}

public void DockerPushMyProject(string project)
{
    var dockerPushSettings = new DockerImagePushSettings
    {
    };

    var path = GetFullImagePath(project);      
    DockerPush(dockerPushSettings, path);
}

public string GetFullImagePath(string project)
{
    return $"{containerRegistry}/esky.flightscache.{project.ToLower()}:{imageTag}";
}

public static void ExecuteInParallel<T>(
    IEnumerable<T> source,
    Action<T> action,
    int maxDegreeOfParallelism = -1,
    System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken)) 
{
    var options = new ParallelOptions {
        MaxDegreeOfParallelism = maxDegreeOfParallelism,
        CancellationToken = cancellationToken
    };

    Parallel.Invoke(options, source.Select(x => (Action)(() => action(x))).ToArray());
}


public static void ExecuteSequencially<T>(
    IEnumerable<T> source,
    Action<T> action) 
{
    foreach (var item in source)
    {
        action(item);
    }   
}

// TASKS

Task("DotnetBuildSolution")
    .Does(() =>
    {
        DotNetCorePublish(            
            $"./Esky.FlightsCache.Robots.sln",
            new DotNetCorePublishSettings {
                Configuration=configuration
            });
    });


Task("Assemble")
    //.IsDependentOn("DotnetRestore")
    .IsDependentOn("DotnetBuildSolution")
    .IsDependentOn("DotnetTest")  
    .Does(() => {
        ExecuteSequencially(definedProjects, p => DockerBuildMyProject(p.project, p.dockerProject));
    });


Task("DotnetRestore")
    .Does(() => {
        DotNetCoreRestore(sln, new DotNetCoreRestoreSettings()
        {          
        }
        );
    });

Task("DotnetTest")
    .Does(() =>
    {
        DotNetCoreTest(           
            $"./Esky.FlightsCache.Robots.sln",
            new DotNetCoreTestSettings()
            {
                Configuration=configuration,
            });           
    });

Task("Publish")
    .Does(() => {
        ExecuteInParallel(definedProjects, p => DockerPushMyProject(p.dockerProject));
    });

// TASK TARGETS

Task("Default")
    .IsDependentOn("DotnetRestore")
    .IsDependentOn("DotnetBuildSolution")
    .IsDependentOn("DotnetTest");



// EXECUTION

RunTarget(target);

