using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using MassTransit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests;

[CollectionDefinition(CollectionName)]
public class SharedApp : ICollectionFixture<InMemoryApp>
{
    public const string CollectionName = "SharedApp.InMemory";
}

public class InMemoryApp : WebApplicationFactory<RobotsReceiveEndpointConnector>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        var partnerSettingsService = Substitute.For<IPartnerSettingsService>();
        partnerSettingsService
            .GetPartnerSettingsAsync("ADMIN")
            .Returns(new PartnerSettingsModel { SpecialOccasionsSettings = new SpecialOccasionsSettings() });

        builder.ConfigureTestServices(
            services =>
            {
                services.AddMassTransitTestHarness();
                services.RemoveHostedService<RobotsReceiveEndpointConnector>();
                services.Replace(ServiceDescriptor.Singleton(partnerSettingsService));
            }
        );
    }
}