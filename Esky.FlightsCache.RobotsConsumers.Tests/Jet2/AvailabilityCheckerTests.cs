using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsConsumers.Jet2;
using Esky.FlightsCache.RobotsConsumers.Services;

namespace Esky.FlightsCache.RobotsConsumers.Tests.Jet2;

public class AvailabilityCheckerTests
{
    private readonly ISearchService<Jet2QueueElement> _searchServiceMock;
    private readonly AvailabilityChecker _availabilityChecker;

    public AvailabilityCheckerTests()
    {
        _searchServiceMock = Substitute.For<ISearchService<Jet2QueueElement>>();
        _availabilityChecker = new AvailabilityChecker(_searchServiceMock);
    }

    [Theory]
    [InlineData(1, 1)]
    [InlineData(2, 2)]
    [InlineData(3, 3)]
    [InlineData(4, 4)]
    [InlineData(5, 5)]
    [InlineData(6, 6)]
    [InlineData(7, 7)]
    [InlineData(8, null)]
    public async Task GetTotalFaresLeft_ReturnsCorrectThreshold_ForEachAdultsCount(int adults, int? expectedThreshold)
    {
        var segment = new SegmentDto
        {
            OriginAirport = "JFK",
            DestinationAirport = "LAX",
            DepartureLocalTime = DateTime.UtcNow,
            FlightNumber = "123"
        };
        var cancellationToken = CancellationToken.None;

        _searchServiceMock
            .Search(Arg.Is<Jet2QueueElement>(m => m.PaxConfiguration[0] <= adults.ToString()[0]), Arg.Any<string>(), cancellationToken)
            .Returns((null, new SearchResponse { Data = [new FlightDto { Legs = [new LegDto { Segments = [new SegmentDto { FlightNumber = "123" }] }] }] })!);

        var result = await _availabilityChecker.GetTotalFaresLeft(segment, isRoundTrip: false, 8, cancellationToken);

        Assert.Equal(expectedThreshold, result);
    }

    [Fact]
    public async Task GetTotalFaresLeft_ReturnsDefault_WhenNoFlightsAvailable()
    {
        var segment = new SegmentDto
        {
            OriginAirport = "JFK",
            DestinationAirport = "LAX",
            DepartureLocalTime = DateTime.UtcNow,
            FlightNumber = "123"
        };
        var cancellationToken = CancellationToken.None;

        _searchServiceMock
            .Search(Arg.Any<Jet2QueueElement>(), Arg.Any<string>(), cancellationToken)
            .Returns((null, new SearchResponse { Data = [] })!);

        var result = await _availabilityChecker.GetTotalFaresLeft(segment, isRoundTrip: false, 8, cancellationToken);

        Assert.Equal(1, result);
    }
}