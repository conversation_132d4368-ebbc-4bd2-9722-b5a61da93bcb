using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.RyanairRouteFeeRefreshing;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.RobotsConsumers.Tests.RyanairRouteFeeRefreshing;

public class RyanAirRefreshRouteFeeConsumerStores : IAsyncLifetime
{
    private readonly ISearchFlightsQueryBuilder _queryBuilder = Substitute.For<ISearchFlightsQueryBuilder>();
    private readonly IGenericApiFlightProvider _providerApi = Substitute.For<IGenericApiFlightProvider>();
    private readonly IRyanairFeeStorage _ryanairFeeStorage = Substitute.For<IRyanairFeeStorage>();
    private readonly ITestHarness _testHarness;

    public RyanAirRefreshRouteFeeConsumerStores()
    {
        var serviceProvider = new ServiceCollection()
            .AddSingleton(_queryBuilder)
            .AddSingleton(_providerApi)
            .AddSingleton(_ryanairFeeStorage)
            .AddMassTransitTestHarness(cfg => cfg.AddConsumer<RyanairRefreshRouteFeeConsumer>())
            .BuildServiceProvider();

        _testHarness = serviceProvider.GetTestHarness();
    }

    public Task InitializeAsync() => _testHarness.Start();
    public Task DisposeAsync() => _testHarness.Stop();

    [Theory]
    [InlineData(26.32, 10.00, 13.32, 120, "PLN", "GBP", 10, 13)]
    [InlineData(435.85, 36.00, 435.85, 120, "PLN", "GBP", 36, 0)]
    [InlineData(435.85, 36.00, 435.85, 120, "PLN", "PLN", 36, 0)]
    public async Task CalculatedFees(
        decimal adultChildInfant_AdultBasePrice,
        decimal adultChildInfant_AdultMandatoryService,
        decimal adultChildInfant_ChildPrice,
        decimal adultChildInfant_InfantPrice,
        string outboundCurrency,
        string inboundCurrency,
        decimal expectedMandatorySeatFee,
        decimal expectedChildDiscount)
    {
        // Arrange
        _providerApi
            .SearchAsync(Arg.Any<ProviderQuery>(), default)
            .ReturnsForAnyArgs(
                CreateAdultChildInfantResponse(
                    outboundCurrency,
                    inboundCurrency,
                    (adultChildInfant_AdultBasePrice, adultChildInfant_AdultMandatoryService),
                    adultChildInfant_ChildPrice,
                    adultChildInfant_InfantPrice
                )
            );

        // Act
        await _testHarness.Bus.Publish(new RyanairRefreshRouteFee { Departure = "WMI", Arrival = "STN" });
        var expectedChildAdditionalFee = expectedMandatorySeatFee - expectedChildDiscount;

        // Assert
        _testHarness
            .Consumed
            .Select<RyanairRefreshRouteFee>()
            .Single() // should receive single message
            .Context
            .Message
            .Should()
            .BeEquivalentTo(new RyanairRefreshRouteFee { Departure = "WMI", Arrival = "STN" });

        await _ryanairFeeStorage
            .Received(requiredNumberOfCalls: 1)
            .SetFees(
                outboundCurrency,
                new Route(Departure: "WMI", Arrival: "STN"),
                new Fee {
                    InfantFee = adultChildInfant_InfantPrice,
                    ChildAdditionalFee = expectedChildAdditionalFee,
                    MandatorySeatFee = expectedMandatorySeatFee,
                    ChildDiscount = expectedChildDiscount
                }
            );

        await _ryanairFeeStorage
            .Received(requiredNumberOfCalls: 1)
            .SetFees(
                inboundCurrency,
                new Route(Departure: "STN", Arrival: "WMI"),
                new Fee {
                    InfantFee = adultChildInfant_InfantPrice,
                    ChildAdditionalFee = expectedChildAdditionalFee,
                    MandatorySeatFee = expectedMandatorySeatFee,
                    ChildDiscount = expectedChildDiscount
                }
            );
    }

    private static SearchResponse CreateAdultChildInfantResponse(
        string outboundCurrency,
        string inboundCurrency,
        (decimal baseAmount, decimal mandatoryService) adultPrice,
        decimal childPrice,
        decimal infantPrice)
    {
        return new SearchResponse
        {
            Data =
            [
                new() // outbound
                {
                    Currency = outboundCurrency,
                    Prices =
                    [
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = adultPrice.baseAmount
                        },
                        new()
                        {
                            PriceType = PriceType.Tax,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = 11.01M
                        },
                        new()
                        {
                            PriceType = PriceType.MandatoryService,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = adultPrice.mandatoryService
                        },
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.CHD,
                            Amount = childPrice
                        },
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.INF,
                            Amount = infantPrice
                        }
                    ],
                    TripType = TripType.OWO
                },
                new() // inbound
                {
                    Currency = inboundCurrency,
                    Prices =
                    [
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = adultPrice.baseAmount
                        },
                        new()
                        {
                            PriceType = PriceType.Tax,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = 11.01M
                        },
                        new()
                        {
                            PriceType = PriceType.MandatoryService,
                            PassengerType = PassengerTypeEnum.ADT,
                            Amount = adultPrice.mandatoryService
                        },
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.CHD,
                            Amount = childPrice
                        },
                        new()
                        {
                            PriceType = PriceType.Base,
                            PassengerType = PassengerTypeEnum.INF,
                            Amount = infantPrice
                        }
                    ],
                    TripType = TripType.OWI
                }
            ]
        };
    }
}