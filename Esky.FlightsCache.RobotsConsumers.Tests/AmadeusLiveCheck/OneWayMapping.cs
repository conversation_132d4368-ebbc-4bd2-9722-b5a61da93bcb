using Esky.FlightsCache.Contract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.AmadeusLiveCheck;

namespace Esky.FlightsCache.RobotsConsumers.Tests.AmadeusLiveCheck;

public class OneWayMapping
{
    [Fact]
    public void MapsCorrectly()
    {
        var request = CreateRequests();
        request.Should().ContainSingle().Which.Should().BeEquivalentTo(_expectedRequest);
    }

    [Fact]
    public void CreatesSeparateRequestForTourOperatorFares()
    {
        _flights[0].Legs.ForEach(l => l.Segments.ForEach(s => s.FareDetails = new FareDetailsDto { OfferType = OfferType.TourOperator }));
        var request = CreateRequests();
        request.Should().HaveCount(2);
        request[0].Context.TourOperatorFares.Should().BeTrue();
        request[1].Context.TourOperatorFares.Should().BeFalse();
    }

    private List<AmadeusLiveCheckRequest> CreateRequests()
    {
        var request = _flights
            .CreateAmadeusLiveCheckRequests(
                new Robots.PaxConfiguration(1, 0, 1, 1),
                new Dictionary<string, OfficeSettings>
                {
                    ["officeId"] = new()
                    {
                        Url = "https://test.com",
                        CurrencyCode = "PLN",
                        User = "user",
                        Password = "cGFzcw==",
                        OfficeId = "officeId"
                    }
                },
                "officeId",
                string.Empty
            )
            .ToList();
        return request;
    }

    private readonly FlightDto[] _flights =
    {
        new()
        {
            ProviderCode = 35,
            Prices = new List<PriceDto> { new() { Currency = "PLN" } },
            Legs = new List<LegDto>
            {
                new()
                {
                    Segments = new List<SegmentDto>
                    {
                        new()
                        {
                            OriginAirport = "WAW",
                            DestinationAirport = "BER",
                            AirlineCode = "LO",
                            FlightNumber = "1",
                            DepartureLocalTime = new DateTime(2023, 11, 21, 8, 30, 0),
                            ArrivalLocalTime = new DateTime(2023, 11, 21, 8, 30, 0).AddHours(2)
                        },
                        new()
                        {
                            OriginAirport = "BER",
                            DestinationAirport = "LHR",
                            AirlineCode = "FR",
                            FlightNumber = "2",
                            DepartureLocalTime =
                                new DateTime(2023, 11, 21, 8, 30, 0).AddHours(2),
                            ArrivalLocalTime = new DateTime(2023, 11, 21, 8, 30, 0).AddHours(4)
                        }
                    }
                }
            }
        },
        new()
        {
            ProviderCode = 35,
            Prices = new List<PriceDto> { new() { Currency = "PLN" } },
            Legs = new List<LegDto>
            {
                new()
                {
                    Segments = new List<SegmentDto>
                    {
                        new()
                        {
                            OriginAirport = "WAW",
                            DestinationAirport = "LHR",
                            AirlineCode = "LO",
                            FlightNumber = "1",
                            DepartureLocalTime = new DateTime(2023, 11, 21, 10, 30, 0),
                            ArrivalLocalTime = new DateTime(2023, 11, 21, 10, 30, 0).AddHours(2)
                        }
                    }
                }
            }
        }
    };

    private readonly AmadeusLiveCheckRequest _expectedRequest = new()
    {
        Adults = 1,
        Youths = 0,
        Children = 1,
        Infants = 1,
        CurrencyCode = "PLN",
        ServiceClass = ServiceClass.Any,
        Context =
            new AmadeusLiveCheckContext
            {
                RuntimeSettings = new AmadeusRuntimeSettings
                {
                    Url = "https://noded1.test.com",
                    CurrencyCode = "PLN",
                    AgencyCode = "officeId",
                    Password = "pass",
                    User = "user"
                },
                OverrideCurrencyConversion = false,
                UseMajorCabinQualifier = false
            },
        Legs = new AmadeusLiveCheckLeg[]
        {
            new()
            {
                Origin = "WAW",
                Destination = "LHR",
                DepartureDate = new DateTime(2023, 11, 21, 8, 30, 0),
                Options = new AmadeusLiveCheckOption[]
                {
                    new()
                    {
                        Segments = new AmadeusLiveCheckSegment[]
                        {
                            new()
                            {
                                Origin = "WAW",
                                Destination = "BER",
                                FlightNo = "1",
                                Airline = "LO",
                                DepartureLocal = new DateTime(2023, 11, 21, 8, 30, 0),
                                ArrivalLocal =
                                    new DateTime(2023, 11, 21, 8, 30, 0).AddHours(2)
                            },
                            new()
                            {
                                Origin = "BER",
                                Destination = "LHR",
                                FlightNo = "2",
                                Airline = "FR",
                                DepartureLocal =
                                    new DateTime(2023, 11, 21, 8, 30, 0).AddHours(2),
                                ArrivalLocal =
                                    new DateTime(2023, 11, 21, 8, 30, 0).AddHours(4)
                            }
                        }
                    },
                    new()
                    {
                        Segments = new AmadeusLiveCheckSegment[]
                        {
                            new()
                            {
                                Origin = "WAW",
                                Destination = "LHR",
                                FlightNo = "1",
                                Airline = "LO",
                                DepartureLocal = new DateTime(2023, 11, 21, 10, 30, 0),
                                ArrivalLocal =
                                    new DateTime(2023, 11, 21, 10, 30, 0).AddHours(2)
                            }
                        }
                    }
                }
            }
        }
    };
}