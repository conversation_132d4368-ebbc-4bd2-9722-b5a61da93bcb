using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Services;

namespace Esky.FlightsCache.RobotsConsumers.Tests;

public static class AssertionHelpers
{
    public static void AssertEmpty(
        this CacheRequest cacheRequest,
        string departure,
        string arrival)
    {
        cacheRequest.SourceDescription.SearchDepartureCode.Should().Be(departure);
        cacheRequest.SourceDescription.SearchArrivalCode.Should().Be(arrival);
        cacheRequest.Flights.Should().BeEmpty();
    }

    public static void AssertSingleOwFlight(
        this CacheRequest cacheRequest,
        string departure,
        string arrival,
        string currency,
        SeparationOptionEnum separationOption)
    {
        cacheRequest.SourceDescription.SearchDepartureCode.Should().Be(departure);
        cacheRequest.SourceDescription.SearchArrivalCode.Should().Be(arrival);
        var outboundLeg = cacheRequest.Flights.Single().Legs.Single();
        outboundLeg.SeparationOptions.Options.Should().Be(separationOption);
        outboundLeg.DepartureCode.Should().Be(departure);
        outboundLeg.ArrivalCode.Should().Be(arrival);
        outboundLeg.CurrencyCode.Should().Be(currency);
    }

    public static void AssertSingleRtFlight(
        this CacheRequest cacheRequest,
        string departure,
        string arrival,
        string currency,
        SeparationOptionEnum separationOption)
    {
        cacheRequest.SourceDescription.SearchDepartureCode.Should().Be(departure);
        cacheRequest.SourceDescription.SearchArrivalCode.Should().Be(arrival);
        var flight = cacheRequest.Flights.Should().ContainSingle().Which;
        flight.Legs.Should().HaveCount(2);

        var outboundLeg = flight.Legs[0];
        outboundLeg.SeparationOptions.Options.Should().Be(separationOption);
        outboundLeg.DepartureCode.Should().Be(departure);
        outboundLeg.ArrivalCode.Should().Be(arrival);
        outboundLeg.CurrencyCode.Should().Be(currency);

        var inboundLeg = flight.Legs[1];
        inboundLeg.SeparationOptions.Options.Should().Be(separationOption);
        inboundLeg.DepartureCode.Should().Be(arrival);
        inboundLeg.ArrivalCode.Should().Be(departure);
        inboundLeg.CurrencyCode.Should().Be(currency);
    }

    public static async Task AssertNoRoundTripRequests(this IGenericApiFlightProvider flightProvider)
    {
        await flightProvider
            .DidNotReceive()
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Count() == 2), Arg.Any<CancellationToken>());
    }

    public static async Task AssertNoOneWayRequests(this IGenericApiFlightProvider flightProvider)
    {
        await flightProvider
            .DidNotReceive()
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Count() == 1), Arg.Any<CancellationToken>());
    }
}