using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Robots;
using Esky.FlightsCache.RobotsConsumers.Configuration;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests.SearchStrategies;

[Collection(SharedApp.CollectionName)]
public class IncrementalAdultSeatsStrategyTests
{
    private const decimal _bp = Flights._bp;
    private const decimal _tp = Flights._tp;
    private const int _maxSeatsCountToCheck = 3;

    private readonly IGenericApiFlightProvider _flightProvider;
    private readonly IStrategyFactory _strategyFactory;
    private readonly ISearchService<QueueElement> _searchService;
    private readonly DirectRyanairConfiguration _ryanConfig;

    public IncrementalAdultSeatsStrategyTests(InMemoryApp factory)
    {
        _flightProvider = Substitute.For<IGenericApiFlightProvider>();

        var app = factory
            .WithWebHostBuilder(
                b => b.ConfigureTestServices(
                    services =>
                    {
                        services.Configure<DirectRyanairConfiguration>(e => e.MaxSeatsCountToCheck = _maxSeatsCountToCheck);
                        services.Replace(ServiceDescriptor.Singleton(_flightProvider));
                    }
                )
            );

        var serviceProvider = app.Services.CreateScope().ServiceProvider;
        _strategyFactory = serviceProvider.GetRequiredService<IStrategyFactory>();
        _searchService = serviceProvider.GetRequiredService<ISearchService<QueueElement>>();
        _ryanConfig = serviceProvider.GetRequiredService<DirectRyanairConfiguration>();
    }

    [Theory]
    [InlineData(1)]
    [InlineData(2)]
    [InlineData(3)]
    public async Task When_NullSeatsRemaining_Then_NoMoreRequests(int adultsWithNullSeatsRemaining)
    {
        for (var i = 1; i <= adultsWithNullSeatsRemaining; i++)
        {
            var adults = i;
            _flightProvider
                .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is(new PaxConfiguration(adults, 0, 0, 0))), Arg.Any<CancellationToken>())
                .Returns(Response(Flight().SeatsRemaining(adults == adultsWithNullSeatsRemaining ? null : adults)));
        }

        var cacheRequests = await PerformSearch();

        cacheRequests.AssertCacheRequestPrices(outbound: [[new PriceCacheEntry { BasePrice = _bp, TaxPrice = _tp, MinimumNumberOfPaxes = 1 }]]);
        await _flightProvider.AssertSearchCalls(
            Enumerable
                .Range(1, adultsWithNullSeatsRemaining)
                .Select(adults => new PaxConfiguration(adult: adults))
                .ToArray()
        );
    }

    [Fact]
    public async Task When_SeatsRemaining_Equals_PaxCount_Continue()
    {
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().SeatsRemaining(1), Flight().Reverse().SeatsRemaining(1)));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight(), Flight().Reverse().SeatsRemaining(2)));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().SeatsRemaining(3), Flight().Reverse()));

        var cacheRequests = await PerformSearch();

        cacheRequests.AssertCacheRequestPrices(
            outbound: [[new PriceCacheEntry { BasePrice = _bp, TaxPrice = _tp, MinimumNumberOfPaxes = 1 }]],
            inbound: [[new PriceCacheEntry { BasePrice = _bp, TaxPrice = _tp, MinimumNumberOfPaxes = 1 }]]
        );
        await _flightProvider.AssertSearchCalls("*******", "*******", "*******");
    }

    [Fact]
    public async Task When_SeatsRemaining_GreaterOrEqual_PaxCount_Then_SkipRequest()
    {
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().SeatsRemaining(2), Flight().Reverse().SeatsRemaining(2))); // should skip ******* request
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response());

        var cacheRequests = await PerformSearch();

        cacheRequests.AssertCacheRequestPrices(
            outbound: [[new PriceCacheEntry { BasePrice = _bp, TaxPrice = _tp, MinimumNumberOfPaxes = 1 }]],
            inbound: [[new PriceCacheEntry { BasePrice = _bp, TaxPrice = _tp, MinimumNumberOfPaxes = 1 }]]
        );
        await _flightProvider.AssertSearchCalls("*******", "*******");
    }

    [Fact]
    public async Task Merge_Prices_From_DifferentPaxConfigurations()
    {
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().Price(100).SeatsRemaining(1), Flight().Price(106).SeatsRemaining(2).ShiftHours(1), Flight().Price(110).Reverse().SeatsRemaining(1)));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().Price(200), Flight().Price(109).ShiftHours(1), Flight().Price(220).Reverse().SeatsRemaining(2)));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().Price(300).SeatsRemaining(10), Flight().Price(230).Reverse()));

        var cacheRequests = await PerformSearch();

        cacheRequests.AssertCacheRequestPrices(
            outbound:
            [
                [
                    new PriceCacheEntry { BasePrice = 100, TaxPrice = 100, MinimumNumberOfPaxes = 1 },
                    new PriceCacheEntry { BasePrice = 200, TaxPrice = 200, MinimumNumberOfPaxes = 2 },
                    new PriceCacheEntry { BasePrice = 300, TaxPrice = 300, MinimumNumberOfPaxes = 3 }
                ],
                [
                    new PriceCacheEntry { BasePrice = 106, TaxPrice = 106, MinimumNumberOfPaxes = 1 },
                    new PriceCacheEntry { BasePrice = 109, TaxPrice = 109, MinimumNumberOfPaxes = 2 }
                ]
            ],
            inbound:
            [
                [
                    new PriceCacheEntry { BasePrice = 110, TaxPrice = 110, MinimumNumberOfPaxes = 1 },
                    new PriceCacheEntry { BasePrice = 220, TaxPrice = 220, MinimumNumberOfPaxes = 2 },
                    new PriceCacheEntry { BasePrice = 230, TaxPrice = 230, MinimumNumberOfPaxes = 3 }
                ]
            ]);
        await _flightProvider.AssertSearchCalls("*******", "*******", "*******");
    }

    [Fact]
    public async Task When_TotalFaresLeft_LessThanOrEqual_PaxCount_Then_NoMoreRequests()
    {
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is("*******")), Arg.Any<CancellationToken>())
            .Returns(Response(Flight().Price(100), Flight().Price(110).Reverse().SeatsRemaining(1).TotalFaresLeft(1)));

        var cacheRequests = await PerformSearch();
        
        cacheRequests.AssertCacheRequestPrices(
            outbound: [[new PriceCacheEntry { BasePrice = 100, TaxPrice = 100, MinimumNumberOfPaxes = 1 }]],
            inbound: [[new PriceCacheEntry { BasePrice = 110, TaxPrice = 110, MinimumNumberOfPaxes = 1 }]]
        );
        await _flightProvider.AssertSearchCalls("*******");
    }

    private Task<IReadOnlyCollection<CacheRequest>> PerformSearch()
    {
        var strategy = _strategyFactory.IncrementalAdultSeats<RyanAirDirectQueueElement>(
            _searchService,
            new Context(_ryanConfig.InitialSeatsCountToCheck, _ryanConfig.MaxSeatsCountToCheck)
        );

        return strategy.Execute(
            new RyanAirDirectQueueElement
            {
                PartnerCode = "ADMIN",
                DepartureCode = "KTW",
                ArrivalCode = "STN",
                DepartureDay = DateTime.UtcNow,
                ReturnDepartureDay = DateTime.UtcNow,
                ProviderCode = ProviderCodeEnum.DirectRyanair,
                IsRoundTrip = true
            },
            CancellationToken.None
        );
    }

    private static SearchResponse Response(params FlightDto[] flights) => Flights.Response(flights);
    private static FlightDto Flight() => Flights.Ob();
}

static file class Extensions
{
    public static void AssertCacheRequestPrices(this IEnumerable<CacheRequest> cacheRequests, PriceCacheEntry[][] outbound, PriceCacheEntry[][]? inbound = null)
    {
        var requests = cacheRequests as IReadOnlyCollection<CacheRequest> ?? cacheRequests.ToArray();

        requests.Should().HaveCount(2, "2xOW - both directions");

        var outboundFlights = requests
            .Single(e => e.SourceDescription.SearchDepartureCode == "KTW")
            .Flights;

        if (outboundFlights.Count != outbound.Length)
        {
            Assert.Fail("Outbound flights count != expected prices count");
        }

        for (var i = 0; i < outboundFlights.Count; i++)
        {
            outboundFlights[i].Legs.Should().ContainSingle().Which.AdultPrices.Should().BeEquivalentTo(outbound[i]);
        }
        
        var inboundFlights = requests
            .Single(e => e.SourceDescription.SearchDepartureCode == "STN")
            .Flights;

        if (inbound is null)
        {
            inboundFlights.Should().BeEmpty();
        }
        else
        {
            if (inboundFlights.Count != inbound.Length)
            {
                Assert.Fail("Inbound flights count != expected prices count");
            }

            for (var i = 0; i < inboundFlights.Count; i++)
            {
                inboundFlights[i].Legs.Should().ContainSingle().Which.AdultPrices.Should().BeEquivalentTo(inbound[i]);
            }
        }
    }

    public static async Task AssertSearchCalls(this IGenericApiFlightProvider flightProvider, params PaxConfiguration[] paxConfigurations)
    {
        foreach (var paxConfiguration in paxConfigurations)
        {
            await flightProvider.Received(1).SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Passengers.Is(paxConfiguration)), Arg.Any<CancellationToken>());
        }

        // ensure no other calls
        await flightProvider.ReceivedWithAnyArgs(paxConfigurations.Length).SearchAsync(Arg.Any<ProviderQuery>(), Arg.Any<CancellationToken>());
    }
}