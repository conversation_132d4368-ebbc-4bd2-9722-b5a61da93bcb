using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests.SearchStrategies;

[Collection(SharedApp.CollectionName)]
public class DiffCurrencyOneWayBothCurrenciesStrategyTests
{
    private readonly IAirportCurrencyService _airportCurrencyService = Substitute.For<IAirportCurrencyService>();
    private readonly ISearchStrategy<SSCQueueElement> _baseStrategy = Substitute.For<ISearchStrategy<SSCQueueElement>>();
    private readonly ISearchStrategy<SSCQueueElement> _sut;
    private readonly IExtension _extension = Substitute.For<IExtension>();

    public DiffCurrencyOneWayBothCurrenciesStrategyTests(InMemoryApp factory)
    {
        _sut = factory
            .WithWebHostBuilder(
                b => b.ConfigureTestServices(
                    services =>
                    {
                        services.Replace(ServiceDescriptor.Singleton(_airportCurrencyService));
                    }
                )
            )
            .Services.CreateScope()
            .ServiceProvider.GetRequiredService<IStrategyFactory>()
            .DiffCurrencyOneWayBothCurrenciesStrategy(_baseStrategy, _extension);
    }

    [Fact]
    public async Task OW_OneRequestOnSameCurrency_WithNullCurrency()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(true);
        _baseStrategy
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>())
            .Returns([new CacheRequest { Flights = [new FlightCache()] }]);

        // act
        await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, null, "easyjet", 1), default);

        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(1).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.Received(1).OnSameCurrency(Arg.Any<IReadOnlyCollection<CacheRequest>>());
        _extension.DidNotReceiveWithAnyArgs().OnDifferentCurrency([]);
    }

    [Fact]
    public async Task RT_OneRequest_WithNullCurrency()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(false);
        _baseStrategy
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>())
            .Returns([new CacheRequest { Flights = [new FlightCache()] }]);

        // act
        await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, DateTime.Today, "easyjet", 1), default);

        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(1).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.DidNotReceiveWithAnyArgs().OnSameCurrency([]);
        _extension.DidNotReceiveWithAnyArgs().OnDifferentCurrency([]);
    }

    [Fact]
    public async Task OW_TwoRequestsOnCurrencyDiff_OneWithNullCurrency_OneWithArrivalCurrency()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(false);
        _airportCurrencyService.Get("LHR", ProviderCodeEnum.SSCProvider.ToString(), Arg.Any<CancellationToken>()).Returns("GBP");
        _baseStrategy
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>())
            .Returns([new CacheRequest { Flights = [new FlightCache()] }]);
        _baseStrategy
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == "GBP"), Arg.Any<CancellationToken>())
            .Returns([new CacheRequest { Flights = [] }]);

        // act
        var cacheRequests = await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, null, "easyjet", 1), default);

        cacheRequests.Should().HaveCount(2);
        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == "GBP"), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(2).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.DidNotReceiveWithAnyArgs().OnSameCurrency([]);
        _extension.Received(1).OnDifferentCurrency(Arg.Any<IReadOnlyCollection<CacheRequest>>());
    }

    [Fact]
    public async Task OW_OneRequestOnCurrencyDiff_WhenNoFlights()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(false);
        _baseStrategy
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>())
            .Returns([new CacheRequest { Flights = [] }]);

        // act
        var cacheRequests = await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, null, "easyjet", 1), default);

        cacheRequests.Should().ContainSingle();
        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(1).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.DidNotReceiveWithAnyArgs().OnSameCurrency([]);
        _extension.DidNotReceiveWithAnyArgs().OnDifferentCurrency([]);
    }
}