using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.SearchStrategies;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests.SearchStrategies;

[Collection(SharedApp.CollectionName)]
public class RoundTripAsTwoOneWayWithDepartureAirportCurrencyTests
{
    private readonly IAirportCurrencyService _airportCurrencyService = Substitute.For<IAirportCurrencyService>();
    private readonly ISearchStrategy<SSCQueueElement> _baseStrategy = Substitute.For<ISearchStrategy<SSCQueueElement>>();
    private readonly ISearchStrategy<SSCQueueElement> _sut;
    private readonly IExtension _extension = Substitute.For<IExtension>();

    public RoundTripAsTwoOneWayWithDepartureAirportCurrencyTests(InMemoryApp factory)
    {
        _sut = factory
            .WithWebHostBuilder(
                b => b.ConfigureTestServices(
                    services =>
                    {
                        services.Replace(ServiceDescriptor.Singleton(_airportCurrencyService));
                    }
                )
            )
            .Services.CreateScope()
            .ServiceProvider.GetRequiredService<IStrategyFactory>()
            .RoundTripAsTwoOneWayWithDepartureAirportCurrency(_baseStrategy, _extension);
    }

    [Fact]
    public async Task OneRequestForOneWay_WithNullCurrency()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(true);
        // act
        await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, null, "easyjet", 1), default);

        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(1).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.Received(1).OnSameCurrency(Arg.Any<IReadOnlyCollection<CacheRequest>>());
        _extension.DidNotReceiveWithAnyArgs().OnDifferentCurrency([]);
    }

    [Fact]
    public async Task OneRequestForRoundTrip_SameCurrency()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(true);

        // act
        await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, DateTime.Today, "easyjet", 1), default);

        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(1).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.Received(1).OnSameCurrency(Arg.Any<IReadOnlyCollection<CacheRequest>>());
        _extension.DidNotReceiveWithAnyArgs().OnDifferentCurrency([]);
    }

    [Fact]
    public async Task TwoRequestsForRoundTrip_WithInboundCurrencyFromDepartureAirport()
    {
        _airportCurrencyService.SameCurrency("KTW", "LHR", ProviderCodeEnum.SSCProvider.ToString()).Returns(false);
        _airportCurrencyService.Get("KTW", ProviderCodeEnum.SSCProvider.ToString(), Arg.Any<CancellationToken>()).Returns("PLN");

        // act
        await _sut.Execute(SSCQueueElement.Create("KTW", "LHR", DateTime.Today, DateTime.Today, "easyjet", 1), default);

        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "KTW" && e.Currency == null), Arg.Any<CancellationToken>());
        await _baseStrategy
            .Received(1)
            .Execute(Arg.Is<SSCQueueElement>(e => e.DepartureCode == "LHR" && e.Currency == "PLN"), Arg.Any<CancellationToken>());
        await _baseStrategy.Received(2).Execute(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
        _extension.DidNotReceiveWithAnyArgs().OnSameCurrency([]);
        _extension.Received(1).OnDifferentCurrency(Arg.Any<IReadOnlyCollection<CacheRequest>>());
    }
}
