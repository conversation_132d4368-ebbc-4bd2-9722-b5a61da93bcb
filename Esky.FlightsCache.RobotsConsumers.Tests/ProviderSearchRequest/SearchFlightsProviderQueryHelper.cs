using Esky.Flights.Integration.Providers.Contract;

namespace Esky.FlightsCache.RobotsConsumers.Tests.ProviderSearchRequest;

public static class SearchFlightsProviderQueryHelper
{
    public static int GetPassengerCount(this SearchFlightsProviderQuery query, PassengerTypeEnum passengerType)
    {
        var passenger = query.Passengers.SingleOrDefault(x => x.Code == passengerType);
        return passenger?.Count ?? 0;
    }
}
