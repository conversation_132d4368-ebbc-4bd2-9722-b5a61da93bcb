using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.ProviderSearchRequest;
using Esky.FlightsCache.RobotsProducers.Messages;

namespace Esky.FlightsCache.RobotsConsumers.Tests.ProviderSearchRequest;

public class SearchFlightsQueryBuilderTests
{
    private readonly ISearchFlightsQueryBuilder _sut = new SearchFlightsQueryBuilder();
    private readonly DateTime _today = DateTime.Today;

    private static QueueElement ArrangeQueueElement()
        => new()
        {
            PartnerCode = "ESKY"
        };

    [Fact]
    public void Build_WhenPaxConfigurationNotSet_ThenQueryForTwoAdults()
    {
        // Arrange
        var message = ArrangeQueueElement();
        message.PaxConfiguration = null;

        // Act
        var request = _sut.BuildSingle(message);

        // Assert
        request.GetPassengerCount(PassengerTypeEnum.ADT).Should().Be(2);
        request.GetPassengerCount(PassengerTypeEnum.YTH).Should().Be(0);
        request.GetPassengerCount(PassengerTypeEnum.CHD).Should().Be(0);
        request.GetPassengerCount(PassengerTypeEnum.INF).Should().Be(0);
    }

    [Fact]
    public void Build_WhenPaxConfigurationSet_ThenQueryForRequestedConfiguration()
    {
        // Arrange
        var message = ArrangeQueueElement();
        message.PaxConfiguration = "1.2.3.4";

        // Act
        var request = _sut.BuildSingle(message);

        // Assert
        request.GetPassengerCount(PassengerTypeEnum.ADT).Should().Be(1);
        request.GetPassengerCount(PassengerTypeEnum.YTH).Should().Be(2);
        request.GetPassengerCount(PassengerTypeEnum.CHD).Should().Be(3);
        request.GetPassengerCount(PassengerTypeEnum.INF).Should().Be(4);
    }

    [Fact]
    public void BuildAll_WhenOneWay_ThenSingleOneWayQuery()
    {
        // Arrange
        var message = new QueueElement
        {
            PartnerCode = "ESKY",
            IsRoundTrip = false,
            DepartureCode = "KTW",
            ArrivalCode = "LON",
            DepartureDay = _today
        };

        var expectedQuery = new SearchFlightsProviderQuery
        {
            PartnerCode = "ESKY",
            Legs = [new() {DepartureCode = "KTW", ArrivalCode = "LON", DepartureDate = _today}],
            Passengers = GetDefaultPassengers()
        };

        // Act
        var queries = _sut.BuildAll(message).ToArray();

        // Assert
        queries.Should().HaveCount(1);
        queries.First().Should().BeEquivalentTo((expectedQuery, true));
    }
    
    [Fact]
    public void BuildAll_WhenNonSeparableRoundTrip_ThenSingleRoundTripQuery()
    {
        // Arrange
        var message = new QueueElement
        {
            PartnerCode = "ESKY",
            DepartureCode = "KTW",
            ArrivalCode = "LON",
            IsRoundTrip = true,
            DepartureDay = _today,
            ReturnDepartureDay = _today.AddDays(1)
        };

        var expectedQuery = new SearchFlightsProviderQuery
        {
            PartnerCode = "ESKY",
            Legs = [
                new() {DepartureCode = "KTW", ArrivalCode = "LON", DepartureDate = _today},
                new() {DepartureCode = "LON", ArrivalCode = "KTW", DepartureDate = _today.AddDays(1)}
            ],
            Passengers = GetDefaultPassengers()
        };
        
        // Act
        var queries = _sut.BuildAll(message).ToArray();

        // Assert
        queries.Should().HaveCount(1);
        queries.First().Should().BeEquivalentTo((expectedQuery, true));
    }
    
    [Fact]
    public void BuildAll_WhenSeparableRoundTrip_ThenMultipleRoundTripQueries()
    {
        // Arrange
        var message = new QueueElement
        {
            PartnerCode = "ESKY",
            DepartureCode = "KTW",
            ArrivalCode = "LON",
            IsRoundTrip = true,
            DepartureDay = _today,
            ReturnDepartureDays = [
                new ReturnDepartureDate(_today.AddDays(1), false),
                new ReturnDepartureDate(_today.AddDays(2), true),
                new ReturnDepartureDate(_today.AddDays(3), false)
            ]
        };

        var expectedQueries = new [] 
        {
            (GetQuery(returnDepartureDate: _today.AddDays(2)), true),
            (GetQuery(returnDepartureDate: _today.AddDays(1)), false),
            (GetQuery(returnDepartureDate: _today.AddDays(3)), false)
        };
        
        // Act
        var queries = _sut.BuildAll(message).ToArray();

        // Assert
        queries.Should().HaveCount(3);
        queries.Should().BeEquivalentTo(expectedQueries, options => options.WithStrictOrdering());

        SearchFlightsProviderQuery GetQuery(DateTime returnDepartureDate) => new()
            {
                PartnerCode = "ESKY",
                Legs = [
                    new() {DepartureCode = "KTW", ArrivalCode = "LON", DepartureDate = _today},
                    new() {DepartureCode = "LON", ArrivalCode = "KTW", DepartureDate = returnDepartureDate}
                ],
                Passengers = GetDefaultPassengers()
            };
    }
    
    private static IEnumerable<SearchFlightsProviderQuery.Passenger> GetDefaultPassengers() =>
        [
            new() { Code = PassengerTypeEnum.ADT, Count = 2 },
            new() { Code = PassengerTypeEnum.YTH, Count = 0 },
            new() { Code = PassengerTypeEnum.CHD, Count = 0 },
            new() { Code = PassengerTypeEnum.INF, Count = 0 }
        ];
}
