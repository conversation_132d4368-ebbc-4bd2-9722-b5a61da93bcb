using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.PartnerSettings;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsProducers.Messages;

namespace Esky.FlightsCache.RobotsConsumers.Tests.Mocks;

internal sealed class MockedFlightsConverter : IFlightCacheConverter
{
    public List<FlightCache> Convert(IEnumerable<FlightDto> flights, QueueElement queueElement, CacheTimeOutConfiguration timeOutConfiguration,
        SearchFlightsProviderQuery query)
    {
        return GetFlights().ToList();

        IEnumerable<FlightCache> GetFlights()
        {
            foreach (var flight in flights)
            {
                yield return new FlightCache
                {
                    ProviderCode = (int)(queueElement.ProviderCode ?? 0),
                    Supplier = flight.Supplier,
                    Legs = flight.Legs.Select((leg, index) =>
                    {
                        return new FlightCacheLeg
                        {
                            ArrivalCode = leg.Segments.Last().DestinationAirport,
                            ArrivalDate = leg.Segments.Last().ArrivalLocalTime,
                            DepartureCode = leg.Segments.First().OriginAirport,
                            DepartureDate = leg.Segments.First().DepartureLocalTime,
                            AirlineCode = leg.Segments.First().AirlineCode,
                            CurrencyCode = flight.Currency,
                            Segments = leg.Segments.Select(s => new FlightCacheSegment
                            {
                                ArrivalCode = s.DestinationAirport,
                                ArrivalDate = s.ArrivalLocalTime,
                                DepartureCode = s.OriginAirport,
                                DepartureDate = s.DepartureLocalTime,
                                AirlineCode = s.AirlineCode,
                                FlightNumber = s.FlightNumber,
                                OperatingAirlineCode = s.OperatedBy,
                                OperatingFlightNumber = s.OperatedByFlightNumber,
                                BookingClass = s.BookingClass,
                                FareDetails = new FareDetails
                                {
                                    FareCode = s.FareDetails?.FareCode,
                                    OfficeId = s.FareDetails?.OfficeId,
                                    OfferId = s.FareDetails?.OfferId,
                                    OfferType =
                                        (MessageContract.OfferType?)s.FareDetails?.OfferType ??
                                        MessageContract.OfferType.Regular
                                },
                                IsBaggageIncludedInPrice = s.Baggage is AvailabilityEnum.Included,
                                AircraftCode = s.AircraftCode,
                                Stopovers = s.Stopovers
                                    ?.Select(stop => new FlightCacheSegmentStopover
                                    {
                                        AirportCode = stop.AirportCode,
                                        DepartureDate = stop.DepartureDate,
                                        ArrivalDate = stop.ArrivalDate,
                                    })
                                    .ToList(),
                            }).ToList(),
                            AdultPrices = GetPrices(flight, index, PassengerTypeEnum.ADT)
                        };
                    }).ToList()
                };
            }
        }

        List<PriceCacheEntry> GetPrices(FlightDto flight, int index, PassengerTypeEnum passengerType)
        {
            var adultPrice = flight.Prices
                ?.Where(p => (p.Segments?.Contains(index) ?? true) && p.PassengerType == passengerType);
            return adultPrice?.Any() == true ? [MapPrice(adultPrice)] : null;
        }
            
        PriceCacheEntry MapPrice(IEnumerable<PriceDto> priceDtos)
        {
            return new PriceCacheEntry
            {
                BasePrice = priceDtos.FirstOrDefault(x => x.PriceType == PriceType.Base)?.Amount ?? 0,
                TaxPrice = priceDtos.FirstOrDefault(x => x.PriceType == PriceType.Tax)?.Amount ?? 0,
                MinimumNumberOfPaxes = 1
            };
        }
    }
}