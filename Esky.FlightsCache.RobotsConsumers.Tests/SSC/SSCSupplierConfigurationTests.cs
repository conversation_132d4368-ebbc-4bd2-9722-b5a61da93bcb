using Esky.FlightsCache.RobotsConsumers.SSC;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.RobotsConsumers.Tests.SSC;

[Collection(SharedApp.CollectionName)]
public class SSCSupplierConfigurationTests
{
    private readonly InMemoryApp _app;

    public SSCSupplierConfigurationTests(InMemoryApp app)
    {
        _app = app;
    }

    [Fact]
    public async Task HasDefinedSuppliers()
    {
        var suppliers = _app
            .Services.CreateScope()
            .ServiceProvider.GetRequiredService<ISSCSupplierConfiguration>();

        var configurations = (await suppliers.GetConfigurations(CancellationToken.None)).ToArray();

        configurations.Should().NotBeNull().And.NotBeEmpty();
        foreach (var configuration in configurations)
        {
            configuration.Name.Should().NotBeNullOrWhiteSpace();
            configuration.InitialSeatsCountToCheck.Should().NotBe(0);
            configuration.MaxSeatsCountToCheck.Should().NotBe(0);
        }
    }

    [Fact]
    public void ConfigHasDefaultSeatCountSet()
    {
        var cfg = new ISSCSupplierConfiguration.Config { Name = "x" };
        cfg.InitialSeatsCountToCheck.Should().Be(2);
        cfg.MaxSeatsCountToCheck.Should().Be(2);
    }
}