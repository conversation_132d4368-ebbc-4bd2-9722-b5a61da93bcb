using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.SSC;
using Esky.FlightsCache.RobotsProducers.Messages;
using MassTransit.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests.SSC;

[Collection(SharedApp.CollectionName)]
public class SSCConsumerTests
{
    private const string _departure = Flights._departureCode;
    private const string _arrival = Flights._arrivalCode;

    private readonly ITestHarness _testHarness;
    private readonly ISSCProvider _flightProvider;

    public SSCConsumerTests(InMemoryApp factory)
    {
        _flightProvider = Substitute.For<ISSCProvider>();
        var app = factory
            .WithWebHostBuilder(b => b.ConfigureTestServices(services => services.Replace(ServiceDescriptor.Singleton(_flightProvider))));

        _testHarness = app.Services.CreateScope().ServiceProvider.GetTestHarness();
    }

    [Fact]
    public async Task OW_NoFlights_Publishes_EmptyCacheRequest_For_Outbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response());

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, null, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().ContainSingle().Which.AssertEmpty(_departure, _arrival);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task OW_Publishes_For_Outbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ow()));

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, null, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().ContainSingle().Which.AssertSingleOwFlight(_departure, _arrival, "EUR", SeparationOptionEnum.OneWay);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsSeparateOW_NoFlights_Publishes_EmptyCacheRequest_For_Outbound_Inbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response());

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().HaveCount(2);
        published[0].AssertEmpty(_departure, _arrival);
        published[1].AssertEmpty(_arrival, _departure);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsSeparateOW_NoOutboundFlights_Publishes_EmptyCacheRequest_For_Outbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ib()));

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().HaveCount(2);
        published[0].AssertEmpty(_departure, _arrival);
        published[1].AssertSingleOwFlight(_arrival, _departure, "EUR", SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsSeparateOW_NoInboundFlights_Publishes_EmptyCacheRequest_For_Inbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob()));

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().HaveCount(2);
        published[0].AssertSingleOwFlight(_departure, _arrival, "EUR", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound);
        published[1].AssertEmpty(_arrival, _departure);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsSeparateOW_Publishes_CacheRequest_For_Outbound_Inbound()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob(), Flights.Ib()));

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "easyjet", 3);
        var published = await Perform(queueElement);

        published.Should().HaveCount(2);
        published[0].AssertSingleOwFlight(_departure, _arrival, "EUR", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound);
        published[1].AssertSingleOwFlight(_arrival, _departure, "EUR", SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsRt_NoFlights_Publishes_EmptyCacheRequest()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response());

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "tui", 3);
        var published = await Perform(queueElement);

        published.Should().ContainSingle().Which.AssertEmpty(_departure, _arrival);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RT_ProviderReturnsRT_Publishes_CacheRequest_For_RT()
    {
        _flightProvider
            .Search(Arg.Is<SSCQueueElement>(e => e.DepartureCode == _departure), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Rt()));

        // act
        var queueElement = SSCQueueElement.Create(_departure, _arrival, Flights._utcNow, Flights._utcNow, "tui", 3);
        var published = await Perform(queueElement);

        published.Should().ContainSingle();
        published[0].AssertSingleRtFlight(_departure, _arrival, "EUR", SeparationOptionEnum.None);

        await _flightProvider.Received(1).Search(Arg.Any<SSCQueueElement>(), Arg.Any<CancellationToken>());
    }

    private async Task<CacheRequest[]> Perform<T>(T queueElement) where T : SSCQueueElement
    {
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();
        return published;
    }
}