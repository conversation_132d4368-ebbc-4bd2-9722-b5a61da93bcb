using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.Framework.PartnerSettings.Enums;

namespace Esky.FlightsCache.RobotsConsumers.Tests.Services;

public class StalenessEvaluatorTests
{
    private const string _supplier1 = "supplier1";
    private const string _supplier2 = "supplier2";
    private const string _supplier3 = "supplier3";
    
    private readonly LegDto _notStaleLeg = GetLeg(minutesOld: 10, daysToDeparture: 5);
    private readonly LegDto _staleLeg = GetLeg(minutesOld: 100, daysToDeparture: 1);
    
    private readonly StalenessEvaluator _sut;

    public StalenessEvaluatorTests()
    {
        _sut = new StalenessEvaluator(new TravelFusionConsumerSettings
        {
            Retry = new TravelFusionConsumerSettings.RetrySettings
            {
                Conditions =
                [
                    new() { DepartureDaysTo = 10, MaxAgeInMinutes = 60, Suppliers = [_supplier1, _supplier2] },
                    new() { DepartureDaysTo = 50, MaxAgeInMinutes = 180, Suppliers = [_supplier1] }
                ],
                PartnerCode = "MASTER"
            }
        });
    }

    [Theory]
    [InlineData(ProviderCodeEnum.Amadeus)]
    [InlineData(ProviderCodeEnum.TravelFusion)]
    [InlineData(ProviderCodeEnum.DirectRyanair)]
    public void WhenEmptyResponse_ThenAlwaysNotStale(ProviderCodeEnum providerCode)
    {
        var isStale = _sut.IsStale(providerCode, new SearchResponse { Data = [] });

        isStale.Should().BeFalse();
    }

    [Theory]
    [InlineData(ProviderCodeEnum.Amadeus, null)]
    [InlineData(ProviderCodeEnum.Amadeus, _supplier1)]
    [InlineData(ProviderCodeEnum.TravelFusion, _supplier3)]
    public void WhenNotAffectedProviderAndSupplier_ThenAlwaysNotStale(ProviderCodeEnum providerCode, string? supplier)
    {
        var searchResponse = new SearchResponse { Data = [new FlightDto
        {
            Supplier = supplier,
            Legs = [_staleLeg]
        }] };
        var isStale = _sut.IsStale(providerCode, searchResponse);
        
        isStale.Should().BeFalse();
    }

    [Fact]
    public void GivenMatchingProviderAndSupplier_WhenDataVeryOld_ThenIsStale()
    {
        var searchResponse =
            new SearchResponse { Data = [new FlightDto { Supplier = _supplier1, Legs = [_staleLeg] }] };
        var isStale = _sut.IsStale(ProviderCodeEnum.TravelFusion, searchResponse);
        
        isStale.Should().BeTrue();
    }
    
    [Fact]
    public void GivenMatchingProviderAndSupplier_WhenDataVeryFresh_ThenNotStale()
    {
        var searchResponse =
            new SearchResponse { Data = [new FlightDto { Supplier = _supplier1, Legs = [_notStaleLeg] }] };
        var isStale = _sut.IsStale(ProviderCodeEnum.TravelFusion, searchResponse);
        
        isStale.Should().BeFalse();
    }
    
    [Fact]
    public void GivenMatchingProviderAndSupplier_WhenOneLegIsStale_ThenIsStale()
    {
        var searchResponse =
            new SearchResponse { Data = [new FlightDto { Supplier = _supplier1, Legs = [_staleLeg, _notStaleLeg] }] };
        var isStale = _sut.IsStale(ProviderCodeEnum.TravelFusion, searchResponse);
        
        isStale.Should().BeTrue();
    }
    
    [Fact]
    public void GivenMatchingProviderAndSupplier_WhenOneFlightHaveStaleLeg_ThenIsStale()
    {
        var searchResponse =
            new SearchResponse { Data = [
                new FlightDto { Supplier = _supplier1, Legs = [_notStaleLeg] },
                new FlightDto { Supplier = _supplier1, Legs = [_staleLeg] }
            ] };
        var isStale = _sut.IsStale(ProviderCodeEnum.TravelFusion, searchResponse);
        
        isStale.Should().BeTrue();
    }

    [Theory]
    [InlineData(_supplier1, 0, 0, false)]
    [InlineData(_supplier2, 10, 5, false)]
    [InlineData(_supplier3, 10, 5, false)]
    
    [InlineData(_supplier1, 70, 5, true)]
    [InlineData(_supplier2, 70, 5, true)]
    [InlineData(_supplier3, 70, 5, false)]
    
    [InlineData(_supplier1, 70, 15, false)]
    [InlineData(_supplier2, 70, 15, false)]
    [InlineData(_supplier3, 70, 15, false)]
    
    [InlineData(_supplier1, 200, 15, true)]
    [InlineData(_supplier2, 200, 15, false)]
    [InlineData(_supplier3, 200, 15, false)]
    
    [InlineData(_supplier1, 200, 60, false)]
    [InlineData(_supplier2, 200, 60, false)]
    [InlineData(_supplier3, 200, 60, false)]
    public void DefinedFlight_IsStaleAsExpected(string supplier, int minutesOld, int daysToDeparture, bool expectedIsStale)
    {
        var searchResponse =
            new SearchResponse { Data = [
                new FlightDto { Supplier = supplier, Legs = [GetLeg(minutesOld, daysToDeparture)] }
            ] };
        var isStale = _sut.IsStale(ProviderCodeEnum.TravelFusion, searchResponse);
        
        isStale.Should().Be(expectedIsStale);
    } 
    
    private static LegDto GetLeg(int minutesOld, int daysToDeparture) => new()
    {
        DataTimestamp = DateTime.UtcNow.AddMinutes(-minutesOld),
        Segments = [new SegmentDto { DepartureLocalTime = DateTime.UtcNow.AddDays(daysToDeparture) }]
    };
}