using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Esky.FlightsCache.RobotsConsumers.Tests;

[Collection(SharedApp.CollectionName)]
public class DirectRyanairConsumerTests
{
    private const string _departure = Flights._departureCode;
    private const string _arrival = Flights._arrivalCode;

    private readonly ITestHarness _testHarness;
    private readonly IAirportCurrencyService _currencyService;
    private readonly IGenericApiFlightProvider _flightProvider;

    public DirectRyanairConsumerTests(InMemoryApp factory)
    {
        _currencyService = Substitute.For<IAirportCurrencyService>();
        _flightProvider = Substitute.For<IGenericApiFlightProvider>();

        var currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
        currencyRatioProvider.GetRatio("GBP", "EUR").Returns(1.1M);
        currencyRatioProvider.GetRatio("PLN", "EUR").Returns(0.25M);

        var app = factory
            .WithWebHostBuilder(
                b => b.ConfigureTestServices(
                    services =>
                    {
                        services.Replace(ServiceDescriptor.Singleton(typeof(IAirportCurrencyService), _currencyService));
                        services.Replace(ServiceDescriptor.Singleton(typeof(IGenericApiFlightProvider), _flightProvider));
                        services.Replace(ServiceDescriptor.Singleton(typeof(ICurrencyRatioProvider), currencyRatioProvider));
                    }
                )
            );

        _testHarness = app.Services.CreateScope().ServiceProvider.GetTestHarness();
    }

    [Fact]
    public async Task OW_SameCurrency_Publishes_CacheRequest_For_Outbound()
    {
        _currencyService.SameCurrency(_departure, _arrival, ProviderCodeEnum.DirectRyanair.ToString()).Returns(true);
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == null), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).Currency("PLN")));

        // act
        var queueElement = Flights.RyanOwTestElement();
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();

        var cacheRequest = published.Should().ContainSingle().Which;
        cacheRequest.AssertSingleOwFlight(_departure, _arrival, "PLN", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound | SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.AssertNoRoundTripRequests();
    }

    [Fact]
    public async Task OW_DifferentCurrency_Publishes_CacheRequest_For_Outbound_Inbound()
    {
        _currencyService.SameCurrency(_departure, _arrival, ProviderCodeEnum.DirectRyanair.ToString()).Returns(false);
        _currencyService.Get(_arrival, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("GBP");
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == null), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).Currency("PLN")));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == "GBP"), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).Currency("GBP")));

        // act
        var queueElement = Flights.RyanOwTestElement();
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();

        published.Should().HaveCount(2);
        published[0].AssertSingleOwFlight(_departure, _arrival, "PLN", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound);
        published[1].AssertSingleOwFlight(_departure, _arrival, "GBP", SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.AssertNoRoundTripRequests();
    }

    [Fact]
    public async Task OW_DifferentCurrency_NoOutbound_Publishes_EmptyCacheRequest_For_Outbound()
    {
        _currencyService.SameCurrency(_departure, _arrival, ProviderCodeEnum.DirectRyanair.ToString()).Returns(false);
        _currencyService.Get(_arrival, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("PLN");
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == null), Arg.Any<CancellationToken>())
            .Returns(Flights.Response());

        // act
        var queueElement = Flights.RyanOwTestElement();
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();

        published.Should().ContainSingle();
        // ob
        published[0].SourceDescription.SearchDepartureCode.Should().Be(_departure);
        published[0].SourceDescription.SearchArrivalCode.Should().Be(_arrival);
        published[0].Flights.Should().BeEmpty();

        await _flightProvider.AssertNoRoundTripRequests();
    }

    [Fact]
    public async Task RT_SameCurrency_Publishes_CacheRequest_For_Outbound_Inbound()
    {
        _currencyService.SameCurrency(_departure, _arrival, ProviderCodeEnum.DirectRyanair.ToString()).Returns(true);
        _currencyService.Get(_departure, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("PLN");
        _currencyService.Get(_arrival, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("PLN");
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == null), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).Currency("PLN")));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _arrival && e.Query.Currency == "PLN"), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).ReverseRoute().Currency("PLN")));

        // act
        var queueElement = Flights.RyanRtTestElement();
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();

        published.Should().ContainSingle().Which.AssertSingleOwFlight(_departure, _arrival, "PLN", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound | SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.ReceivedWithAnyArgs(1).SearchAsync(Arg.Any<ProviderQuery>(), Arg.Any<CancellationToken>());
        await _flightProvider.AssertNoRoundTripRequests();
    }

    [Fact]
    public async Task RT_DifferentCurrency_Publishes_CacheRequest_For_Outbound_Inbound()
    {
        _currencyService.SameCurrency(_departure, _arrival, ProviderCodeEnum.DirectRyanair.ToString()).Returns(false);
        _currencyService.SameCurrency(_arrival, _departure, ProviderCodeEnum.DirectRyanair.ToString()).Returns(false);
        _currencyService.Get(_departure, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("PLN");
        _currencyService.Get(_arrival, ProviderCodeEnum.DirectRyanair.ToString(), Arg.Any<CancellationToken>()).Returns("GBP");
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _departure && e.Query.Currency == null), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).Currency("PLN")));
        _flightProvider
            .SearchAsync(Arg.Is<ProviderQuery>(e => e.Query.Legs.Single().DepartureCode == _arrival && e.Query.Currency == "PLN"), Arg.Any<CancellationToken>())
            .Returns(Flights.Response(Flights.Ob().SeatsRemaining(10).ReverseRoute().Currency("PLN")));

        // act
        var queueElement = Flights.RyanRtTestElement();
        await _testHarness.Bus.Publish(queueElement);

        var published = _testHarness
            .Published
            .Select<CacheRequest>()
            .Select(e => e.Context.Message)
            .ToArray();

        published.Should().HaveCount(2);
        published[0].AssertSingleOwFlight(_departure, _arrival, "PLN", SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound);
        published[1].AssertSingleOwFlight(_arrival, _departure, "PLN", SeparationOptionEnum.RoundTripInbound);

        await _flightProvider.AssertNoRoundTripRequests();
    }
}