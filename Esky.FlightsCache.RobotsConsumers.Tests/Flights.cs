

using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;

namespace Esky.FlightsCache.RobotsConsumers.Tests;

public static class Flights
{
    public const string _departureCode = "KTW";
    public const string _arrivalCode = "STN";
    public const decimal _bp = 100;
    public const decimal _tp = 100;
    public static readonly DateTime _utcNow = DateTime.UtcNow;

    public static RyanAirDirectQueueElement RyanOwTestElement()
    {
        return new RyanAirDirectQueueElement
        {
            PartnerCode = "ADMIN",
            DepartureCode = _departureCode,
            ArrivalCode = _arrivalCode,
            DepartureDay = _utcNow,
            ProviderCode = ProviderCodeEnum.DirectRyanair,
        };
    }

    public static RyanAirDirectQueueElement RyanRtTestElement()
    {
        return new RyanAirDirectQueueElement
        {
            PartnerCode = "ADMIN",
            DepartureCode = _departureCode,
            ArrivalCode = _arrivalCode,
            DepartureDay = _utcNow,
            ReturnDepartureDay = _utcNow,
            ProviderCode = ProviderCodeEnum.DirectRyanair,
            IsRoundTrip = true
        };
    }

    public static SearchResponse Response(params FlightDto[] flights) => new() { Data = flights.ToList() };

    public static FlightDto Ob()
    {
        return new FlightDto
        {
            Legs =
            [
                new LegDto
                {
                    Segments =
                    [
                        new SegmentDto
                        {
                            AirlineCode = "FR",
                            FlightNumber = "1642",
                            OriginAirport = _departureCode,
                            DestinationAirport = _arrivalCode,
                            DepartureLocalTime = _utcNow,
                            ArrivalLocalTime = _utcNow.AddHours(1),
                            DepartureUtc = _utcNow,
                            ArrivalUtc = _utcNow,
                            OperatedBy = "test",
                            AircraftCode = "test",
                            IsSelfTransfer = false,
                            BookingClass = "Y",
                            Duration = 60,
                            SeatsRemaining = null
                        }
                    ],
                    Duration = 60,
                    SeparationOptions = new SeparationOptions { Options = SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound, MinStay = 0 },
                    DataTimestamp = _utcNow.AddHours(-5)
                }
            ],
            TotalAmount = _bp + _tp,
            TaxAmount = _tp,
            PnrCount = 1,
            Baggage = AvailabilityEnum.Surcharge,
            Prices =
            [
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = _bp },
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = _tp }
            ],
            TripType = TripType.OWO,
            Currency = "EUR",
            Supplier = null
        };
    }

    public static FlightDto Rt()
    {
        return new FlightDto
        {
            Legs =
            [
                new LegDto
                {
                    Segments =
                    [
                        new SegmentDto
                        {
                            AirlineCode = "FR",
                            FlightNumber = "1642",
                            OriginAirport = _departureCode,
                            DestinationAirport = _arrivalCode,
                            DepartureLocalTime = _utcNow,
                            ArrivalLocalTime = _utcNow.AddHours(1),
                            DepartureUtc = _utcNow,
                            ArrivalUtc = _utcNow.AddHours(1),
                            OperatedBy = "test",
                            AircraftCode = "test",
                            IsSelfTransfer = false,
                            BookingClass = "Y",
                            Duration = 60,
                            SeatsRemaining = null
                        }
                    ],
                    Duration = 60,
                    SeparationOptions = new SeparationOptions { Options = SeparationOptionEnum.None, MinStay = 0 },
                    DataTimestamp = _utcNow.AddHours(-5)
                },
                new LegDto
                {
                    Segments =
                    [
                        new SegmentDto
                        {
                            AirlineCode = "FR",
                            FlightNumber = "1643",
                            OriginAirport = _arrivalCode,
                            DestinationAirport = _departureCode,
                            DepartureLocalTime = _utcNow.AddHours(8),
                            ArrivalLocalTime = _utcNow.AddHours(8).AddHours(1),
                            DepartureUtc = _utcNow.AddHours(8),
                            ArrivalUtc = _utcNow.AddHours(8).AddHours(1),
                            OperatedBy = "test",
                            AircraftCode = "test",
                            IsSelfTransfer = false,
                            BookingClass = "Y",
                            Duration = 60,
                            SeatsRemaining = null
                        }
                    ],
                    Duration = 60,
                    SeparationOptions = new SeparationOptions { Options = SeparationOptionEnum.None, MinStay = 0 },
                    DataTimestamp = _utcNow.AddHours(-5)
                }
            ],
            TotalAmount = _bp + _tp,
            TaxAmount = _tp,
            PnrCount = 1,
            Baggage = AvailabilityEnum.Surcharge,
            Prices =
            [
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Base, Amount = _bp },
                new PriceDto { PassengerType = PassengerTypeEnum.ADT, PriceType = PriceType.Tax, Amount = _tp }
            ],
            TripType = TripType.RT,
            Currency = "EUR",
            Supplier = null
        };
    }

    public static FlightDto Ow()
    {
        var ob = Ob();
        foreach (var leg in ob.Legs) leg.SeparationOptions=SeparationOptions.OneWay;
        return ob;
    }

    public static FlightDto Ib() => Ob().Reverse();

    public static FlightDto Currency(this FlightDto flightDto, string currency)
    {
        flightDto.Currency = currency;
        return flightDto;
    }

    public static FlightDto ReverseRoute(this FlightDto flightDto)
    {
        foreach (var leg in flightDto.Legs)
        {
            foreach (var segment in leg.Segments)
            {
                (segment.OriginAirport, segment.DestinationAirport) = (segment.DestinationAirport, segment.OriginAirport);
            }

            leg.Segments.Reverse();
        }

        return flightDto;
    }

    public static FlightDto Reverse(this FlightDto flightDto)
    {
        foreach (var leg in flightDto.Legs)
        {
            leg.SeparationOptions.Options = leg.SeparationOptions.Options switch
            {
                SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound => SeparationOptionEnum.RoundTripInbound,
                SeparationOptionEnum.RoundTripOutbound => SeparationOptionEnum.RoundTripInbound,
                SeparationOptionEnum.RoundTripInbound => SeparationOptionEnum.OneWay | SeparationOptionEnum.RoundTripOutbound,
                _ => leg.SeparationOptions.Options
            };

            foreach (var segment in leg.Segments)
            {
                (segment.OriginAirport, segment.DestinationAirport) = (segment.DestinationAirport, segment.OriginAirport);
            }

            leg.Segments.Reverse();
        }

        flightDto.TripType = flightDto.TripType switch
        {
            TripType.OWO => TripType.OWI,
            TripType.OWI => TripType.OWO,
            _ => flightDto.TripType
        };

        return flightDto;
    }

    public static FlightDto SeatsRemaining(this FlightDto flightDto, int? seatsRemaining)
    {
        foreach (var leg in flightDto.Legs)
        {
            foreach (var segment in leg.Segments)
            {
                segment.SeatsRemaining = seatsRemaining;
            }
        }

        return flightDto;
    }

    public static FlightDto Price(this FlightDto flightDto, decimal price)
    {
        foreach (var p in flightDto.Prices)
        {
            p.Amount = price;
        }

        return flightDto;
    }

    public static FlightDto TotalFaresLeft(this FlightDto flightDto, int? totalFaresLeft)
    {
        foreach (var leg in flightDto.Legs)
        {
            leg.TotalFaresLeft = totalFaresLeft;
        }

        return flightDto;
    }

    public static FlightDto ShiftHours(this FlightDto flightDto, int hours)
    {
        var timespan = TimeSpan.FromHours(hours);

        foreach (var leg in flightDto.Legs)
        {
            foreach (var segment in leg.Segments)
            {
                segment.DepartureLocalTime += timespan;
                segment.DepartureUtc += timespan;
                segment.ArrivalLocalTime += timespan;
                segment.ArrivalUtc += timespan;
            }
        }

        return flightDto;
    }

    public static IServiceCollection AddMassTransitTestHarness(this IServiceCollection services)
    {
        return services.AddMassTransitTestHarness(opt => opt.SetEndpointNameFormatter(new DefaultEndpointNameFormatter(true)));
    }
}