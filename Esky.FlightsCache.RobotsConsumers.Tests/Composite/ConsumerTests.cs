using Esky.Flights.Integration.Providers.Contract;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.RobotsConsumers.Composite;
using Esky.FlightsCache.RobotsConsumers.Consumers;
using Esky.FlightsCache.RobotsConsumers.Services;
using Esky.FlightsCache.RobotsConsumers.Tests.Mocks;
using Esky.FlightsCache.RobotsProducers.Messages;
using Esky.FlightsCache.RobotsProducers.Tools;
using Esky.Framework.PartnerSettings.Enums;
using MassTransit;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace Esky.FlightsCache.RobotsConsumers.Tests.Composite;

public class ConsumerTests
{
    private readonly ISearchService<QueueElement> _searchService = Substitute.For<ISearchService<QueueElement>>();
    private readonly ISearchService<SSCQueueElement> _sscSearchService = Substitute.For<ISearchService<SSCQueueElement>>();
    private readonly ICacheRequestBuilder _cacheRequestBuilder = Substitute.For<ICacheRequestBuilder>();
    private readonly IFlightCacheConverter _flightCacheConverter = new MockedFlightsConverter();
    private readonly ICacheTimeoutConfiguration _timeoutConfiguration = Substitute.For<ICacheTimeoutConfiguration>();
    private readonly IAirportCurrencyService _airportCurrencyService = Substitute.For<IAirportCurrencyService>();
    private readonly ILogger<Consumer> _logger = Substitute.For<ILogger<Consumer>>();
    private readonly IStalenessEvaluator _stalenessEvaluator = Substitute.For<IStalenessEvaluator>();

    private readonly ConsumeContext<CompositeQueueElement> _context =
        Substitute.For<ConsumeContext<CompositeQueueElement>>();
    private readonly Consumer _sut;

    public ConsumerTests()
    {
        _sut = new Consumer(_searchService, _sscSearchService, _cacheRequestBuilder, _airportCurrencyService, _flightCacheConverter, _timeoutConfiguration,_stalenessEvaluator, _logger);
        _searchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse {Data = [CreateFlight()]}));
        _sscSearchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse {Data = [CreateFlight()]}));
        _cacheRequestBuilder
            .Build(default, default, default, default)
            .ReturnsForAnyArgs(x => new CacheRequest
            {
                Flights = x.Arg<List<FlightCache>>(),
                SourceDescription = new SourceDescription
                {
                    PaxConfiguration = x.Arg<QueueElement>().PaxConfiguration,
                    PartnerCode = x.Arg<QueueElement>().PartnerCode,
                    Provider = ((int)(x.Arg<QueueElement>().ProviderCode ?? 0)).ToString(),
                    ProviderCode = (int)(x.Arg<QueueElement>().ProviderCode ?? 0),
                    Supplier = x.Arg<QueueElement>().Supplier
                },
                CommandOptions = new CommandDataOptions()
            });
        _stalenessEvaluator
            .IsStale(default, Arg.Any<SearchResponse>())
            .ReturnsForAnyArgs(false);
    }
    
    [Fact]
    public async Task WhenSingleQueueElement_ThenReturnSingleFlight()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement { PaxConfigurations = ["*******"], ProviderCode = Provider(1) });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        _searchService.ReceivedCalls().Should().HaveCount(1);
        AssertGenericSearch(x => x.PaxConfiguration == "*******");
        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x => x.Flights.Count == 1);
    }
    
    [Fact]
    public async Task When2PaxConfiguration_ThenFlightsAreMerged()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            PaxConfigurations = ["*******", "*******"], ProviderCode = Provider(1)
        });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.PaxConfiguration == "*******");
        AssertGenericSearch(x => x.PaxConfiguration == "*******");
        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x => x.Flights.Count == 1);
    }
    
    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task WhenNestedQueueElementsDefined_ThenProcessAccordingToConfiguration(bool proceedLastOnSuccess)
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            PartnerCode = "1",
            ProviderCode = Provider(1),
            PaxConfigurations = ["*******"],
            ProceedNextOnSuccess = true,
            NextQueueElements = [new CompositeQueueElement
            {
                PartnerCode = "2",
                ProviderCode = Provider(1),
                PaxConfigurations = ["*******"],
                ProceedNextOnSuccess = proceedLastOnSuccess,
                NextQueueElements = [new CompositeQueueElement
                {
                    PartnerCode = "3",
                    ProviderCode = Provider(1),
                    PaxConfigurations = ["*******"],
                }]
            }]
        });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.PartnerCode == "1");
        AssertGenericSearch(x => x.PartnerCode == "2");
        
        if (proceedLastOnSuccess)
            AssertGenericSearch(x => x.PartnerCode == "3");
        else
            AssertGenericSearch(x => x.PartnerCode == "3", notReceived: true);

        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x =>
            x.Flights.Count == 1 
            && x.SourceDescription.PartnerCode == (proceedLastOnSuccess ? "3" : "2"));
    }
    
    [Fact]
    public async Task WhenFirstSearchSucceededAndNotProceedNextIsSet_ThenOnlyOneSearchIsDone()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            PartnerCode = "1",
            ProviderCode = Provider(1),
            PaxConfigurations = ["*******"],
            ProceedNextOnSuccess = false,
            NextQueueElements = [new CompositeQueueElement
            {
                PartnerCode = "2",
                ProviderCode = Provider(1),
                PaxConfigurations = ["*******"]
            }]
        });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.PartnerCode == "1");
        AssertGenericSearch(x => x.PartnerCode == "2", notReceived: true);
        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x => x.Flights.Count == 1 && x.SourceDescription.PartnerCode == "1");
    }
    
    [Fact]
    public async Task WhenFirstSearchFailed_ThenMakeNextSearch()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            PartnerCode = "1",
            ProviderCode = Provider(1),
            PaxConfigurations = ["*******"],
            ProceedNextOnSuccess = false,
            NextQueueElements = [new CompositeQueueElement
            {
                PartnerCode = "2",
                ProviderCode = Provider(1),
                PaxConfigurations = ["*******"]
            }]
        });
        
        _searchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(x =>
            {
                var qe = x.Arg<QueueElement>();
                return qe.PartnerCode == "1" 
                    ? (new SearchFlightsProviderQuery(), new SearchResponse { IsError = true}) 
                    : (new SearchFlightsProviderQuery(), new SearchResponse { Data = [CreateFlight()] });
            });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.PartnerCode == "1");
        AssertGenericSearch(x => x.PartnerCode == "2");
        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x => x.Flights.Count == 1 && x.SourceDescription.PartnerCode == "2");
    }
    
    [Fact]
    public async Task WhenFirstSearchIsStale_ThenMakeNextSearch()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            PartnerCode = "1",
            ProviderCode = Provider(1),
            PaxConfigurations = ["*******"],
            ProceedNextOnSuccess = false,
            NextQueueElements = [new CompositeQueueElement
            {
                PartnerCode = "2",
                ProviderCode = Provider(1),
                PaxConfigurations = ["*******"]
            }]
        });
        
        _stalenessEvaluator
            .IsStale(Arg.Any<ProviderCodeEnum>(), Arg.Any<SearchResponse>())
            .Returns(true, false);
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.PartnerCode == "1");
        AssertGenericSearch(x => x.PartnerCode == "2");
        _sscSearchService.ReceivedCalls().Should().HaveCount(0);
        AssertPublish(x => x.Flights.Count == 1 && x.SourceDescription.PartnerCode == "2");
    }
    
    [Fact]
    public async Task WhenSSCProvider_ThenSearchMadeInSSC()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement {  ProviderCode = ProviderCodeEnum.SSCProvider, Supplier = "easyjet", PaxConfigurations = ["*******"] });
        
        // act
        await _sut.Consume(_context);
        
        //assert
        _searchService.ReceivedCalls().Should().HaveCount(0);
        _sscSearchService.ReceivedCalls().Should().HaveCount(1);
        AssertSSCSearch(x => x.ProviderCode == ProviderCodeEnum.SSCProvider && x.Supplier == "easyjet" && x.PaxConfiguration == "*******");
        AssertPublish(x => x.Flights.Count == 1
                           && x.SourceDescription.Provider == ((int)ProviderCodeEnum.SSCProvider).ToString()
                           && x.SourceDescription.Supplier == "easyjet");
    }
    
    [Fact]
    public async Task WhenProviderOverrideDefined_ThenOverrideInResult()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            ProviderCode = ProviderCodeEnum.SSCProvider, Supplier = "easyjet", PaxConfigurations = ["*******"],
            OverrideSettings = new CompositeQueueElement.Override(
                new CompositeQueueElement.ProviderOverride(ProviderCodeEnum.TravelFusion),
                new CompositeQueueElement.SupplierOverride("compositeEasyjet"))
        });
        _sscSearchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse { Data = [CreateFlight("easyjet")] }));
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertSSCSearch(x => x.ProviderCode == ProviderCodeEnum.SSCProvider && x.Supplier == "easyjet" && x.PaxConfiguration == "*******");
        AssertPublish(x => x.Flights.Count == 1
                           && x.Flights.All(f => f.ProviderCode == 58 && f.Supplier == "compositeEasyjet")
                           && x.SourceDescription.Provider == "58"
                           && x.SourceDescription.ProviderCode == 58
                           && x.SourceDescription.Supplier == "compositeEasyjet");
    }
    
    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task WhenNestedProviderOverrideDefined_ThenOverrideInResult(bool proceedNextOnSuccess)
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            ProviderCode = ProviderCodeEnum.SSCProvider, Supplier = "easyjet", PaxConfigurations = ["*******"],
            OverrideSettings = new CompositeQueueElement.Override(
                new CompositeQueueElement.ProviderOverride(ProviderCodeEnum.TravelFusion),
                new CompositeQueueElement.SupplierOverride("compositeEasyjet")),
            ProceedNextOnSuccess = proceedNextOnSuccess,
            NextQueueElements = [
            new CompositeQueueElement
            {
                ProviderCode = ProviderCodeEnum.TravelFusion, Supplier = "ezy", PaxConfigurations = ["*******"],
                OverrideSettings = new CompositeQueueElement.Override(
                    new CompositeQueueElement.ProviderOverride(ProviderCodeEnum.TravelFusion),
                    new CompositeQueueElement.SupplierOverride("compositeEasyjet")),
            }]
        });
        
        _sscSearchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse { Data = [CreateFlight("easyjet")] }));
        
        _searchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse {Data = [CreateFlight("ezy")]}));
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertSSCSearch(x => x.ProviderCode == ProviderCodeEnum.SSCProvider && x.Supplier == "easyjet");
        if (proceedNextOnSuccess) AssertGenericSearch(x => x.ProviderCode == ProviderCodeEnum.TravelFusion && x.Supplier == "ezy");
        AssertPublish(x => x.Flights.Count == 1
                           && x.Flights.All(f => f.ProviderCode == 58 && f.Supplier == "compositeEasyjet")
                           && x.SourceDescription.Provider == "58"
                           && x.SourceDescription.ProviderCode == 58
                           && x.SourceDescription.Supplier == "compositeEasyjet");
    }
    
    [Fact]
    public async Task WhenSupplierOverrideDefined_ThenOverrideOnlyMatchingSupplierInResult()
    {
        // arrange
        _context.Message.Returns(new CompositeQueueElement
        {
            ProviderCode = ProviderCodeEnum.TravelFusion, Supplier = "ezy", PaxConfigurations = ["*******"],
            PartnerCode = "MASTER",
            OverrideSettings = new CompositeQueueElement.Override(
                new CompositeQueueElement.ProviderOverride(ProviderCodeEnum.TravelFusion),
                new CompositeQueueElement.SupplierOverride("compositeEasyjet")),
            ProceedNextOnSuccess = false,
        });
        _searchService
            .Search(default, default, default)
            .ReturnsForAnyArgs(_ => (new SearchFlightsProviderQuery(), new SearchResponse {Data = [CreateFlight("ezy"), 
                CreateFlight("not-ezy")]}));
        
        // act
        await _sut.Consume(_context);
        
        //assert
        AssertGenericSearch(x => x.ProviderCode == ProviderCodeEnum.TravelFusion && x.Supplier == "ezy");
        AssertPublish(x => x.Flights.Count == 2
                           && x.Flights.Any(f => f.ProviderCode == 58 && f.Supplier == "compositeEasyjet")
                           && x.Flights.Any(f => f.ProviderCode == 58 && f.Supplier == "not-ezy")
                           && x.SourceDescription.Provider == "58"
                           && x.SourceDescription.ProviderCode == 58
                           && x.SourceDescription.Supplier == "compositeEasyjet");
    }
    
    private void AssertGenericSearch(Expression<Predicate<QueueElement>> predicate, bool notReceived = false)
    {
        var service = notReceived ? _searchService.DidNotReceive() : _searchService.Received();
        service.Search(Arg.Is(predicate), Arg.Any<string>(), Arg.Any<CancellationToken>());
    }
    
    private void AssertSSCSearch(Expression<Predicate<SSCQueueElement>> predicate, bool notReceived = false)
    {
        var service = notReceived ? _sscSearchService.DidNotReceive() : _sscSearchService.Received();
        service.Search(Arg.Is(predicate), Arg.Any<string>(), Arg.Any<CancellationToken>());
    }

    private void AssertPublish(Expression<Predicate<CacheRequest>> predicate)
    {
        _context.Received()
            .Publish(Arg.Is(predicate), Arg.Any<CancellationToken>());
    }

    private static FlightDto CreateFlight(string supplier = null)
    {
        return new FlightDto
        {
            Supplier = supplier,
            Legs = [ new LegDto
            {
                Segments = [new SegmentDto
                {
                    AirlineCode = "U2",
                    OriginAirport = "LGW",
                    DestinationAirport = "HRG",
                    FlightNumber = "U21111"
                }]
            }]
        };
    }

    private static ProviderCodeEnum Provider(int provider) => (ProviderCodeEnum)provider;
}