using Esky.FlightsCache.RobotsConsumers.IoC;
using Microsoft.Extensions.Hosting;

namespace Esky.FlightsCache.RobotsConsumers.Tests;

public class DiTests
{
    [Fact]
    public void TestConsumersConfiguration()
    {
        Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) => services.ConfigureIoC(context.Configuration))
            .UseDefaultServiceProvider((context, options) =>
            {
                options.ValidateScopes = true;
                options.ValidateOnBuild = true;
            })
            .Build();
    }
}