using Esky.FlightsCache.Robots.Messages;
using Esky.FlightsCache.RobotsConsumers.HotelConsumers;

namespace Esky.FlightsCache.RobotsConsumers.Tests.HotelsApiConsumer;
public class HotelsApiRequestBuilderTests
{
    [Fact]
    public void RequestParametersAreCopiedFromQueueElement()
    {
        var queueElement = new HotelsApiQueueElement
        {
            ProviderCode = Framework.PartnerSettings.Enums.ProviderCodeEnum.BookingCom,
            PartnerCode = "ESKY",
            ProviderConfigurationId = "epa",
            HotelMetaCodes = [1, 2],
            Adults = 2,
            ChildrenAges = [5, 8],
            CheckInDate = DateOnly.FromDateTime(DateTime.UtcNow),
            CheckOutDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(7)),
        };

        var requestBuilder = new HotelsApiRequestBuilder();

        var request = requestBuilder.CreateRequest(queueElement);

        Assert.Equal(queueElement.CheckInDate, request.StayInformation.CheckInDate);
        Assert.Equal(queueElement.CheckOutDate, request.StayInformation.CheckOutDate);
        Assert.Equal(queueElement.HotelMetaCodes, request.HotelMetaCodes);
        Assert.Equal(queueElement.PartnerCode, request.PartnerCode);
        Assert.Equal(queueElement.ProviderConfigurationId, request.ProviderConfigurationId);
        Assert.Equal(queueElement.Adults, request.Occupancy[0].Adults);
        Assert.Equal(queueElement.ChildrenAges, request.Occupancy[0].ChildrenAges);
    }
}
