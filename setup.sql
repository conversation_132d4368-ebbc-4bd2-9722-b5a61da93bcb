IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'IBE_FlightsPromoCache')
BEGIN
  CREATE DATABASE IBE_FlightsPromoCache;
END;
GO

USE [IBE_FlightsPromoCache]
GO

ALTER DATABASE IBE_FlightsPromoCache SET MEMORY_OPTIMIZED_ELEVATE_TO_SNAPSHOT = ON
GO

IF NOT EXISTS (SELECT * FROM SYS.FILEGROUPS WHERE name='imoltp_mod1')
ALTER DATABASE IBE_FlightsPromoCache ADD FILEGROUP imoltp_mod CONTAINS MEMORY_OPTIMIZED_DATA
ALTER DATABASE IBE_FlightsPromoCache ADD FILE (name='imoltp_mod1', filename='/var/opt/mssql/data/imoltp_mod1') TO FILEGROUP imoltp_mod 
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE SCHEMA cache
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Airports')
CREATE TABLE [dbo].[Airports](
	[AirportHash] [int] NOT NULL,
	[AirportCode] [char](3) NOT NULL,
	[MultiportCode] [varchar](4) NOT NULL,
	[CityCode] [varchar](10) NOT NULL,
	[CountryCode] [char](2) NOT NULL,
	[ContinentCode] [char](2) NOT NULL
) ON [PRIMARY]
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'ContinentTravelOffsets')
CREATE TABLE [dbo].[ContinentTravelOffsets](
	[DepartureContinentCode] [char](2) NOT NULL,
	[ArrivalContinentCode] [char](2) NOT NULL,
	[SecondFlightDepartureOffsetDays] [tinyint] NOT NULL
) ON [PRIMARY]
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'AirportCodes')
CREATE TABLE [dbo].[AirportCodes]
(
	[Code] [varchar](5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[HashCode] [smallint] NOT NULL,

INDEX [HIX_Code] NONCLUSTERED HASH 
(
	[Code]
)WITH ( BUCKET_COUNT = 4096),
 PRIMARY KEY NONCLUSTERED 
(
	[Code] ASC,
	[HashCode] ASC
)
)WITH ( MEMORY_OPTIMIZED = ON , DURABILITY = SCHEMA_AND_DATA )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'cache' AND TABLE_NAME = 'FlightOW')
CREATE TABLE [cache].[FlightOW]
(
	[Id] [int] NOT NULL,
	[ExpirationDate] [smalldatetime] NOT NULL,
	[ProviderCode] [tinyint] NOT NULL,
	[DepartureDate] [smalldatetime] NOT NULL,
	[DepartureDay] [date] NOT NULL,
	[ArrivalDate] [smalldatetime] NOT NULL,
	[IsOneWay] [bit] NOT NULL,
	[IsRoundTripOutbound] [bit] NOT NULL,
	[IsRoundTripInbound] [bit] NOT NULL,
	[MinStay] [tinyint] NOT NULL,
	[SourceId] [bigint] NOT NULL,
	[DepartureHashCode] [smallint] NOT NULL,
	[ArrivalHashCode] [smallint] NOT NULL,
	[RefPrice] [int] NOT NULL,

INDEX [IX_Get3LegFlights_Middle] NONCLUSTERED 
(
	[IsOneWay] ASC,
	[DepartureHashCode] ASC,
	[DepartureDate] ASC
),
INDEX [IX_GetMultiLegFlights_Arrival] NONCLUSTERED 
(
	[IsOneWay] ASC,
	[ArrivalHashCode] ASC,
	[DepartureDay] ASC,
	[RefPrice] ASC
),
INDEX [IX_GetMultiLegFlights_Departure] NONCLUSTERED 
(
	[IsOneWay] ASC,
	[DepartureHashCode] ASC,
	[DepartureDay] ASC,
	[RefPrice] ASC
),
INDEX [IX_IsOneWay] NONCLUSTERED 
(
	[IsOneWay] ASC,
	[ProviderCode] ASC,
	[DepartureHashCode] ASC,
	[ArrivalHashCode] ASC,
	[DepartureDay] ASC,
	[RefPrice] ASC
),
INDEX [IX_IsRoundTripInbound] NONCLUSTERED 
(
	[IsRoundTripInbound] ASC,
	[ProviderCode] ASC,
	[ArrivalHashCode] ASC,
	[DepartureHashCode] ASC,
	[DepartureDay] ASC,
	[MinStay] ASC,
	[RefPrice] ASC
),
INDEX [IX_IsRoundTripOutbound] NONCLUSTERED 
(
	[IsRoundTripOutbound] ASC,
	[ProviderCode] ASC,
	[DepartureHashCode] ASC,
	[ArrivalHashCode] ASC,
	[DepartureDay] ASC,
	[MinStay] ASC,
	[RefPrice] ASC
),
 PRIMARY KEY NONCLUSTERED 
(
	[Id] ASC
)
)WITH ( MEMORY_OPTIMIZED = ON , DURABILITY = SCHEMA_AND_DATA )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'cache' AND TABLE_NAME = 'TransferAirports')
CREATE TABLE [cache].[TransferAirports]
(
	[ArrivalHashCode] [smallint] NOT NULL,
	[DepartureHashCode] [smallint] NOT NULL,
	[MinMinutesBetweenSegments] [smallint] NOT NULL,
	[MaxMinutesBetweenSegments] [smallint] NOT NULL,
	[CountryCode] [char](2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,

INDEX [HIX_ArrivalHashCode] NONCLUSTERED HASH 
(
	[ArrivalHashCode]
)WITH ( BUCKET_COUNT = 4096),
INDEX [HIX_DepartureHashCode] NONCLUSTERED HASH 
(
	[DepartureHashCode]
)WITH ( BUCKET_COUNT = 4096),
 CONSTRAINT [PK_TransferAirports_oltp]  PRIMARY KEY NONCLUSTERED 
(
	[ArrivalHashCode] ASC,
	[DepartureHashCode] ASC
)
)WITH ( MEMORY_OPTIMIZED = ON , DURABILITY = SCHEMA_AND_DATA )
GO

ALTER TABLE [cache].[TransferAirports] ADD  DEFAULT ('--') FOR [CountryCode]
GO

IF NOT EXISTS (SELECT * FROM sys.types WHERE is_table_type = 1 AND name = 'ProviderCodesType')
CREATE TYPE [dbo].[ProviderCodesType] AS TABLE(
	[ProviderCode] [tinyint] NOT NULL,
	INDEX [ix1] NONCLUSTERED 
(
	[ProviderCode] ASC
)
)
WITH ( MEMORY_OPTIMIZED = ON )
GO


/**********************************************************************************************************************************/

CREATE OR ALTER PROCEDURE [cache].[GetMultiLegFlightsOW_v4]
	@DepartureCode varchar(5),
	@ArrivalCode varchar(5),
	@DepartureDay date,
	@NumberOfCheapestFlights int,
	@ProvidersToUse VARCHAR(1024) = NULL,
	@DepartureOffsetDays tinyint,
	@NearbyAirportsOption TINYINT = 0
AS
BEGIN
	SET NOCOUNT ON;
	
	/*** NearbyAirportOptions
    {
        None = 0,
        DepartureOnly = 1,
        ArrivalOnly = 2,
        DepartureAndArrival = 3
    } ***/

	IF @NearbyAirportsOption = 1 OR @NearbyAirportsOption = 3
	BEGIN
		SELECT @DepartureCode = replace(@DepartureCode, '*', '') + '*'
	END

	IF @NearbyAirportsOption = 2 OR @NearbyAirportsOption = 3
	BEGIN
		SELECT @ArrivalCode = replace(@ArrivalCode, '*', '') + '*'
	END

	DECLARE @providers [dbo].[ProviderCodesType];
	
	INSERT INTO @providers 
		([ProviderCode])
	SELECT
		CAST([value] AS TINYINT)
	FROM STRING_SPLIT(@ProvidersToUse,',');

	SELECT TOP (@NumberOfCheapestFlights)
		--SEG 1
		f1.[DepartureDate]	AS [Leg_0_DepartureDate1],
		f1.[ArrivalDate]	AS [Leg_0_ArrivalDate1],
		f1.[ProviderCode]	AS [Leg_0_ProviderCode1],
		f1.[Id]				AS [Leg_0_DetailsId1],
		CAST(1 as TINYINT)	AS [Leg_0_Fid1],
		f1.[DepartureHashCode] AS [Leg_0_DepartureHashCode1],
		f1.[ArrivalHashCode] AS [Leg_0_ArrivalHashCode1],

		--SEG 2
		f2.[DepartureDate]	AS [Leg_0_DepartureDate2],
		f2.[ArrivalDate]	AS [Leg_0_ArrivalDate2],
		f2.[ProviderCode]	AS [Leg_0_ProviderCode2],
		f2.[Id]				AS [Leg_0_DetailsId2],	
		CAST(2 as TINYINT)	AS [Leg_0_Fid2],
	    f2.[DepartureHashCode] AS [Leg_0_DepartureHashCode2],
		f2.[ArrivalHashCode] AS [Leg_0_ArrivalHashCode2],

		--LEG
		CAST(1 as BIT)		AS [Leg_0_IsOneWay],
		f1.[RefPrice] + f2.[RefPrice] AS [RefPrice]
	FROM (
		SELECT f1.*
		FROM [dbo].[AirportCodes] da WITH (SNAPSHOT)
		INNER LOOP JOIN [cache].[FlightOW] f1 WITH (SNAPSHOT)
			ON f1.[DepartureHashCode] = da.[HashCode]
			AND (f1.IsOneWay = 1)
		WHERE da.[Code] = @DepartureCode
			AND f1.[DepartureDay] = @DepartureDay
		) AS f1
	INNER JOIN cache.[TransferAirports] ta WITH (SNAPSHOT)
		ON f1.[ArrivalHashCode] = ta.[ArrivalHashCode]
	INNER JOIN (
		SELECT f2.*
		FROM [dbo].[AirportCodes] aa WITH (SNAPSHOT)
		INNER LOOP JOIN [cache].[FlightOW] f2 WITH (SNAPSHOT)
			ON f2.[ArrivalHashCode] = aa.[HashCode]
			AND (f2.IsOneWay = 1)
		WHERE aa.[Code] = @ArrivalCode
			AND f2.[DepartureDay] BETWEEN @DepartureDay AND DATEADD(day, @DepartureOffsetDays, @DepartureDay)
		) AS f2 
			ON f2.[DepartureHashCode] = ta.[DepartureHashCode]
			AND f2.DepartureDate BETWEEN DATEADD(MINUTE, ta.MinMinutesBetweenSegments, f1.ArrivalDate) AND DATEADD(MINUTE, ta.MaxMinutesBetweenSegments, f1.ArrivalDate)
	INNER JOIN @providers p1 ON p1.ProviderCode = f1.ProviderCode
	INNER JOIN @providers p2 ON p2.ProviderCode = f2.ProviderCode
	ORDER BY f1.[RefPrice] + f2.[RefPrice]
END
GO

------------------------------------------------------------------------------------------------------

CREATE OR ALTER PROCEDURE [cache].[GetMultiLegFlightsOW_withTransferCountries]
	@DepartureCode varchar(5),
	@ArrivalCode varchar(5),
	@DepartureDay date,
	@NumberOfCheapestFlights int,
	@ProvidersToUse VARCHAR(1024) = NULL,
	@DepartureOffsetDays tinyint,
	@NearbyAirportsOption TINYINT = 0,
	@TransferCountries VARCHAR(1024)
AS
BEGIN
	SET NOCOUNT ON;
	
	/*** NearbyAirportOptions
    {
        None = 0,
        DepartureOnly = 1,
        ArrivalOnly = 2,
        DepartureAndArrival = 3
    } ***/

	IF @NearbyAirportsOption = 1 OR @NearbyAirportsOption = 3
	BEGIN
		SELECT @DepartureCode = replace(@DepartureCode, '*', '') + '*'
	END

	IF @NearbyAirportsOption = 2 OR @NearbyAirportsOption = 3
	BEGIN
		SELECT @ArrivalCode = replace(@ArrivalCode, '*', '') + '*'
	END

	DECLARE @providers [dbo].[ProviderCodesType];
	
	INSERT INTO @providers 
		([ProviderCode])
	SELECT
		CAST([value] AS TINYINT)
	FROM STRING_SPLIT(@ProvidersToUse,',');

	SELECT TOP (@NumberOfCheapestFlights)
		--SEG 1
		f1.[DepartureDate]	AS [Leg_0_DepartureDate1],
		f1.[ArrivalDate]	AS [Leg_0_ArrivalDate1],
		f1.[ProviderCode]	AS [Leg_0_ProviderCode1],
		f1.[Id]				AS [Leg_0_DetailsId1],
		CAST(1 as TINYINT)	AS [Leg_0_Fid1],
		f1.[DepartureHashCode] AS [Leg_0_DepartureHashCode1],
		f1.[ArrivalHashCode] AS [Leg_0_ArrivalHashCode1],

		--SEG 2
		f2.[DepartureDate]	AS [Leg_0_DepartureDate2],
		f2.[ArrivalDate]	AS [Leg_0_ArrivalDate2],
		f2.[ProviderCode]	AS [Leg_0_ProviderCode2],
		f2.[Id]				AS [Leg_0_DetailsId2],	
		CAST(2 as TINYINT)	AS [Leg_0_Fid2],
	    f2.[DepartureHashCode] AS [Leg_0_DepartureHashCode2],
		f2.[ArrivalHashCode] AS [Leg_0_ArrivalHashCode2],

		--LEG
		CAST(1 as BIT)		AS [Leg_0_IsOneWay],
		f1.[RefPrice] + f2.[RefPrice] AS [RefPrice]
	FROM (
		SELECT f1.*
		FROM [dbo].[AirportCodes] da WITH (SNAPSHOT)
		INNER LOOP JOIN [cache].[FlightOW] f1 WITH (SNAPSHOT)
			ON f1.[DepartureHashCode] = da.[HashCode]
			AND (f1.IsOneWay = 1)
		WHERE da.[Code] = @DepartureCode
			AND f1.[DepartureDay] = @DepartureDay
		) AS f1
	INNER JOIN cache.[TransferAirports] ta WITH (SNAPSHOT)
		ON f1.[ArrivalHashCode] = ta.[ArrivalHashCode]
		AND ta.[CountryCode] IN (SELECT [value] FROM STRING_SPLIT(@TransferCountries,','))
	INNER JOIN (
		SELECT f2.*
		FROM [dbo].[AirportCodes] aa WITH (SNAPSHOT)
		INNER LOOP JOIN [cache].[FlightOW] f2 WITH (SNAPSHOT)
			ON f2.[ArrivalHashCode] = aa.[HashCode]
			AND (f2.IsOneWay = 1)
		WHERE aa.[Code] = @ArrivalCode
			AND f2.[DepartureDay] BETWEEN @DepartureDay AND DATEADD(day, @DepartureOffsetDays, @DepartureDay)
		) AS f2 
			ON f2.[DepartureHashCode] = ta.[DepartureHashCode]
			AND f2.DepartureDate BETWEEN DATEADD(MINUTE, ta.MinMinutesBetweenSegments, f1.ArrivalDate) AND DATEADD(MINUTE, ta.MaxMinutesBetweenSegments, f1.ArrivalDate)
	INNER JOIN @providers p1 ON p1.ProviderCode = f1.ProviderCode
	INNER JOIN @providers p2 ON p2.ProviderCode = f2.ProviderCode
	ORDER BY f1.[RefPrice] + f2.[RefPrice]
END
GO






