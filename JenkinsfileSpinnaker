user = buildUser()
settings = settingsResolver(
    gitEndpoint: env.GIT_ENDPOINT ?: 'ssh://***********************:7999',
    gitCredentialsId: env.GIT_CREDENTIALS_ID,
    sourceRepositoryOwner: (params.get('sourceRepositoryOwner') ?: env.DEFAULT_SOURCE_REPOSITORY_OWNER).toLowerCase(),
    sourceRepositoryName: params.get('sourceRepositoryName') ?: env.DEFAULT_SOURCE_REPOSITORY_NAME,
    sourceRepository: { "${gitEndpoint}/${sourceRepositoryOwner}/${sourceRepositoryName}.git" },
    sourceBranch: params.get('sourceBranch') ?: env.DEFAULT_SOURCE_BRANCH,
    appName: params.get('appName'),
    imageTag: params.get('imageVersion') ?: env.DEFAULT_IMAGE_VERSION,
    dockerCloudRegistryEndpointUrl: { parseUri(env.DOCKER_CLOUD_REGISTRY_ENDPOINT ?: 'https://eu.gcr.io') },
    dockerCloudRegistryCredentialsId: env.DOCKER_CLOUD_REGISTRY_CREDENTIALS_ID,
    dockerCloudArtifactsRegistryEndpoint: { parseUri(env.DOCKER_CLOUD_ARTIFACTS_REGISTRY_ENDPOINT ?: 'https://europe-docker.pkg.dev') },
    dockerCloudArtifactsRegistryCredentialsId: env.DOCKER_CLOUD_ARTIFACTS_REGISTRY_CREDENTIALS_ID,
    buildImageName: { "${dockerCloudArtifactsRegistryEndpoint.host}/esky-common/esky-images/buildenv/dotnet7:latest" },
    buildAgentLabel: "${env.JENKINS_NODE}",
    imageVendor: 'esky-ets-flightscontent-pro',
	apiImageNameConsumer: { "${dockerCloudRegistryEndpointUrl.host}/${imageVendor}/${appName}consumers:${imageTag}" },
    apiImageNameProducer: { "${dockerCloudRegistryEndpointUrl.host}/${imageVendor}/${appName}producers:${imageTag}" },
    spinnakerAdditionalParams: params.get('additionalParams'),
    prMergedStatus: params.get('uploadImage'),
)

badge.addLabel(user.fullName, priority: 'info', title: "Started by ${user.fullName} <${user.emailAddress}>")
badge.addLabel(settings.imageTag)
if (env.pullRequestId) {
    badge.addLabel(
        """<a href="${env.pullRequestUrl}">#${env.pullRequestId}</a>""",
        priority: 'info',
        title: env.pullRequestTitle
    )
}
if (env.sourceBranch) {
    badge.addLabel(
        truncate(env.sourceBranch, 16),
        priority: 'primary',
        title: "Branch ${env.sourceBranch} from fork ${env.sourceRepositoryOwner}/${env.sourceRepositoryName}"
    )
}

echo """\
    Produces the following images:
      - ${settings.apiImagePrefix}
    """.stripIndent()

wraps { // NOTICE: Stages are wrapped inside container started from image settings.buildImageName
    stage ('Checkout'){
        git \
            branch: settings.sourceBranch,
            credentialsId: settings.gitCredentialsId,
            url: settings.sourceRepository,
            pool: false,
            changelog: true
    }
        
    stage('Assemble') {
        try {
            notifyBitbucket()
            sh("""\
                docker-compose -f docker-compose.yml -f docker-compose.ci.build.yml down --remove-orphans
                docker-compose -f docker-compose.yml -f docker-compose.ci.build.yml up --build --exit-code-from integration.tests
                docker tag robotsconsumer ${settings.apiImageNameConsumer}
				docker tag robotsproducer ${settings.apiImageNameProducer}
                docker-compose -f docker-compose.yml -f docker-compose.ci.build.yml down --remove-orphans
            """.stripIndent())
            currentBuild.result = 'SUCCESS'
        } catch (Throwable t) {
            currentBuild.result = 'FAILED'
            throw t
        } finally {
            notifyBitbucket()
        }
    }

    stage('Publish') {
        if (settings.prMergedStatus) {
            dockerRegistry(
                endpointUrl: settings.dockerCloudRegistryEndpointUrl,
                credentialsId: settings.dockerCloudRegistryCredentialsId
            ) {              
                  sh("""\
                        docker push ${settings.apiImageNameConsumer}
                        docker push ${settings.apiImageNameProducer}
            """.stripIndent())
            }
            sshagent([settings.gitCredentialsId]) {
                sh("""\
                    export GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no"
                    git tag release/${settings.imageTag} HEAD
                    git push origin --tags
                """.stripIndent())
            }
        } else {
            println('This is only building flow, your image was not uploaded')
        }
    }

} with { body ->
    timestamps {
        ansiColor {
            node(settings.buildAgentLabel) {
                bitbucketNotifications {
                    dockerRegistry(
                        endpointUrl: settings.dockerCloudArtifactsRegistryEndpoint,
                        credentialsId: settings.dockerCloudArtifactsRegistryCredentialsId
                    ) {
                        dockerContainer(settings.buildImageName, body)
                    }
                }
            }
        }
    }
}
