{"profiles": {"Docker Compose": {"commandName": "DockerCompose", "commandVersion": "1.0", "composeLaunchAction": "LaunchBrowser", "composeLaunchServiceName": "robotsproducer", "composeLaunchUrl": "{Scheme}://localhost:{ServicePort}", "serviceActions": {"mongo.objectdirectory": "StartWithoutDebugging", "mongo.promocache": "StartWithoutDebugging", "mongo.robotproducers": "StartWithoutDebugging", "redis": "StartWithoutDebugging", "robot.rabbitmq": "StartWithoutDebugging", "robotsconsumer": "StartDebugging", "robotsproducer": "StartDebugging", "mockserver": "StartWithoutDebugging"}}}}