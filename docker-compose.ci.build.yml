services:
  integration.tests:
    image: integration.tests
    build:
      context: .
      dockerfile: source/Esky.FlightsCache.Robots.Integration.Tests/Dockerfile
    depends_on:
      - robotsproducer
      - robotsconsumer
      - mockserver
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: 
      - /bin/bash
      - -c
      - |
        i=0
        until [ \
          "$$(curl -s -w '%{http_code}' -o /dev/null -u robotuser:robotpassword host.docker.internal:15672/api/overview)" \
          -eq 200 ]
        do
          if [[ "$$i" == '5' ]]
          then
            break
          fi

          echo "waiting for rabbit..."
          sleep 5

          ((i++))
        done

        echo "rabbit is up"

        i=0
        until [ \
          "$$(curl -s -w '%{http_code}' -o /dev/null  host.docker.internal:58799/health/ready)" \
          -eq 200 ]
        do
          if [[ "$$i" == '5' ]]
          then
            break
          fi

          echo "waiting for producer..."
          sleep 5

          ((i++))
        done

        echo "producer is up"

        dotnet test "Esky.FlightsCache.Robots.Integration.Tests.csproj"
  