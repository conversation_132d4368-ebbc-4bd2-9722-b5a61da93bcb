# Esky.FlightsCache.Robots

- [Esky.FlightsCache.Robots](#eskyflightscacherobots)
- [Esky.FlightsCache.RobotsProducers](#eskyflightscacherobotsproducers)
  - [Hangfire jobs](#hangfire-jobs)
  - [Evenly distribution (message throttling)](#evenly-distribution-message-throttling)
  - [Links](#links)
    - [Deployment definitions](#deployment-definitions)
    - [Deployment path](#deployment-path)
    - [Instances](#instances)
    - [Observability](#observability)
- [Esky.FlightsCache.RobotsConsumers](#eskyflightscacherobotsconsumers)
  - [Links](#links-1)
    - [Deployment definitions](#deployment-definitions-1)
    - [Deployment path](#deployment-path-1)
    - [Observability](#observability-1)
- [C4 diagram](#c4-diagram)

Robots are responsible for gathering content for Flights Cache based on a defined schedule jobs (and passing it further for saving).

# Esky.FlightsCache.RobotsProducers
Producers are producing messages consumed by Consumers, publishing them into several RabbitMQ exchanges. Exchanges are defined separately for each handled provider. Each message is containing information about single route and single date to be queried for flights. Producers are providing messages throttling which is named here an evenly distribution. Messages are produced by Hangfire jobs and direct API calls.

## Hangfire jobs
Jobs are working in a recurring mode described by CRON expression. Currently there are
- TravelFusion jobs - pack of jobs to handle getting content from TravelFusion provider
    - categorized `TravelFusionCategorizedProducer`: TravelFusion suppliers are put into 4 categories, each category have a different period of processing; **how it works**: based on TravelFusion timetable (retrieved via Flights Cache API) it produces messages to query for flights on routes and dates where we expect that thera are some flights from today +1 day up to partner settings' limit (days forward); to optimize the way of querying TravelFusion API (via FlightsIntegration API) we ask for content for all suppliers from processed catgory, excluding suppliers from more frequently refreshed categories; the existing categories:
        - 1: every 1h
        - 2: every 3h
        - 3: every 6h
        - 4: every 12h
    - existing routes network scan `TravelFusionSuppliersProducer`: generates queue elements for all categorized suppliers excluding days that are already in cache. It uses the week day algorithm, which means that when executed on Wednesday then we generate items for every Wednesday up to partner settings' limit (days forward) - should be executed every day
    - new routes network scan `DiscoverRoutesProducer`: gets routes from Timetables.API marked as `new` and produce messages for all days from +1 up to partner settings' limit (days forward)
- SSC jobs - jobs to get content from SSC (self-served web-scrapping solutions via Flights.Integration.API)
- Cache Demand jobs - with scope defined in Google Sheets, manageble via API and ETSAdmin Web Gui, see [links section](#links).
- Cache Demand jobs - with scope defined in Google Sheets, manageble via API and ETSAdmin Web Gui, see [links section](#links).
- AmadeusLiveCheck - based on cache demand - elements are sent to separate queue by provider code (ALC). Queries cache for flights data (required for getting price from AmadeusLiveCheck) - if there are no flights in cache it uses integration endpoint as fallback
- RyanairRouteFeeRefreshing - sends all ryanair (FR, RK) routes from timetables to queue. Then for each route current algorithm sends two requests (pax ******* and *******) to integration layer and based on results calculates fees for children and infants. 

## Evenly distribution (message throttling)
Evenly distribution is implemented to use Hangfire scheduled jobs: a one execution of a **recurring** job is creating multiple **scheduled** jobs with a set of messages to publish with a certain delay.
To make jobs definitions lightweight and keep Hangfire performance acceptable the related messages are passed indirectly to jobs, by a list of identifiers. Those identifiers are representing messages stored in the MongoDB. When all messages were passed to the job definition directly there started to occure OutOfMemoryExceptions during serialization, reaching max document size limits in MongoDB, worse performance of Hangfire's dashboard and inacceptable (growing) lag in processing scheduled jobs (they were not executed on time).

## Links

### Deployment definitions
- [YAMLs: deployments and config maps](https://stash.eskyspace.com/projects/DEP/repos/flightscontent/browse/esky.flightscache.robotsproducers)

### Deployment path
- Jenkins jobs:
  - [PR job](https://jenkinsets.eskyspace.com/view/Spinnaker/job/SPINNAKER_PR_esky.flightscache.robots/)
  - [Publish job](https://jenkinsets.eskyspace.com/view/Spinnaker/job/SPINNAKER_BUILDER_esky.flightscache.robots/)
- [Docker images registry](https://console.cloud.google.com/gcr/images/esky-ets-flightscontent-pro/eu/esky.flightscache.robotsproducers?project=esky-ets-flightscontent-pro)
- [Spinnaker pipeline](https://spinnaker.eskyspace.com/#/applications/flightscache/executions?pipeline=esky.flightscache.robotsproducers)

### Instances
- [Prod instance](http://esky-flightscache-robotsproducers.service.local-pro.consul/)
  - [Swagger](http://esky-flightscache-robotsproducers.service.local-pro.consul/swagger/index.html)
  - [Hangfire](http://esky-flightscache-robotsproducers.service.local-pro.consul/hangfire/)
- ETS Admin
  - [CacheDemand job list](https://etsadmin.eskyspace.com/FlightsCacheContent/CacheDemandJobList)
  - [CacheDemand sheets job list](https://etsadmin.eskyspace.com/FlightsCacheContent/CacheDemandSheetJobsList)

### Observability
- Grafana dashboards
  - [RabbitMQ Dashboard Producers -> Consumers](https://grafanasre.eskyspace.com/d/t8ktV6cVz/rabbitmq-universal?var-job=rabbitmq-flightscache&var-node=**********&var-port=9000&var-mq_instance=rabbitmq-flightscache&var-source=Prometheus-onprem-techsre&var-env=pro&from=now-3h&to=now&orgId=1)
  - [ETS FCACHE / FlightsCache TravelFusion Robots](https://grafanasre.eskyspace.com/d/oF49FXK4z/flightscache-travelfusion-robots?orgId=1)
  - [ETS Flightscontent / FlightsCache WorkerService / Robots](https://grafanasre.eskyspace.com/d/OT3nnEsWz/flightscache-workerservice-robots?orgId=1)
- [Kibana logs](https://kibana.eskyspace.com/app/dashboards#/view/9d2b4df0-1c01-11ee-b00c-ebcc2c475043?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(description:'',filters:!(('state':(store:appState),meta:(alias:!n,controlledBy:'1631534374808',disabled:!f,index:'2a101380-db2d-11eb-80a8-c7c4ea983b6c',key:Application.keyword,negate:!f,params:(query:esky-flightscache-robotsproducers),type:phrase),query:(match_phrase:(Application.keyword:esky-flightscache-robotsproducers)))),fullScreenMode:!f,options:(hidePanelTitles:!f,useMargins:!t),panels:!((embeddableConfig:(enhancements:()),gridData:(h:13,i:'4bd4458d-c133-4c48-8dbe-19b30666a9b3',w:12,x:0,y:0),id:'6e2daea0-148b-11ec-86a0-5773fbcdbe68',panelIndex:'4bd4458d-c133-4c48-8dbe-19b30666a9b3',type:visualization,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:13,i:e8578a5c-ec2f-4988-9c84-822cb141a169,w:15,x:12,y:0),id:'45948120-148c-11ec-86a0-5773fbcdbe68',panelIndex:e8578a5c-ec2f-4988-9c84-822cb141a169,type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:13,i:dada6948-5351-43c2-8ac7-f31fb7192727,w:21,x:27,y:0),id:'652c0ce0-148e-11ec-86a0-5773fbcdbe68',panelIndex:dada6948-5351-43c2-8ac7-f31fb7192727,type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:10,i:'2e8dad13-2d15-4171-9461-d9bb6ea27b52',w:48,x:0,y:13),id:da512250-148c-11ec-86a0-5773fbcdbe68,panelIndex:'2e8dad13-2d15-4171-9461-d9bb6ea27b52',type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:32,i:ff1cbecb-e02b-4067-94a7-cfc8912df1d8,w:48,x:0,y:23),id:'8bbde6c0-148f-11ec-86a0-5773fbcdbe68',panelIndex:ff1cbecb-e02b-4067-94a7-cfc8912df1d8,type:search,version:'7.17.3')),query:(language:kuery,query:''),tags:!(),timeRestore:!f,title:'ETS%20Flights%20Cache%20Logs',viewMode:view))

# Esky.FlightsCache.RobotsConsumers
Consumers are consuming messages published to RabbitMQ by Producers.
Based on the message content which contains route and date it retrieves data via HTTP from Flights.Integration.API with flights details.
Those details are transformed and sent to RabbitMQ to be stored by another service.

## Links

### Deployment definitions
- [YAMLs: deployments and config maps](https://stash.eskyspace.com/projects/DEP/repos/flightscontent/browse/esky.flightscache.robotsconsumers)

### Deployment path
- Jenkins jobs:
  - [PR job](https://jenkinsets.eskyspace.com/view/Spinnaker/job/SPINNAKER_PR_esky.flightscache.robots/)
  - [Publish job](https://jenkinsets.eskyspace.com/view/Spinnaker/job/SPINNAKER_BUILDER_esky.flightscache.robots/)
- [Docker images registry](https://console.cloud.google.com/gcr/images/esky-ets-flightscontent-pro/eu/esky.flightscache.robotsconsumers?project=esky-ets-flightscontent-pro)
- [Spinnaker pipeline](https://spinnaker.eskyspace.com/#/applications/flightscache/executions?pipeline=esky.flightscache.robotsconsumers)

### Observability
- Grafana dashboards
  - [RabbitMQ Dashboard Consumers -> WorkerService](https://grafanasre.eskyspace.com/d/t8ktV6cVz/rabbitmq-universal?var-job=gcp-rabbitmq-flightscache&var-node=*************&var-port=9000&var-mq_instance=gcp-rabbitmq-flightscache&var-source=Prometheus-techsre&var-env=pro&from=now-3h&to=now&orgId=1)
  - [ETS FCACHE / FlightsCache TravelFusion Robots](https://grafanasre.eskyspace.com/d/oF49FXK4z/flightscache-travelfusion-robots?orgId=1)
  - [ETS Flightscontent / FlightsCache WorkerService / Robots](https://grafanasre.eskyspace.com/d/OT3nnEsWz/flightscache-workerservice-robots?orgId=1)
- [Kibana logs](https://kibana.eskyspace.com/app/dashboards#/view/9d2b4df0-1c01-11ee-b00c-ebcc2c475043?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(description:'',filters:!(('state':(store:appState),meta:(alias:!n,controlledBy:'1631534374808',disabled:!f,index:'2a101380-db2d-11eb-80a8-c7c4ea983b6c',key:Application.keyword,negate:!f,params:(query:esky-flightscache-robotsconsumers),type:phrase),query:(match_phrase:(Application.keyword:esky-flightscache-robotsconsumers)))),fullScreenMode:!f,options:(hidePanelTitles:!f,useMargins:!t),panels:!((embeddableConfig:(enhancements:()),gridData:(h:13,i:'4bd4458d-c133-4c48-8dbe-19b30666a9b3',w:12,x:0,y:0),id:'6e2daea0-148b-11ec-86a0-5773fbcdbe68',panelIndex:'4bd4458d-c133-4c48-8dbe-19b30666a9b3',type:visualization,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:13,i:e8578a5c-ec2f-4988-9c84-822cb141a169,w:15,x:12,y:0),id:'45948120-148c-11ec-86a0-5773fbcdbe68',panelIndex:e8578a5c-ec2f-4988-9c84-822cb141a169,type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:13,i:dada6948-5351-43c2-8ac7-f31fb7192727,w:21,x:27,y:0),id:'652c0ce0-148e-11ec-86a0-5773fbcdbe68',panelIndex:dada6948-5351-43c2-8ac7-f31fb7192727,type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:10,i:'2e8dad13-2d15-4171-9461-d9bb6ea27b52',w:48,x:0,y:13),id:da512250-148c-11ec-86a0-5773fbcdbe68,panelIndex:'2e8dad13-2d15-4171-9461-d9bb6ea27b52',type:lens,version:'7.17.3'),(embeddableConfig:(enhancements:()),gridData:(h:32,i:ff1cbecb-e02b-4067-94a7-cfc8912df1d8,w:48,x:0,y:23),id:'8bbde6c0-148f-11ec-86a0-5773fbcdbe68',panelIndex:ff1cbecb-e02b-4067-94a7-cfc8912df1d8,type:search,version:'7.17.3')),query:(language:kuery,query:''),tags:!(),timeRestore:!f,title:'ETS%20Flights%20Cache%20Logs',viewMode:view))


# C4 diagram
![](<docs/FlightsCacheRobots-diagram/Flights%20Cache%20Robots.svg>)