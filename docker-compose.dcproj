<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" Sdk="Microsoft.Docker.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectVersion>2.1</ProjectVersion>
    <DockerTargetOS>Linux</DockerTargetOS>
    <ProjectGuid>************************************</ProjectGuid>
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerServiceUrl>{Scheme}://localhost:{ServicePort}</DockerServiceUrl>
    <DockerServiceName>esky.flightscache.robotsproducers</DockerServiceName>
	<DockerComposeProjectName>esky-flightscache-robots</DockerComposeProjectName> 
  </PropertyGroup>
  <ItemGroup>
    <None Include="docker-compose.ci.build.yml" />
    <None Include="docker-compose.yml" />
    <None Include=".dockerignore" />
    <None Include="rabbitmq.conf" />
  </ItemGroup>
</Project>