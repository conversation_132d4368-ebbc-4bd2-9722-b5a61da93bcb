@startuml Flights Cache Robots
!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

title System Context diagram for Robots
Enterprise_Boundary(b0, "ESKY") {
    Enterprise_Boundary(b1, "Flights cache") {
        System_Boundary(b2, "Robots") {
            System(Producer, "Robots Producer", "Produces messages for Consumers","")
            System(Consumer, "Robots Consumer", "Consumes Producer's messages, query for flights, send them")
            
            SystemDb(Database, "Robots' DB", "MongoDB")
            SystemDb(Queue, "Robots' Queue", "RabbitMq")
        }
        System_Ext(Timetables.API, "Timetables API", "esky.timetables.api")
        System_Ext(FlightsCache.API, "Flights Cache API", "esky.flightscache.api")
        SystemDb(FlightsCacheConfiguration, "FlightsCacheConfiguration", "MongoDB")
        
        SystemDb(WSQueue, "WorkerService' Queue", "RabbitMq")
    }

    System_Ext(FlightSearchIntegration, "Flight Providers", "esky.flightsintegration.api")
    System_Ext(IbeSearch, "IBE Flight Providers", "esky.flightsearch.api.providers/legacy")
    System_Ext(CurrencyService, "Currency Service", "esky.currencyservice.api")

    System_Ext(PartnerSettings, "Partner settings", "esky.partnersettings.api")
}
System_Ext(GoogleSheets, "Google Sheets", "")

Rel(Producer, Queue, "Publish messages for Consumer")
Rel(Queue, Consumer, "Consumes messages")

Rel(Producer, GoogleSheets, "Gets definitions of Cache Demand jobs")
Rel(Producer, Database, "Stores suppliers config and evenly distributed content")
Rel(Producer, Timetables.API, "Gets timetables")
Rel(Producer, FlightsCache.API, "Gets cached flights")
Rel(Producer, CurrencyService, "Checks airports currency")
Rel(Producer, PartnerSettings, "Gets partner's settings")

Rel(Consumer, CurrencyService, "Updates airports currency")
Rel(Consumer, FlightSearchIntegration, "Gets flights from providers")
Rel(Consumer, IbeSearch, "Gets flights from IBE")
Rel(Consumer, PartnerSettings, "Gets partner's settings")
Rel(Consumer, Queue, "Publish messages for WorkerService")
Rel(Consumer, FlightsCacheConfiguration, "Saves Ryanair route fees")

Rel(Queue, WSQueue, "<federated exchange>")

Rel(IBE, Queue, "Publish refresh cache request")

@enduml